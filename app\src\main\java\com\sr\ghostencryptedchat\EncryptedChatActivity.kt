package com.sr.ghostencryptedchat

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.google.firebase.FirebaseApp
import com.google.firebase.FirebaseOptions
import com.google.firebase.firestore.FirebaseFirestore
import com.sr.ghostencryptedchat.databinding.ActivityEncryptedChatBinding
import com.sr.ghostencryptedchat.ui.ChatsFragment
import com.sr.ghostencryptedchat.ui.ContactsFragment
import com.sr.ghostencryptedchat.ui.SettingsFragment
import com.sr.ghostencryptedchat.util.KeyStoreUtil

class EncryptedChatActivity : AppCompatActivity() {

    private lateinit var binding: ActivityEncryptedChatBinding
    private lateinit var db: FirebaseFirestore
    private lateinit var username: String

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (FirebaseApp.getApps(this).isEmpty()) {
            val options = FirebaseOptions.Builder()
                .setProjectId("ghostencryptedchat")
                .setApplicationId("1:530964170349:android:d3b193427e6a469261e164")
                .setApiKey("AIzaSyAHTxLQJXkpYEhQWHvKVBkqGNdj-CfSJ3Q")
                .build()
            FirebaseApp.initializeApp(this, options)
        }

        db = FirebaseFirestore.getInstance()
        binding = ActivityEncryptedChatBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // ✅ Use local persistent username only
        username = KeyStoreUtil.getOrCreateUsername(this)
        KeyStoreUtil.getOrCreateKeyPair(this)

        // ✅ No longer re-add or re-check username on Firestore

        // Load correct start tab
        if (savedInstanceState == null) {
            val defaultFragment = when (intent.getStringExtra("nav")) {
                "contacts" -> ContactsFragment()
                "settings" -> SettingsFragment()
                else -> ChatsFragment()
            }
            supportFragmentManager.beginTransaction()
                .replace(R.id.fragmentContainer, defaultFragment)
                .commit()
        }

        binding.bottomNav.setOnItemSelectedListener { item ->
            val fragment = when (item.itemId) {
                R.id.nav_chats -> ChatsFragment()
                R.id.nav_contacts -> ContactsFragment()
                R.id.nav_settings -> SettingsFragment()
                else -> null
            }
            fragment?.let {
                supportFragmentManager.beginTransaction()
                    .replace(R.id.fragmentContainer, it)
                    .commit()
                true
            } ?: false
        }
    }
}
