package com.sr.ghostencryptedchat

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.google.firebase.FirebaseApp
import com.google.firebase.FirebaseOptions
import com.google.firebase.firestore.FirebaseFirestore
import com.sr.ghostencryptedchat.databinding.ActivityEncryptedChatBinding
import com.sr.ghostencryptedchat.ui.ChatsFragment
import com.sr.ghostencryptedchat.ui.ContactsFragment
import com.sr.ghostencryptedchat.ui.SettingsFragment
import com.sr.ghostencryptedchat.ui.WalletFragment
import com.sr.ghostencryptedchat.util.KeyStoreUtil

class EncryptedChatActivity : AppCompatActivity() {

    private lateinit var binding: ActivityEncryptedChatBinding
    private lateinit var db: FirebaseFirestore
    private lateinit var username: String

    // Persistent fragments to avoid recreation
    private var chatsFragment: ChatsFragment? = null
    private var contactsFragment: ContactsFragment? = null
    private var walletFragment: WalletFragment? = null
    private var settingsFragment: SettingsFragment? = null
    private var currentFragment: Fragment? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (FirebaseApp.getApps(this).isEmpty()) {
            val options = FirebaseOptions.Builder()
                .setProjectId("ghostencryptedchat")
                .setApplicationId("1:530964170349:android:d3b193427e6a469261e164")
                .setApiKey("AIzaSyAHTxLQJXkpYEhQWHvKVBkqGNdj-CfSJ3Q")
                .build()
            FirebaseApp.initializeApp(this, options)
        }

        db = FirebaseFirestore.getInstance()
        binding = ActivityEncryptedChatBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // ✅ Use local persistent username only
        username = KeyStoreUtil.getOrCreateUsername(this)
        KeyStoreUtil.getOrCreateKeyPair(this)

        // ✅ No longer re-add or re-check username on Firestore

        // Load correct start tab
        if (savedInstanceState == null) {
            val startTab = intent.getStringExtra("nav") ?: "chats"
            showFragment(startTab)
        }

        binding.bottomNav.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.nav_chats -> {
                    showFragment("chats")
                    true
                }
                R.id.nav_contacts -> {
                    showFragment("contacts")
                    true
                }
                R.id.nav_wallet -> {
                    showFragment("wallet")
                    true
                }
                R.id.nav_settings -> {
                    showFragment("settings")
                    true
                }
                else -> false
            }
        }
    }

    private fun showFragment(tabName: String) {
        val transaction = supportFragmentManager.beginTransaction()

        // Hide current fragment if it exists
        currentFragment?.let { transaction.hide(it) }

        // Get or create the requested fragment
        val targetFragment = when (tabName) {
            "chats" -> {
                if (chatsFragment == null) {
                    chatsFragment = ChatsFragment()
                    transaction.add(R.id.fragmentContainer, chatsFragment!!, "chats")
                }
                chatsFragment!!
            }
            "contacts" -> {
                if (contactsFragment == null) {
                    contactsFragment = ContactsFragment()
                    transaction.add(R.id.fragmentContainer, contactsFragment!!, "contacts")
                }
                contactsFragment!!
            }
            "wallet" -> {
                if (walletFragment == null) {
                    walletFragment = WalletFragment()
                    transaction.add(R.id.fragmentContainer, walletFragment!!, "wallet")
                }
                walletFragment!!
            }
            "settings" -> {
                if (settingsFragment == null) {
                    settingsFragment = SettingsFragment()
                    transaction.add(R.id.fragmentContainer, settingsFragment!!, "settings")
                }
                settingsFragment!!
            }
            else -> {
                if (chatsFragment == null) {
                    chatsFragment = ChatsFragment()
                    transaction.add(R.id.fragmentContainer, chatsFragment!!, "chats")
                }
                chatsFragment!!
            }
        }

        // Show the target fragment
        transaction.show(targetFragment)
        transaction.commit()

        currentFragment = targetFragment
    }

    override fun onBackPressed() {
        // Handle back button for WebView in WalletFragment
        if (currentFragment is WalletFragment && (currentFragment as WalletFragment).canGoBack()) {
            (currentFragment as WalletFragment).goBack()
        } else {
            super.onBackPressed()
        }
    }
}
