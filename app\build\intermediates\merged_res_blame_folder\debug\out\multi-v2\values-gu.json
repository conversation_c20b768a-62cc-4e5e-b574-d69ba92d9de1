{"logs": [{"outputFile": "com.sr.ghostencryptedchat.app-mergeDebugResources-41:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\f2dc38ad7cc09bc05989a176e11ace02\\transformed\\appcompat-1.6.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "940,1047,1151,1258,1345,1445,1565,1643,1720,1811,1904,1999,2093,2193,2286,2381,2475,2566,2657,2737,2843,2944,3041,3150,3250,3360,3520,16115", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "1042,1146,1253,1340,1440,1560,1638,1715,1806,1899,1994,2088,2188,2281,2376,2470,2561,2652,2732,2838,2939,3036,3145,3245,3355,3515,3618,16191"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\6142c6f2f2c5da410d9cf340d166ee81\\transformed\\play-services-base-18.0.1\\res\\values-gu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,459,580,688,822,940,1047,1143,1287,1391,1551,1672,1811,1957,2014,2076", "endColumns": "103,161,120,107,133,117,106,95,143,103,159,120,138,145,56,61,77", "endOffsets": "296,458,579,687,821,939,1046,1142,1286,1390,1550,1671,1810,1956,2013,2075,2153"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5076,5184,5350,5475,5587,5725,5847,5958,6205,6353,6461,6625,6750,6893,7043,7104,7170", "endColumns": "107,165,124,111,137,121,110,99,147,107,163,124,142,149,60,65,81", "endOffsets": "5179,5345,5470,5582,5720,5842,5953,6053,6348,6456,6620,6745,6888,7038,7099,7165,7247"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\1936637f2b78fb49128b3efd39ec3928\\transformed\\material-1.10.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,342,414,496,602,700,799,919,1003,1066,1157,1224,1283,1373,1436,1501,1565,1634,1696,1750,1865,1923,1984,2038,2111,2238,2324,2408,2541,2616,2692,2825,2911,2992,3046,3098,3164,3237,3317,3402,3482,3553,3629,3708,3777,3884,3980,4058,4153,4249,4323,4398,4497,4548,4630,4697,4784,4874,4936,5000,5063,5130,5232,5337,5434,5536,5594,5650", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,71,81,105,97,98,119,83,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,132,85,80,53,51,65,72,79,84,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77", "endOffsets": "263,337,409,491,597,695,794,914,998,1061,1152,1219,1278,1368,1431,1496,1560,1629,1691,1745,1860,1918,1979,2033,2106,2233,2319,2403,2536,2611,2687,2820,2906,2987,3041,3093,3159,3232,3312,3397,3477,3548,3624,3703,3772,3879,3975,4053,4148,4244,4318,4393,4492,4543,4625,4692,4779,4869,4931,4995,5058,5125,5227,5332,5429,5531,5589,5645,5723"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,84,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "772,3623,3697,3769,3851,3957,4773,4872,4992,7324,11230,11321,11388,11447,11537,11600,11665,11729,11798,11860,11914,12029,12087,12148,12202,12275,12402,12488,12572,12705,12780,12856,12989,13075,13156,13210,13262,13328,13401,13481,13566,13646,13717,13793,13872,13941,14048,14144,14222,14317,14413,14487,14562,14661,14712,14794,14861,14948,15038,15100,15164,15227,15294,15396,15501,15598,15700,15758,15895", "endLines": "22,50,51,52,53,54,62,63,64,84,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,196", "endColumns": "12,73,71,81,105,97,98,119,83,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,132,85,80,53,51,65,72,79,84,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77", "endOffsets": "935,3692,3764,3846,3952,4050,4867,4987,5071,7382,11316,11383,11442,11532,11595,11660,11724,11793,11855,11909,12024,12082,12143,12197,12270,12397,12483,12567,12700,12775,12851,12984,13070,13151,13205,13257,13323,13396,13476,13561,13641,13712,13788,13867,13936,14043,14139,14217,14312,14408,14482,14557,14656,14707,14789,14856,14943,15033,15095,15159,15222,15289,15391,15496,15593,15695,15753,15809,15968"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\1100a1eb0e13467cdf6e3e9fa04c3895\\transformed\\core-1.10.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "55,56,57,58,59,60,61,199", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4055,4149,4252,4349,4451,4553,4651,16196", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "4144,4247,4344,4446,4548,4646,4768,16292"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\d858642a26bc843a8ca821fcaa92e1a0\\transformed\\preference-1.2.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,263,344,486,655,739", "endColumns": "71,85,80,141,168,83,81", "endOffsets": "172,258,339,481,650,734,816"}, "to": {"startLines": "83,136,195,197,200,201,202", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7252,11144,15814,15973,16297,16466,16550", "endColumns": "71,85,80,141,168,83,81", "endOffsets": "7319,11225,15890,16110,16461,16545,16627"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\573c237f13283bc1d5f145ebb8306e38\\transformed\\media3-exoplayer-1.2.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,266,334,409,477,576,672", "endColumns": "69,64,75,67,74,67,98,95,78", "endOffsets": "120,185,261,329,404,472,571,667,746"}, "to": {"startLines": "109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9326,9396,9461,9537,9605,9680,9748,9847,9943", "endColumns": "69,64,75,67,74,67,98,95,78", "endOffsets": "9391,9456,9532,9600,9675,9743,9842,9938,10017"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\20819e21ff7c6943837ea2e5c7b675ed\\transformed\\media3-ui-1.2.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,489,677,759,839,922,1021,1123,1200,1262,1351,1439,1503,1567,1627,1694,1807,1921,2032,2105,2183,2252,2328,2410,2490,2553,2616,2669,2727,2775,2836,2898,2960,3025,3087,3154,3217,3283,3350,3417,3470,3532,3608,3684", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,82,98,101,76,61,88,87,63,63,59,66,112,113,110,72,77,68,75,81,79,62,62,52,57,47,60,61,61,64,61,66,62,65,66,66,52,61,75,75,53", "endOffsets": "281,484,672,754,834,917,1016,1118,1195,1257,1346,1434,1498,1562,1622,1689,1802,1916,2027,2100,2178,2247,2323,2405,2485,2548,2611,2664,2722,2770,2831,2893,2955,3020,3082,3149,3212,3278,3345,3412,3465,3527,3603,3679,3733"}, "to": {"startLines": "2,11,15,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,584,7387,7469,7549,7632,7731,7833,7910,7972,8061,8149,8213,8277,8337,8404,8517,8631,8742,8815,8893,8962,9038,9120,9200,9263,10022,10075,10133,10181,10242,10304,10366,10431,10493,10560,10623,10689,10756,10823,10876,10938,11014,11090", "endLines": "10,14,18,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "endColumns": "17,12,12,81,79,82,98,101,76,61,88,87,63,63,59,66,112,113,110,72,77,68,75,81,79,62,62,52,57,47,60,61,61,64,61,66,62,65,66,66,52,61,75,75,53", "endOffsets": "376,579,767,7464,7544,7627,7726,7828,7905,7967,8056,8144,8208,8272,8332,8399,8512,8626,8737,8810,8888,8957,9033,9115,9195,9258,9321,10070,10128,10176,10237,10299,10361,10426,10488,10555,10618,10684,10751,10818,10871,10933,11009,11085,11139"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\ce7eb9a863475fa63014d727748770e8\\transformed\\play-services-basement-18.1.0\\res\\values-gu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6058", "endColumns": "146", "endOffsets": "6200"}}]}]}