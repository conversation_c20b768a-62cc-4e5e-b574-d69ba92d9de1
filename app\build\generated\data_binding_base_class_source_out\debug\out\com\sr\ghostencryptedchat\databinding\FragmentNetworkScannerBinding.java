// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentNetworkScannerBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final RecyclerView devicesRecyclerView;

  @NonNull
  public final TextView networkInfo;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Button scanButton;

  private FragmentNetworkScannerBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton backButton, @NonNull RecyclerView devicesRecyclerView,
      @NonNull TextView networkInfo, @NonNull ProgressBar progressBar, @NonNull Button scanButton) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.devicesRecyclerView = devicesRecyclerView;
    this.networkInfo = networkInfo;
    this.progressBar = progressBar;
    this.scanButton = scanButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentNetworkScannerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentNetworkScannerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_network_scanner, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentNetworkScannerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backButton;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.devicesRecyclerView;
      RecyclerView devicesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (devicesRecyclerView == null) {
        break missingId;
      }

      id = R.id.networkInfo;
      TextView networkInfo = ViewBindings.findChildViewById(rootView, id);
      if (networkInfo == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.scanButton;
      Button scanButton = ViewBindings.findChildViewById(rootView, id);
      if (scanButton == null) {
        break missingId;
      }

      return new FragmentNetworkScannerBinding((LinearLayout) rootView, backButton,
          devicesRecyclerView, networkInfo, progressBar, scanButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
