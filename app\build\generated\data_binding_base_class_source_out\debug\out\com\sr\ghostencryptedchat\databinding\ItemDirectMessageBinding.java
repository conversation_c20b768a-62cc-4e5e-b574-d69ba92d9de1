// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDirectMessageBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView lastMessage;

  @NonNull
  public final View newIndicator;

  @NonNull
  public final TextView recipientName;

  private ItemDirectMessageBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView lastMessage, @NonNull View newIndicator, @NonNull TextView recipientName) {
    this.rootView = rootView;
    this.lastMessage = lastMessage;
    this.newIndicator = newIndicator;
    this.recipientName = recipientName;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDirectMessageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDirectMessageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_direct_message, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDirectMessageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.lastMessage;
      TextView lastMessage = ViewBindings.findChildViewById(rootView, id);
      if (lastMessage == null) {
        break missingId;
      }

      id = R.id.newIndicator;
      View newIndicator = ViewBindings.findChildViewById(rootView, id);
      if (newIndicator == null) {
        break missingId;
      }

      id = R.id.recipientName;
      TextView recipientName = ViewBindings.findChildViewById(rootView, id);
      if (recipientName == null) {
        break missingId;
      }

      return new ItemDirectMessageBinding((ConstraintLayout) rootView, lastMessage, newIndicator,
          recipientName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
