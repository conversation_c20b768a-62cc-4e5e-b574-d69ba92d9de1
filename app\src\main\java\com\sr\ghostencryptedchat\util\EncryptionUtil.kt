package com.sr.ghostencryptedchat.util

import android.util.Base64
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

object EncryptionUtil {
    private val key = "my-strong-key123".toByteArray() // Used for public chat only

    fun encrypt(data: String): String {
        val cipher = Cipher.getInstance("AES")
        val secretKey = SecretKeySpec(key, "AES")
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)
        return Base64.encodeToString(cipher.doFinal(data.toByteArray()), Base64.DEFAULT)
    }

    fun decrypt(data: String): String {
        return try {
            val cipher = Cipher.getInstance("AES")
            val secretKey = SecretKeySpec(key, "AES")
            cipher.init(Cipher.DECRYPT_MODE, secretKey)
            String(cipher.doFinal(Base64.decode(data, Base64.DEFAULT)))
        } catch (e: Exception) {
            "[error]"
        }
    }

    // 🔐 DM-specific dynamic encryption
    fun encryptWithKey(plaintext: String, secretKey: SecretKeySpec): String {
        val cipher = Cipher.getInstance("AES")
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)
        return Base64.encodeToString(cipher.doFinal(plaintext.toByteArray()), Base64.NO_WRAP)
    }

    fun decryptWithKey(ciphertext: String, secretKey: SecretKeySpec): String {
        return try {
            val cipher = Cipher.getInstance("AES")
            cipher.init(Cipher.DECRYPT_MODE, secretKey)
            String(cipher.doFinal(Base64.decode(ciphertext, Base64.NO_WRAP)))
        } catch (e: Exception) {
            "[decryption error]"
        }
    }
}
