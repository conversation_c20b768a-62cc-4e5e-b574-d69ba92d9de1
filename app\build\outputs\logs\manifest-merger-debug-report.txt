-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:23:9-31:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:27:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:25:13-60
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:26:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:24:13-62
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:1:1-57:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:1:1-57:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:1:1-57:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:1:1-57:12
MERGED from [androidx.databinding:viewbinding:8.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\abf11125710f5fb236f5e41f516daab2\transformed\viewbinding-8.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1936637f2b78fb49128b3efd39ec3928\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\9bf1d69a4f2855ab4eaf5dc25cd2313f\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\5c640d08c7fc29c8d79fc4c9159ce3c6\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6b2b1763b94b214a28d6acdeffee58e0\transformed\preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\d858642a26bc843a8ca821fcaa92e1a0\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f2dc38ad7cc09bc05989a176e11ace02\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\d23f5c8b8f40ce50578d3e87ef54bd30\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\eff03cc53257200e8c3dcfd922122a86\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\c74c07e07643abc90a400c46b1aa5e70\transformed\fragment-ktx-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\069f94fc6847012b60c4542900b70cfd\transformed\lifecycle-runtime-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\5b01bad642b38f63b96802e0b70fc049\transformed\lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\61327d3e6979f4737b3bda52a9e84f4c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\348baa9f847794bae76a4fe65eb48e05\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\c40c7d7482a9ddcbef649277c125a60c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\4a7ccb130b2d53875c0a3a460337ba67\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ffff699af584157ef014e9fa478e21d8\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1468990c181f20a121382559d5061f44\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\3654e8d26bb74d8686ffe92ad0164820\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\e3a36c802e045df888290c771b9eb58f\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\38d29c27a7c8f1de2249e89356da7579\transformed\firebase-auth-interop-19.0.2\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7590a545309479e6c8bdb0324f23beec\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\878eb17854bf1d32a7c55175d3665965\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\cb0f2e1c5fa0814b8229f949fc2a5ee8\transformed\activity-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.6.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\9dca56c8203e6c145515337013efb3de\transformed\activity-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\94e3ac7693103d73702b56b67aca1426\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\db23de3102ef5b0aacf59cb2ff8ef0b8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\3d5538849c353bc484a931ee0c5dfcb4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\bb975e21b923ac558dd71796f9e4fb54\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\dee344560d902945139c2476700c560f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8f5b68c609425dd139561f73b5eed331\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\583aaa122ea6893b7ef8488259be243b\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\d53ac869d6d8cf4247d96260bc4f8af8\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\52a935d498e6c703f1080a15d7b7d02d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\412f5e5b9e7b33441491c008cfcf8403\transformed\core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a813e572b8d01d5b93f4b7b7da93ed56\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\4c8c464530416b8e6bf74f92c099de05\transformed\savedstate-ktx-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\27541394121a00a93e1c2cc52bad0088\transformed\savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\43f26f6a6bc5c7565293a77f80dcf283\transformed\lifecycle-livedata-core-ktx-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6305c110e89274509bd3e34a1d2f8f79\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\194f0f8f87f7c370815cc9767701c0f1\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8af3297a194b4b40b2604f3e1b7718cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8cc691eac00e283526c549674efe5485\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\963e8db15f792261bf42c108250682b5\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6c50f4648c47fc78888d2497ebad93b6\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\06d1d15885133fc702cba9358786524d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\db590044eb57e8ba7154ca70dc47ef5d\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\48cfcf0dc6ed58a7259bf2c1f5f0e085\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\83af0ce7f5829fe527f39022b2db2e6e\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\da975ad8b95dd83392dc827e04f5ff2b\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\040a1e6eab0f7cd5921e7e0a9bd36761\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\5613a045021af393e351815700efe8d8\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\727e3f732f83c441f8fcc5d7dc5ac669\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\25257fa29b7e66eaa245ec50609e2b0a\transformed\grpc-android-1.52.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\2d329962fd47df6b99ecc6c97f378103\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:4:5-67
MERGED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:11:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:5:5-79
MERGED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:10:5-79
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\25257fa29b7e66eaa245ec50609e2b0a\transformed\grpc-android-1.52.1\AndroidManifest.xml:9:5-79
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\25257fa29b7e66eaa245ec50609e2b0a\transformed\grpc-android-1.52.1\AndroidManifest.xml:9:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:5:22-76
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:6:5-83
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:6:22-80
uses-permission#android.permission.DOWNLOAD_WITHOUT_NOTIFICATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:7:5-88
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:7:22-85
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:8:22-73
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:10:5-56:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:10:5-56:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1936637f2b78fb49128b3efd39ec3928\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1936637f2b78fb49128b3efd39ec3928\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\5c640d08c7fc29c8d79fc4c9159ce3c6\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\5c640d08c7fc29c8d79fc4c9159ce3c6\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\3654e8d26bb74d8686ffe92ad0164820\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\3654e8d26bb74d8686ffe92ad0164820\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\38d29c27a7c8f1de2249e89356da7579\transformed\firebase-auth-interop-19.0.2\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\38d29c27a7c8f1de2249e89356da7579\transformed\firebase-auth-interop-19.0.2\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7590a545309479e6c8bdb0324f23beec\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7590a545309479e6c8bdb0324f23beec\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8af3297a194b4b40b2604f3e1b7718cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8af3297a194b4b40b2604f3e1b7718cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\06d1d15885133fc702cba9358786524d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\06d1d15885133fc702cba9358786524d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:17:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:15:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:13:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:16:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:20:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:14:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:11:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:18:9-56
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:19:9-69
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:12:9-65
activity#com.sr.ghostencryptedchat.SplashActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:34:9-41:20
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:36:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:35:13-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:37:13-40:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:38:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:38:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:39:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:39:27-74
activity#com.sr.ghostencryptedchat.EncryptedChatActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:44:9-59
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:44:19-56
activity#com.sr.ghostencryptedchat.DirectMessageActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:45:9-83
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:45:57-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:45:19-56
activity#com.sr.ghostencryptedchat.GroupChatActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:46:9-55
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:46:19-52
activity#com.sr.ghostencryptedchat.DMRequestActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:47:9-79
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:47:53-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:47:19-52
activity#com.sr.ghostencryptedchat.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:48:9-50
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:48:19-47
activity#com.sr.ghostencryptedchat.ContactsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:49:9-78
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:49:52-75
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:49:19-51
activity#com.sr.ghostencryptedchat.SettingsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:50:9-54
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:50:19-51
activity#com.sr.ghostencryptedchat.UsernameSetupActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:51:9-59
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:51:19-56
activity#com.sr.ghostencryptedchat.DMChatActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:52:9-54:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:54:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:53:13-43
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:28:13-30:54
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:30:17-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:29:17-67
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\abf11125710f5fb236f5e41f516daab2\transformed\viewbinding-8.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\abf11125710f5fb236f5e41f516daab2\transformed\viewbinding-8.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1936637f2b78fb49128b3efd39ec3928\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1936637f2b78fb49128b3efd39ec3928\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\9bf1d69a4f2855ab4eaf5dc25cd2313f\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\9bf1d69a4f2855ab4eaf5dc25cd2313f\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\5c640d08c7fc29c8d79fc4c9159ce3c6\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\5c640d08c7fc29c8d79fc4c9159ce3c6\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6b2b1763b94b214a28d6acdeffee58e0\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6b2b1763b94b214a28d6acdeffee58e0\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\d858642a26bc843a8ca821fcaa92e1a0\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\d858642a26bc843a8ca821fcaa92e1a0\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f2dc38ad7cc09bc05989a176e11ace02\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f2dc38ad7cc09bc05989a176e11ace02\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\d23f5c8b8f40ce50578d3e87ef54bd30\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\d23f5c8b8f40ce50578d3e87ef54bd30\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\eff03cc53257200e8c3dcfd922122a86\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\eff03cc53257200e8c3dcfd922122a86\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\c74c07e07643abc90a400c46b1aa5e70\transformed\fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\c74c07e07643abc90a400c46b1aa5e70\transformed\fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\069f94fc6847012b60c4542900b70cfd\transformed\lifecycle-runtime-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\069f94fc6847012b60c4542900b70cfd\transformed\lifecycle-runtime-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\5b01bad642b38f63b96802e0b70fc049\transformed\lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\5b01bad642b38f63b96802e0b70fc049\transformed\lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\61327d3e6979f4737b3bda52a9e84f4c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\61327d3e6979f4737b3bda52a9e84f4c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\348baa9f847794bae76a4fe65eb48e05\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\348baa9f847794bae76a4fe65eb48e05\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\c40c7d7482a9ddcbef649277c125a60c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\c40c7d7482a9ddcbef649277c125a60c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\4a7ccb130b2d53875c0a3a460337ba67\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\4a7ccb130b2d53875c0a3a460337ba67\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ffff699af584157ef014e9fa478e21d8\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ffff699af584157ef014e9fa478e21d8\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1468990c181f20a121382559d5061f44\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1468990c181f20a121382559d5061f44\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\3654e8d26bb74d8686ffe92ad0164820\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\3654e8d26bb74d8686ffe92ad0164820\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\e3a36c802e045df888290c771b9eb58f\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\e3a36c802e045df888290c771b9eb58f\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\38d29c27a7c8f1de2249e89356da7579\transformed\firebase-auth-interop-19.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\38d29c27a7c8f1de2249e89356da7579\transformed\firebase-auth-interop-19.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7590a545309479e6c8bdb0324f23beec\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7590a545309479e6c8bdb0324f23beec\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\878eb17854bf1d32a7c55175d3665965\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\878eb17854bf1d32a7c55175d3665965\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\cb0f2e1c5fa0814b8229f949fc2a5ee8\transformed\activity-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\cb0f2e1c5fa0814b8229f949fc2a5ee8\transformed\activity-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.6.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\9dca56c8203e6c145515337013efb3de\transformed\activity-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.6.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\9dca56c8203e6c145515337013efb3de\transformed\activity-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\94e3ac7693103d73702b56b67aca1426\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\94e3ac7693103d73702b56b67aca1426\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\db23de3102ef5b0aacf59cb2ff8ef0b8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\db23de3102ef5b0aacf59cb2ff8ef0b8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\3d5538849c353bc484a931ee0c5dfcb4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\3d5538849c353bc484a931ee0c5dfcb4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\bb975e21b923ac558dd71796f9e4fb54\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\bb975e21b923ac558dd71796f9e4fb54\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\dee344560d902945139c2476700c560f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\dee344560d902945139c2476700c560f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8f5b68c609425dd139561f73b5eed331\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8f5b68c609425dd139561f73b5eed331\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\583aaa122ea6893b7ef8488259be243b\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\583aaa122ea6893b7ef8488259be243b\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\d53ac869d6d8cf4247d96260bc4f8af8\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\d53ac869d6d8cf4247d96260bc4f8af8\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\52a935d498e6c703f1080a15d7b7d02d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\52a935d498e6c703f1080a15d7b7d02d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\412f5e5b9e7b33441491c008cfcf8403\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\412f5e5b9e7b33441491c008cfcf8403\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a813e572b8d01d5b93f4b7b7da93ed56\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a813e572b8d01d5b93f4b7b7da93ed56\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\4c8c464530416b8e6bf74f92c099de05\transformed\savedstate-ktx-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\4c8c464530416b8e6bf74f92c099de05\transformed\savedstate-ktx-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\27541394121a00a93e1c2cc52bad0088\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\27541394121a00a93e1c2cc52bad0088\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\43f26f6a6bc5c7565293a77f80dcf283\transformed\lifecycle-livedata-core-ktx-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\43f26f6a6bc5c7565293a77f80dcf283\transformed\lifecycle-livedata-core-ktx-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6305c110e89274509bd3e34a1d2f8f79\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6305c110e89274509bd3e34a1d2f8f79\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\194f0f8f87f7c370815cc9767701c0f1\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\194f0f8f87f7c370815cc9767701c0f1\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8af3297a194b4b40b2604f3e1b7718cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8af3297a194b4b40b2604f3e1b7718cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8cc691eac00e283526c549674efe5485\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8cc691eac00e283526c549674efe5485\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\963e8db15f792261bf42c108250682b5\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\963e8db15f792261bf42c108250682b5\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6c50f4648c47fc78888d2497ebad93b6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6c50f4648c47fc78888d2497ebad93b6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\06d1d15885133fc702cba9358786524d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\06d1d15885133fc702cba9358786524d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\db590044eb57e8ba7154ca70dc47ef5d\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\db590044eb57e8ba7154ca70dc47ef5d\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\48cfcf0dc6ed58a7259bf2c1f5f0e085\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\48cfcf0dc6ed58a7259bf2c1f5f0e085\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\83af0ce7f5829fe527f39022b2db2e6e\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\83af0ce7f5829fe527f39022b2db2e6e\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\da975ad8b95dd83392dc827e04f5ff2b\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\da975ad8b95dd83392dc827e04f5ff2b\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\040a1e6eab0f7cd5921e7e0a9bd36761\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\040a1e6eab0f7cd5921e7e0a9bd36761\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\5613a045021af393e351815700efe8d8\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\5613a045021af393e351815700efe8d8\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\727e3f732f83c441f8fcc5d7dc5ac669\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\727e3f732f83c441f8fcc5d7dc5ac669\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\25257fa29b7e66eaa245ec50609e2b0a\transformed\grpc-android-1.52.1\AndroidManifest.xml:5:5-7:41
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\25257fa29b7e66eaa245ec50609e2b0a\transformed\grpc-android-1.52.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\2d329962fd47df6b99ecc6c97f378103\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\2d329962fd47df6b99ecc6c97f378103\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:10:13-84
meta-data#com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar
ADDED from [com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8af3297a194b4b40b2604f3e1b7718cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8af3297a194b4b40b2604f3e1b7718cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.sr.ghostencryptedchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.sr.ghostencryptedchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
