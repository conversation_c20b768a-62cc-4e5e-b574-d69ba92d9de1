<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 3D background -->
    <com.sr.ghostencryptedchat.view.ThreeDBackgroundView
        android:id="@+id/backgroundView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- Main content -->
    <LinearLayout
        android:id="@+id/chatLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="#88121212"> <!-- More transparent background -->

        <TextView
            android:id="@+id/chatTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Chats"
            android:textColor="#FFFFFF"
            android:textSize="20sp"
            android:textStyle="bold"
            android:paddingBottom="8dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/chatListRecycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="ifContentScrolls"
            android:clipToPadding="false"
            android:paddingBottom="8dp" />
    </LinearLayout>
</FrameLayout>





