plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
}

android {
    namespace = "com.sr.ghostencryptedchat"
    compileSdk = 33

    defaultConfig {
        applicationId = "com.sr.ghostencryptedchat"
        minSdk = 26
        targetSdk = 33
        versionCode = 1
        versionName = "1.0"
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    buildFeatures {
        viewBinding = true
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }
    dependenciesInfo {
        includeInApk = true
        includeInBundle = true
    }
}

kotlin {
    jvmToolchain(17)
}

dependencies {
    implementation("androidx.appcompat:appcompat:1.6.1") // Unchanged, compatible
    implementation("androidx.activity:activity-ktx:1.6.1") // Downgrade to 1.6.1
    implementation("androidx.activity:activity:1.6.1") // Downgrade to 1.6.1
    implementation("androidx.recyclerview:recyclerview:1.3.2") // Unchanged, compatible
    implementation("com.google.firebase:firebase-firestore-ktx:24.9.0") // Downgrade from 24.10.0
    implementation("com.google.android.material:material:1.10.0") // Downgrade from 1.11.0
    implementation("androidx.preference:preference-ktx:1.2.1") // Unchanged
    implementation("com.google.code.gson:gson:2.10.1") // Unchanged
    implementation("androidx.core:core-ktx:1.10.1") // Unchanged, compatible
    implementation("androidx.core:core:1.10.1") // Unchanged, compatible
    implementation("androidx.annotation:annotation-experimental:1.3.1") // Unchanged, compatible

    // Coroutines for async operations
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")

    // ExoPlayer for flawless video looping (compatible with API 33)
    implementation("com.google.android.exoplayer:exoplayer:2.19.1")
    implementation("com.google.android.exoplayer:exoplayer-ui:2.19.1")

    // Test dependencies (unchanged, compatible)
    androidTestImplementation("androidx.test.ext:junit:1.2.1")
    androidTestImplementation("androidx.test:runner:1.6.2")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.6.1")
    testImplementation("junit:junit:4.13.2")
}

configurations.all {
    resolutionStrategy {
        force("androidx.activity:activity:1.6.1")
        force("androidx.activity:activity-ktx:1.6.1")
    }
}