<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_password_generator" modulePackage="com.sr.ghostencryptedchat" filePath="app\src\main\res\layout\fragment_password_generator.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_password_generator_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="247" endOffset="12"/></Target><Target id="@+id/backButton" view="ImageButton"><Expressions/><location startLine="22" startOffset="12" endLine="29" endOffset="51"/></Target><Target id="@+id/generatedPassword" view="TextView"><Expressions/><location startLine="67" startOffset="16" endLine="78" endOffset="56"/></Target><Target id="@+id/strengthIndicator" view="TextView"><Expressions/><location startLine="93" startOffset="20" endLine="101" endOffset="65"/></Target><Target id="@+id/copyButton" view="Button"><Expressions/><location startLine="103" startOffset="20" endLine="110" endOffset="73"/></Target><Target id="@+id/lengthValue" view="TextView"><Expressions/><location startLine="157" startOffset="20" endLine="165" endOffset="49"/></Target><Target id="@+id/lengthSlider" view="com.google.android.material.slider.Slider"><Expressions/><location startLine="167" startOffset="20" endLine="179" endOffset="71"/></Target><Target id="@+id/includeUppercase" view="CheckBox"><Expressions/><location startLine="184" startOffset="16" endLine="191" endOffset="55"/></Target><Target id="@+id/includeLowercase" view="CheckBox"><Expressions/><location startLine="193" startOffset="16" endLine="200" endOffset="55"/></Target><Target id="@+id/includeNumbers" view="CheckBox"><Expressions/><location startLine="202" startOffset="16" endLine="209" endOffset="55"/></Target><Target id="@+id/includeSymbols" view="CheckBox"><Expressions/><location startLine="211" startOffset="16" endLine="218" endOffset="55"/></Target><Target id="@+id/excludeSimilar" view="CheckBox"><Expressions/><location startLine="220" startOffset="16" endLine="227" endOffset="56"/></Target><Target id="@+id/generateButton" view="Button"><Expressions/><location startLine="234" startOffset="8" endLine="243" endOffset="61"/></Target></Targets></Layout>