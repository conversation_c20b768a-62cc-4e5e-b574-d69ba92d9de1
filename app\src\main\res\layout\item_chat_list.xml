<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="8dp"
    android:layout_marginHorizontal="16dp"
    app:cardCornerRadius="10dp"
    app:cardElevation="0dp"
    app:cardBackgroundColor="@android:color/transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:orientation="horizontal"
        android:background="@drawable/bg_chat_item"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/chatIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="12dp"
            android:tint="#00FF00"
            android:src="@drawable/ic_globe_white" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/chatName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Chat Name"
                android:textSize="16sp"
                android:textColor="#FFFFFF"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/chatNewLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="NEW"
                android:textSize="12sp"
                android:textColor="#00FF00"
                android:textStyle="bold"
                android:visibility="gone"
                android:paddingTop="2dp" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
