<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_network_device" modulePackage="com.sr.ghostencryptedchat" filePath="app\src\main\res\layout\item_network_device.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_network_device_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="85" endOffset="14"/></Target><Target id="@+id/deviceIcon" view="TextView"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="33"/></Target><Target id="@+id/deviceIp" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="43" endOffset="48"/></Target><Target id="@+id/deviceStatus" view="TextView"><Expressions/><location startLine="45" startOffset="12" endLine="52" endOffset="57"/></Target><Target id="@+id/deviceHostname" view="TextView"><Expressions/><location startLine="63" startOffset="12" endLine="70" endOffset="59"/></Target><Target id="@+id/responseTime" view="TextView"><Expressions/><location startLine="72" startOffset="12" endLine="79" endOffset="48"/></Target></Targets></Layout>