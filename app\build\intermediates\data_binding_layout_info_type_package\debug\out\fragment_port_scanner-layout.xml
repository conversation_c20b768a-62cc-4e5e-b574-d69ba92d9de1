<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_port_scanner" modulePackage="com.sr.ghostencryptedchat" filePath="app\src\main\res\layout\fragment_port_scanner.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_port_scanner_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="224" endOffset="12"/></Target><Target id="@+id/backButton" view="ImageButton"><Expressions/><location startLine="22" startOffset="12" endLine="29" endOffset="51"/></Target><Target id="@+id/targetInput" view="EditText"><Expressions/><location startLine="67" startOffset="16" endLine="77" endOffset="49"/></Target><Target id="@+id/scanCommonButton" view="Button"><Expressions/><location startLine="108" startOffset="16" endLine="118" endOffset="69"/></Target><Target id="@+id/customPortsInput" view="EditText"><Expressions/><location startLine="129" startOffset="16" endLine="140" endOffset="56"/></Target><Target id="@+id/scanCustomButton" view="Button"><Expressions/><location startLine="142" startOffset="16" endLine="151" endOffset="84"/></Target><Target id="@+id/scanStatus" view="TextView"><Expressions/><location startLine="165" startOffset="12" endLine="172" endOffset="59"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="174" startOffset="12" endLine="179" endOffset="43"/></Target><Target id="@+id/resultsRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="208" startOffset="16" endLine="216" endOffset="90"/></Target></Targets></Layout>