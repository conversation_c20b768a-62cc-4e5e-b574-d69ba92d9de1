package com.sr.ghostencryptedchat

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.SetOptions
import com.sr.ghostencryptedchat.adapter.DMAdapter
import com.sr.ghostencryptedchat.databinding.ActivityDmChatBinding
import com.sr.ghostencryptedchat.model.ChatMessage
import com.sr.ghostencryptedchat.util.EncryptionUtil
import com.sr.ghostencryptedchat.util.KeyStoreUtil

class DMChatActivity : AppCompatActivity() {

    private lateinit var binding: ActivityDmChatBinding
    private lateinit var db: FirebaseFirestore
    private lateinit var currentUser: String
    private lateinit var recipient: String
    private val messages = mutableListOf<ChatMessage>()
    private lateinit var adapter: DMAdapter
    private lateinit var chatId: String

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        try {
            binding = ActivityDmChatBinding.inflate(layoutInflater)
            setContentView(binding.root)

            db = FirebaseFirestore.getInstance()
            currentUser = KeyStoreUtil.getOrCreateUsername(this)

            val incomingRecipient = intent.getStringExtra("recipient")
            Log.d("DMChatActivity", "Recipient: $incomingRecipient")
            
            if (incomingRecipient.isNullOrBlank()) {
                Toast.makeText(this, "No recipient provided", Toast.LENGTH_SHORT).show()
                finish()
                return
            }
            recipient = incomingRecipient
            chatId = listOf(currentUser, recipient).sorted().joinToString("_")

            binding.chatTitle.text = "DM with $recipient"

            // Initialize the RecyclerView with a fresh adapter
            messages.clear()
            adapter = DMAdapter(messages, currentUser)
            
            // Make sure we're using a LinearLayoutManager
            val layoutManager = LinearLayoutManager(this)
            layoutManager.stackFromEnd = true // Messages appear from bottom
            binding.dmRecyclerView.layoutManager = layoutManager
            binding.dmRecyclerView.adapter = adapter
            
            // Add these lines to ensure the RecyclerView is visible and working
            binding.dmRecyclerView.setHasFixedSize(false)
            binding.dmRecyclerView.visibility = View.VISIBLE
            
            // Log the RecyclerView state
            Log.d("DMChatActivity", "RecyclerView setup: adapter=${adapter}, visibility=${binding.dmRecyclerView.visibility}")

            listenForMessages()
            markChatAsRead()

            binding.sendDMButton.isEnabled = false
            binding.dmMessageInput.addTextChangedListener(object : TextWatcher {
                override fun afterTextChanged(s: Editable?) {
                    binding.sendDMButton.isEnabled = !s.isNullOrBlank()
                }

                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            })

            binding.sendDMButton.setOnClickListener {
                val plaintext = binding.dmMessageInput.text.toString()
                if (plaintext.isNotBlank()) {
                    val encrypted = EncryptionUtil.encrypt(plaintext)
                    val now = System.currentTimeMillis()
                    val chatMessage = ChatMessage(
                        sender = currentUser,
                        message = encrypted,
                        timestamp = now
                    )

                    db.collection("dm_chats")
                        .document(chatId)
                        .collection("messages")
                        .add(chatMessage)

                    db.collection("dm_chats")
                        .document(chatId)
                        .set(
                            mapOf(
                                "participants" to listOf(currentUser, recipient),
                                "status" to "accepted",
                                "lastSender" to currentUser,
                                "lastMessageTime" to now,
                                "lastRead.$currentUser" to now
                            ),
                            SetOptions.merge()
                        )

                    binding.dmMessageInput.setText("")
                    val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                    imm.hideSoftInputFromWindow(binding.dmMessageInput.windowToken, 0)
                    
                    // Scroll to bottom after sending a message
                    scrollToBottom()
                }
            }

            // ✅ "Add to Contacts" button
            binding.addToContactsButton.setOnClickListener {
                showAddToContactsDialog()
            }

            // ✅ Bottom navigation
            binding.bottomNav.setOnItemSelectedListener { item ->
                when (item.itemId) {
                    R.id.nav_chats -> {
                        val intent = Intent(this, EncryptedChatActivity::class.java)
                        intent.putExtra("nav", "chats")
                        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                        startActivity(intent)
                        finish()
                        true
                    }
                    R.id.nav_contacts -> {
                        val intent = Intent(this, EncryptedChatActivity::class.java)
                        intent.putExtra("nav", "contacts")
                        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                        startActivity(intent)
                        finish()
                        true
                    }
                    R.id.nav_wallet -> {
                        val intent = Intent(this, EncryptedChatActivity::class.java)
                        intent.putExtra("nav", "wallet")
                        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                        startActivity(intent)
                        finish()
                        true
                    }
                    R.id.nav_settings -> {
                        val intent = Intent(this, EncryptedChatActivity::class.java)
                        intent.putExtra("nav", "settings")
                        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                        startActivity(intent)
                        finish()
                        true
                    }
                    else -> false
                }
            }

            // binding.bottomNav.selectedItemId = R.id.nav_chats
        } catch (e: Exception) {
            Log.e("DMChatActivity", "Error in onCreate", e)
            Toast.makeText(this, "Error: ${e.message}", Toast.LENGTH_LONG).show()
            finish()
        }
    }

    private fun listenForMessages() {
        Log.d("DMChatActivity", "Starting to listen for messages in chatId: $chatId")
        db.collection("dm_chats")
            .document(chatId)
            .collection("messages")
            .orderBy("timestamp")
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    Log.e("DMChatActivity", "Snapshot error: ${error.message}", error)
                    return@addSnapshotListener
                }
                
                Log.d("DMChatActivity", "Snapshot received: ${snapshot?.documents?.size ?: 0} documents")
                
                if (snapshot != null) {
                    // Always scroll to bottom for new messages
                    val wasAtBottom = true
                    messages.clear()
                    
                    for (doc in snapshot.documents) {
                        Log.d("DMChatActivity", "Document data: ${doc.data}")
                        val message = doc.toObject(ChatMessage::class.java)
                        if (message != null) {
                            messages.add(message)
                            Log.d("DMChatActivity", "Added message: sender=${message.sender}, msg=${message.message.take(10)}...")
                        } else {
                            Log.e("DMChatActivity", "Failed to convert document to ChatMessage: ${doc.id}")
                        }
                    }
                    
                    Log.d("DMChatActivity", "Total messages after processing: ${messages.size}")
                    
                    adapter.notifyDataSetChanged()
                    if (wasAtBottom && messages.isNotEmpty()) {
                        binding.dmRecyclerView.post {
                            binding.dmRecyclerView.scrollToPosition(messages.size - 1)
                        }
                    }
                }
            }
    }

    private fun markChatAsRead() {
        val now = System.currentTimeMillis()
        db.collection("dm_chats")
            .document(chatId)
            .update("lastRead.$currentUser", now)
    }

    private fun showAddToContactsDialog() {
        val contactsRef = db.collection("contacts")
        contactsRef.document(currentUser).get()
            .addOnSuccessListener { doc ->
                // Use safe cast with fallback to empty list
                val list = (doc.get("list") as? List<String>)?.toMutableList() ?: mutableListOf()
                if (list.contains(recipient)) {
                    Toast.makeText(this, "$recipient is already in your contacts", Toast.LENGTH_SHORT).show()
                } else {
                    AlertDialog.Builder(this)
                        .setTitle("Add to Contacts")
                        .setMessage("Add $recipient to your contacts?")
                        .setPositiveButton("Yes") { _, _ ->
                            list.add(recipient)
                            contactsRef.document(currentUser).set(mapOf("list" to list))
                            Toast.makeText(this, "$recipient added to contacts", Toast.LENGTH_SHORT).show()
                        }
                        .setNegativeButton("Cancel", null)
                        .show()
                }
            }
    }

    override fun onResume() {
        super.onResume()
        markChatAsRead()
    }

    // Add this method to scroll to the bottom of the RecyclerView
    private fun scrollToBottom() {
        if (messages.isNotEmpty()) {
            binding.dmRecyclerView.post {
                binding.dmRecyclerView.smoothScrollToPosition(messages.size - 1)
            }
        }
    }
}



















