com.sr.ghostencryptedchat.app-media-1.6.0-0 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\002b212ad783e73de1712c56ac2acdc6\transformed\media-1.6.0\res
com.sr.ghostencryptedchat.app-lifecycle-runtime-ktx-2.5.1-1 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\069f94fc6847012b60c4542900b70cfd\transformed\lifecycle-runtime-ktx-2.5.1\res
com.sr.ghostencryptedchat.app-core-1.10.1-2 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\res
com.sr.ghostencryptedchat.app-slidingpanelayout-1.2.0-3 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1468990c181f20a121382559d5061f44\transformed\slidingpanelayout-1.2.0\res
com.sr.ghostencryptedchat.app-material-1.10.0-4 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1936637f2b78fb49128b3efd39ec3928\transformed\material-1.10.0\res
com.sr.ghostencryptedchat.app-lifecycle-runtime-2.5.1-5 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\194f0f8f87f7c370815cc9767701c0f1\transformed\lifecycle-runtime-2.5.1\res
com.sr.ghostencryptedchat.app-media3-ui-1.2.1-6 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\20819e21ff7c6943837ea2e5c7b675ed\transformed\media3-ui-1.2.1\res
com.sr.ghostencryptedchat.app-savedstate-1.2.0-7 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\27541394121a00a93e1c2cc52bad0088\transformed\savedstate-1.2.0\res
com.sr.ghostencryptedchat.app-coordinatorlayout-1.1.0-8 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\3d5538849c353bc484a931ee0c5dfcb4\transformed\coordinatorlayout-1.1.0\res
com.sr.ghostencryptedchat.app-core-ktx-1.10.1-9 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\412f5e5b9e7b33441491c008cfcf8403\transformed\core-ktx-1.10.1\res
com.sr.ghostencryptedchat.app-lifecycle-livedata-core-2.5.1-10 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\48cfcf0dc6ed58a7259bf2c1f5f0e085\transformed\lifecycle-livedata-core-2.5.1\res
com.sr.ghostencryptedchat.app-lifecycle-viewmodel-2.5.1-11 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\4a7ccb130b2d53875c0a3a460337ba67\transformed\lifecycle-viewmodel-2.5.1\res
com.sr.ghostencryptedchat.app-savedstate-ktx-1.2.0-12 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\4c8c464530416b8e6bf74f92c099de05\transformed\savedstate-ktx-1.2.0\res
com.sr.ghostencryptedchat.app-customview-poolingcontainer-1.0.0-13 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\52a935d498e6c703f1080a15d7b7d02d\transformed\customview-poolingcontainer-1.0.0\res
com.sr.ghostencryptedchat.app-media3-exoplayer-1.2.1-14 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\573c237f13283bc1d5f145ebb8306e38\transformed\media3-exoplayer-1.2.1\res
com.sr.ghostencryptedchat.app-lifecycle-viewmodel-ktx-2.5.1-15 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\5b01bad642b38f63b96802e0b70fc049\transformed\lifecycle-viewmodel-ktx-2.5.1\res
com.sr.ghostencryptedchat.app-constraintlayout-2.0.1-16 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\5c640d08c7fc29c8d79fc4c9159ce3c6\transformed\constraintlayout-2.0.1\res
com.sr.ghostencryptedchat.app-play-services-base-18.0.1-17 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\res
com.sr.ghostencryptedchat.app-preference-ktx-1.2.1-18 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6b2b1763b94b214a28d6acdeffee58e0\transformed\preference-ktx-1.2.1\res
com.sr.ghostencryptedchat.app-lifecycle-process-2.4.1-19 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\res
com.sr.ghostencryptedchat.app-fragment-1.3.6-20 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\878eb17854bf1d32a7c55175d3665965\transformed\fragment-1.3.6\res
com.sr.ghostencryptedchat.app-startup-runtime-1.1.1-21 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\8af3297a194b4b40b2604f3e1b7718cc\transformed\startup-runtime-1.1.1\res
com.sr.ghostencryptedchat.app-emoji2-views-helper-1.2.0-22 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\94e3ac7693103d73702b56b67aca1426\transformed\emoji2-views-helper-1.2.0\res
com.sr.ghostencryptedchat.app-cardview-1.0.0-23 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\963e8db15f792261bf42c108250682b5\transformed\cardview-1.0.0\res
com.sr.ghostencryptedchat.app-appcompat-resources-1.6.1-24 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\9bf1d69a4f2855ab4eaf5dc25cd2313f\transformed\appcompat-resources-1.6.1\res
com.sr.ghostencryptedchat.app-activity-ktx-1.6.1-25 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\9dca56c8203e6c145515337013efb3de\transformed\activity-ktx-1.6.1\res
com.sr.ghostencryptedchat.app-annotation-experimental-1.3.1-26 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a813e572b8d01d5b93f4b7b7da93ed56\transformed\annotation-experimental-1.3.1\res
com.sr.ghostencryptedchat.app-emoji2-1.2.0-27 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\res
com.sr.ghostencryptedchat.app-window-1.0.0-28 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\res
com.sr.ghostencryptedchat.app-activity-1.6.1-29 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\cb0f2e1c5fa0814b8229f949fc2a5ee8\transformed\activity-1.6.1\res
com.sr.ghostencryptedchat.app-play-services-basement-18.1.0-30 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\res
com.sr.ghostencryptedchat.app-recyclerview-1.3.2-31 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\d23f5c8b8f40ce50578d3e87ef54bd30\transformed\recyclerview-1.3.2\res
com.sr.ghostencryptedchat.app-transition-1.4.1-32 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\d53ac869d6d8cf4247d96260bc4f8af8\transformed\transition-1.4.1\res
com.sr.ghostencryptedchat.app-preference-1.2.1-33 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\d858642a26bc843a8ca821fcaa92e1a0\transformed\preference-1.2.1\res
com.sr.ghostencryptedchat.app-drawerlayout-1.1.1-34 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\db23de3102ef5b0aacf59cb2ff8ef0b8\transformed\drawerlayout-1.1.1\res
com.sr.ghostencryptedchat.app-viewpager2-1.1.0-beta02-35 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\eff03cc53257200e8c3dcfd922122a86\transformed\viewpager2-1.1.0-beta02\res
com.sr.ghostencryptedchat.app-appcompat-1.6.1-36 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f2dc38ad7cc09bc05989a176e11ace02\transformed\appcompat-1.6.1\res
com.sr.ghostencryptedchat.app-firebase-common-20.4.2-37 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\res
com.sr.ghostencryptedchat.app-lifecycle-viewmodel-savedstate-2.5.1-38 C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ffff699af584157ef014e9fa478e21d8\transformed\lifecycle-viewmodel-savedstate-2.5.1\res
com.sr.ghostencryptedchat.app-pngs-39 C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\build\generated\res\pngs\debug
com.sr.ghostencryptedchat.app-resValues-40 C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\build\generated\res\resValues\debug
com.sr.ghostencryptedchat.app-packageDebugResources-41 C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.sr.ghostencryptedchat.app-packageDebugResources-42 C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.sr.ghostencryptedchat.app-merged_res-43 C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\build\intermediates\merged_res\debug
com.sr.ghostencryptedchat.app-debug-44 C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\debug\res
com.sr.ghostencryptedchat.app-main-45 C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res
