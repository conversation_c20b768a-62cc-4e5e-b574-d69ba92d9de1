<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_chats" modulePackage="com.sr.ghostencryptedchat" filePath="app\src\main\res\layout\fragment_chats.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/fragment_chats_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="38" endOffset="13"/></Target><Target id="@+id/backgroundView" view="com.sr.ghostencryptedchat.view.ThreeDBackgroundView"><Expressions/><location startLine="6" startOffset="4" endLine="9" endOffset="46"/></Target><Target id="@+id/chatLayout" view="LinearLayout"><Expressions/><location startLine="12" startOffset="4" endLine="37" endOffset="18"/></Target><Target id="@+id/chatTitle" view="TextView"><Expressions/><location startLine="20" startOffset="8" endLine="28" endOffset="41"/></Target><Target id="@+id/chatListRecycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="30" startOffset="8" endLine="36" endOffset="41"/></Target></Targets></Layout>