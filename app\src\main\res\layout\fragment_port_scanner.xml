<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <ImageButton
                android:id="@+id/backButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_arrow_back"
                android:tint="@color/text_primary"
                android:contentDescription="Back" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="16dp"
                android:text="Port Scanner"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

        </LinearLayout>

        <!-- Target Input -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/background_secondary"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Target Host"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <EditText
                    android:id="@+id/targetInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Enter IP address or hostname"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_secondary"
                    android:background="@drawable/rounded_input_background"
                    android:padding="16dp"
                    android:fontFamily="monospace"
                    android:inputType="textUri" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Scan Options -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/background_secondary"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Scan Options"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <!-- Common Ports Scan -->
                <Button
                    android:id="@+id/scanCommonButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="Scan Common Ports"
                    android:textColor="@color/background_primary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:backgroundTint="@color/accent_color"
                    android:layout_marginBottom="12dp"
                    style="@style/Widget.MaterialComponents.Button" />

                <!-- Custom Ports -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Custom Ports (comma-separated or ranges like 80-90)"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="8dp" />

                <EditText
                    android:id="@+id/customPortsInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="e.g., 80,443,8080-8090"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_secondary"
                    android:background="@drawable/rounded_input_background"
                    android:padding="16dp"
                    android:fontFamily="monospace"
                    android:inputType="text"
                    android:layout_marginBottom="12dp" />

                <Button
                    android:id="@+id/scanCustomButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="Scan Custom Ports"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:backgroundTint="@color/background_primary"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Scan Status -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:id="@+id/scanStatus"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Ready to scan"
                android:textSize="14sp"
                android:textColor="@color/text_secondary" />

            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:indeterminateTint="@color/accent_color"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Results -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:minHeight="300dp"
            app:cardBackgroundColor="@color/background_secondary"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Scan Results"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:padding="20dp"
                    android:paddingBottom="12dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/resultsRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:paddingHorizontal="12dp"
                    android:paddingBottom="12dp"
                    android:clipToPadding="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
