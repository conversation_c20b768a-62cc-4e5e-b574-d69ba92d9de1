package com.sr.ghostencryptedchat.ui

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.sr.ghostencryptedchat.databinding.ItemNetworkDeviceBinding

class NetworkDeviceAdapter(
    private val devices: List<NetworkDevice>
) : RecyclerView.Adapter<NetworkDeviceAdapter.DeviceViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceViewHolder {
        val binding = ItemNetworkDeviceBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return DeviceViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON>ViewHolder, position: Int) {
        holder.bind(devices[position])
    }

    override fun getItemCount(): Int = devices.size

    inner class DeviceViewHolder(private val binding: ItemNetworkDeviceBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(device: NetworkDevice) {
            binding.apply {
                deviceIp.text = device.ip
                deviceHostname.text = device.hostname
                
                if (device.isReachable) {
                    deviceStatus.text = "Online"
                    deviceStatus.setTextColor(
                        itemView.context.getColor(android.R.color.holo_green_light)
                    )
                    responseTime.text = "${device.responseTime}ms"
                    responseTime.visibility = android.view.View.VISIBLE
                } else {
                    deviceStatus.text = "Offline"
                    deviceStatus.setTextColor(
                        itemView.context.getColor(android.R.color.holo_red_light)
                    )
                    responseTime.visibility = android.view.View.GONE
                }
                
                // Set device icon based on hostname or IP
                deviceIcon.text = when {
                    device.hostname.contains("router", ignoreCase = true) -> "🌐"
                    device.hostname.contains("phone", ignoreCase = true) -> "📱"
                    device.hostname.contains("laptop", ignoreCase = true) -> "💻"
                    device.hostname.contains("desktop", ignoreCase = true) -> "🖥️"
                    device.hostname.contains("printer", ignoreCase = true) -> "🖨️"
                    device.hostname == "This Device" -> "📍"
                    else -> "📡"
                }
            }
        }
    }
}
