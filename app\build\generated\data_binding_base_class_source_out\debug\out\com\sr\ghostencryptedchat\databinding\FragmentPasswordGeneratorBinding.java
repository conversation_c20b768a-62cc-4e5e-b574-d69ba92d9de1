// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.ImageButton;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.slider.Slider;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentPasswordGeneratorBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final Button copyButton;

  @NonNull
  public final CheckBox excludeSimilar;

  @NonNull
  public final Button generateButton;

  @NonNull
  public final TextView generatedPassword;

  @NonNull
  public final CheckBox includeLowercase;

  @NonNull
  public final CheckBox includeNumbers;

  @NonNull
  public final CheckBox includeSymbols;

  @NonNull
  public final CheckBox includeUppercase;

  @NonNull
  public final Slider lengthSlider;

  @NonNull
  public final TextView lengthValue;

  @NonNull
  public final TextView strengthIndicator;

  private FragmentPasswordGeneratorBinding(@NonNull ScrollView rootView,
      @NonNull ImageButton backButton, @NonNull Button copyButton, @NonNull CheckBox excludeSimilar,
      @NonNull Button generateButton, @NonNull TextView generatedPassword,
      @NonNull CheckBox includeLowercase, @NonNull CheckBox includeNumbers,
      @NonNull CheckBox includeSymbols, @NonNull CheckBox includeUppercase,
      @NonNull Slider lengthSlider, @NonNull TextView lengthValue,
      @NonNull TextView strengthIndicator) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.copyButton = copyButton;
    this.excludeSimilar = excludeSimilar;
    this.generateButton = generateButton;
    this.generatedPassword = generatedPassword;
    this.includeLowercase = includeLowercase;
    this.includeNumbers = includeNumbers;
    this.includeSymbols = includeSymbols;
    this.includeUppercase = includeUppercase;
    this.lengthSlider = lengthSlider;
    this.lengthValue = lengthValue;
    this.strengthIndicator = strengthIndicator;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentPasswordGeneratorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentPasswordGeneratorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_password_generator, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentPasswordGeneratorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backButton;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.copyButton;
      Button copyButton = ViewBindings.findChildViewById(rootView, id);
      if (copyButton == null) {
        break missingId;
      }

      id = R.id.excludeSimilar;
      CheckBox excludeSimilar = ViewBindings.findChildViewById(rootView, id);
      if (excludeSimilar == null) {
        break missingId;
      }

      id = R.id.generateButton;
      Button generateButton = ViewBindings.findChildViewById(rootView, id);
      if (generateButton == null) {
        break missingId;
      }

      id = R.id.generatedPassword;
      TextView generatedPassword = ViewBindings.findChildViewById(rootView, id);
      if (generatedPassword == null) {
        break missingId;
      }

      id = R.id.includeLowercase;
      CheckBox includeLowercase = ViewBindings.findChildViewById(rootView, id);
      if (includeLowercase == null) {
        break missingId;
      }

      id = R.id.includeNumbers;
      CheckBox includeNumbers = ViewBindings.findChildViewById(rootView, id);
      if (includeNumbers == null) {
        break missingId;
      }

      id = R.id.includeSymbols;
      CheckBox includeSymbols = ViewBindings.findChildViewById(rootView, id);
      if (includeSymbols == null) {
        break missingId;
      }

      id = R.id.includeUppercase;
      CheckBox includeUppercase = ViewBindings.findChildViewById(rootView, id);
      if (includeUppercase == null) {
        break missingId;
      }

      id = R.id.lengthSlider;
      Slider lengthSlider = ViewBindings.findChildViewById(rootView, id);
      if (lengthSlider == null) {
        break missingId;
      }

      id = R.id.lengthValue;
      TextView lengthValue = ViewBindings.findChildViewById(rootView, id);
      if (lengthValue == null) {
        break missingId;
      }

      id = R.id.strengthIndicator;
      TextView strengthIndicator = ViewBindings.findChildViewById(rootView, id);
      if (strengthIndicator == null) {
        break missingId;
      }

      return new FragmentPasswordGeneratorBinding((ScrollView) rootView, backButton, copyButton,
          excludeSimilar, generateButton, generatedPassword, includeLowercase, includeNumbers,
          includeSymbols, includeUppercase, lengthSlider, lengthValue, strengthIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
