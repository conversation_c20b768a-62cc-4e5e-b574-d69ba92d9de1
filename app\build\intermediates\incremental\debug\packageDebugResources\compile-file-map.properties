#Tue Jun 10 13:44:43 MST 2025
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_launcher_background.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_launcher_background.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_send.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_send.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/neon_input_border.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\neon_input_border.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_add_image.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_add_image.xml
com.sr.ghostencryptedchat.app-main-5\:/color/nav_icon_selector.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\color\\nav_icon_selector.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/item_dm_message.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_dm_message.xml
com.sr.ghostencryptedchat.app-main-5\:/xml/backup_rules.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\xml\\backup_rules.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_contacts.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_contacts.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_avatar_placeholder.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_avatar_placeholder.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/activity_dm_requests.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_dm_requests.xml
com.sr.ghostencryptedchat.app-main-5\:/color/nav_text_selector.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\color\\nav_text_selector.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-mdpi/ic_launcher_foreground.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher_foreground.webp
com.sr.ghostencryptedchat.app-main-5\:/xml/network_security_config.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\xml\\network_security_config.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_chat.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_chat.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/fragment_chats.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_chats.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/activity_main.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_main.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.sr.ghostencryptedchat.app-main-5\:/mipmap-xhdpi/ic_launcher_foreground.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\ic_launcher_foreground.webp
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_wallet.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_wallet.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/activity_username_setup.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_username_setup.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/activity_encrypted_chat.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_encrypted_chat.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/bg_bubble_other.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_bubble_other.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-anydpi-v26\\ic_launcher.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/fragment_contacts.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_contacts.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/fragment_wallet.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_wallet.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher.webp
com.sr.ghostencryptedchat.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\ic_launcher.webp
com.sr.ghostencryptedchat.app-main-5\:/drawable/bg_message_other.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_message_other.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/activity_dm_request.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_dm_request.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-hdpi/ic_launcher_foreground.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher_foreground.webp
com.sr.ghostencryptedchat.app-main-5\:/xml/data_extraction_rules.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\xml\\data_extraction_rules.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/neon_border.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\neon_border.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_globe.png=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_globe.png
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/item_message.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_message.xml
com.sr.ghostencryptedchat.app-main-5\:/menu/bottom_nav_menu.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\menu\\bottom_nav_menu.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/message_bubble_me.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\message_bubble_me.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/search_box_background.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\search_box_background.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/bg_badge.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_badge.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/activity_contacts.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_contacts.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/activity_direct_message.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_direct_message.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.sr.ghostencryptedchat.app-main-5\:/drawable/bg_message_me.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_message_me.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_launcher_foreground.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/item_contact.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_contact.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/activity_settings.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_settings.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.sr.ghostencryptedchat.app-main-5\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\ic_launcher_foreground.webp
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_globe_white.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_globe_white.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/item_message_me.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_message_me.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/bg_dm_message.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_dm_message.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/circle_green.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\circle_green.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/rounded_bg.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\rounded_bg.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/item_chat.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_chat.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/dm_input_box.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\dm_input_box.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/bg_chat_item.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_chat_item.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-xxhdpi/ic_launcher_foreground.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\ic_launcher_foreground.webp
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/activity_dm_chat.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_dm_chat.xml
com.sr.ghostencryptedchat.app-main-5\:/menu/dm_menu.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\menu\\dm_menu.xml
com.sr.ghostencryptedchat.app-main-5\:/menu/chat_menu.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\menu\\chat_menu.xml
com.sr.ghostencryptedchat.app-main-5\:/xml/file_paths.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\xml\\file_paths.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_settings.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_settings.xml
com.sr.ghostencryptedchat.app-main-5\:/color/nav_item_background_selector.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\color\\nav_item_background_selector.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_add_contact.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_add_contact.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_person.png=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_person.png
com.sr.ghostencryptedchat.app-main-5\:/drawable/bg_input_border.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_input_border.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher.webp
com.sr.ghostencryptedchat.app-main-5\:/drawable/message_bubble_other.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\message_bubble_other.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/activity_group_chat.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_group_chat.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/fragment_settings.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_settings.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/item_chat_list.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_chat_list.xml
com.sr.ghostencryptedchat.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.sr.ghostencryptedchat.app-main-5\:/drawable/bg_bubble_me.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_bubble_me.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/splash_logo.png=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\splash_logo.png
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/item_direct_message.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_direct_message.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/circle_bg.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\circle_bg.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/item_dm_request.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_dm_request.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/avatar_circle_bg.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\avatar_circle_bg.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/item_message_other.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_message_other.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/activity_splash.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_splash.xml
com.sr.ghostencryptedchat.app-packageDebugResources-2\:/layout/fragment_global_chat.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_global_chat.xml
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_person_add.png=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_person_add.png
com.sr.ghostencryptedchat.app-main-5\:/drawable/ic_add_chat.xml=C\:\\Users\\C\\AndroidStudioProjects\\GhostEncryptedChat\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_add_chat.xml
