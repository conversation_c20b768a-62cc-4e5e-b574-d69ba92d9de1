<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="16dp"
    android:background="?android:attr/selectableItemBackground">

    <!-- Device Icon -->
    <TextView
        android:id="@+id/deviceIcon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/circle_background"
        android:backgroundTint="@color/accent_color"
        android:gravity="center"
        android:text="📡"
        android:textSize="20sp" />

    <!-- Device Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/deviceIp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="***********"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:fontFamily="monospace" />

            <TextView
                android:id="@+id/deviceStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Online"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/accent_color" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="4dp">

            <TextView
                android:id="@+id/deviceHostname"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Unknown Device"
                android:textSize="14sp"
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/responseTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="25ms"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:fontFamily="monospace" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
