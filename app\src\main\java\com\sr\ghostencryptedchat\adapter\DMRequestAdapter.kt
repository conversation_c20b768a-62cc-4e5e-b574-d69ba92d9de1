package com.sr.ghostencryptedchat.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.sr.ghostencryptedchat.databinding.ItemDmRequestBinding
import com.sr.ghostencryptedchat.model.DMRequest

class DMRequestAdapter(
    private val requests: List<DMRequest>,
    private val onAccept: (DMRequest) -> Unit,
    private val onReject: (DMRequest) -> Unit
) : RecyclerView.Adapter<DMRequestAdapter.DMRequestViewHolder>() {

    inner class DMRequestViewHolder(val binding: ItemDmRequestBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DMRequestViewHolder {
        val binding = ItemDmRequestBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return DMRequestViewHolder(binding)
    }

    override fun onBindViewHolder(holder: DMRequestViewHolder, position: Int) {
        val request = requests[position]
        holder.binding.requestText.text = "${request.from} wants to DM you"
        holder.binding.acceptButton.setOnClickListener { onAccept(request) }
        holder.binding.rejectButton.setOnClickListener { onReject(request) }
    }

    override fun getItemCount(): Int = requests.size
}
