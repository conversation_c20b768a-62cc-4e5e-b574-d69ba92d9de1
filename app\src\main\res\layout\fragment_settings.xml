<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="#121212">

    <TextView
        android:id="@+id/usernameText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Username:"
        android:textColor="#FFFFFF"
        android:textSize="18sp"
        android:layout_marginBottom="24dp" />

    <Button
        android:id="@+id/backupButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Backup Account"
        android:layout_marginBottom="16dp"
        android:backgroundTint="#aeff00"
        android:textColor="#000000"/>

    <Button
        android:id="@+id/restoreButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Restore Account"
        android:layout_marginBottom="16dp"
        android:backgroundTint="#aeff00"
        android:textColor="#000000"/>

</LinearLayout>
