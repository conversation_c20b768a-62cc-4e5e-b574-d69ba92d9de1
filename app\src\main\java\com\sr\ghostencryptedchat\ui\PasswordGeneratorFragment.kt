package com.sr.ghostencryptedchat.ui

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.sr.ghostencryptedchat.databinding.FragmentPasswordGeneratorBinding
import java.security.SecureRandom
import kotlin.random.Random

class PasswordGeneratorFragment : Fragment() {

    private var _binding: FragmentPasswordGeneratorBinding? = null
    private val binding get() = _binding!!

    private val secureRandom = SecureRandom()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPasswordGeneratorBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupUI()
        generatePassword() // Generate initial password
    }

    private fun setupUI() {
        // Set initial values
        binding.lengthSlider.value = 16f
        binding.lengthValue.text = "16"
        
        // Set default options
        binding.includeUppercase.isChecked = true
        binding.includeLowercase.isChecked = true
        binding.includeNumbers.isChecked = true
        binding.includeSymbols.isChecked = true
        binding.excludeSimilar.isChecked = true
        
        // Set up listeners
        binding.lengthSlider.addOnChangeListener { _, value, _ ->
            val length = value.toInt()
            binding.lengthValue.text = length.toString()
            generatePassword()
        }
        
        binding.includeUppercase.setOnCheckedChangeListener { _, _ -> generatePassword() }
        binding.includeLowercase.setOnCheckedChangeListener { _, _ -> generatePassword() }
        binding.includeNumbers.setOnCheckedChangeListener { _, _ -> generatePassword() }
        binding.includeSymbols.setOnCheckedChangeListener { _, _ -> generatePassword() }
        binding.excludeSimilar.setOnCheckedChangeListener { _, _ -> generatePassword() }
        
        binding.generateButton.setOnClickListener { generatePassword() }
        binding.copyButton.setOnClickListener { copyPassword() }
        binding.backButton.setOnClickListener { 
            parentFragmentManager.popBackStack()
        }
    }

    private fun generatePassword() {
        val length = binding.lengthSlider.value.toInt()
        
        // Build character set based on options
        var charset = ""
        
        if (binding.includeUppercase.isChecked) {
            charset += if (binding.excludeSimilar.isChecked) {
                "ABCDEFGHJKLMNPQRSTUVWXYZ" // Exclude I, O
            } else {
                "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
            }
        }
        
        if (binding.includeLowercase.isChecked) {
            charset += if (binding.excludeSimilar.isChecked) {
                "abcdefghjkmnpqrstuvwxyz" // Exclude i, l, o
            } else {
                "abcdefghijklmnopqrstuvwxyz"
            }
        }
        
        if (binding.includeNumbers.isChecked) {
            charset += if (binding.excludeSimilar.isChecked) {
                "23456789" // Exclude 0, 1
            } else {
                "0123456789"
            }
        }
        
        if (binding.includeSymbols.isChecked) {
            charset += "!@#$%^&*()_+-=[]{}|;:,.<>?"
        }
        
        if (charset.isEmpty()) {
            binding.generatedPassword.text = "Select at least one character type"
            binding.copyButton.isEnabled = false
            return
        }
        
        // Generate secure password
        val password = StringBuilder()
        for (i in 0 until length) {
            val randomIndex = secureRandom.nextInt(charset.length)
            password.append(charset[randomIndex])
        }
        
        binding.generatedPassword.text = password.toString()
        binding.copyButton.isEnabled = true
        
        // Update strength indicator
        updatePasswordStrength(password.toString())
    }

    private fun updatePasswordStrength(password: String) {
        val strength = calculatePasswordStrength(password)
        
        when {
            strength >= 80 -> {
                binding.strengthIndicator.text = "Very Strong"
                binding.strengthIndicator.setTextColor(
                    requireContext().getColor(android.R.color.holo_green_dark)
                )
            }
            strength >= 60 -> {
                binding.strengthIndicator.text = "Strong"
                binding.strengthIndicator.setTextColor(
                    requireContext().getColor(android.R.color.holo_green_light)
                )
            }
            strength >= 40 -> {
                binding.strengthIndicator.text = "Medium"
                binding.strengthIndicator.setTextColor(
                    requireContext().getColor(android.R.color.holo_orange_light)
                )
            }
            else -> {
                binding.strengthIndicator.text = "Weak"
                binding.strengthIndicator.setTextColor(
                    requireContext().getColor(android.R.color.holo_red_light)
                )
            }
        }
    }

    private fun calculatePasswordStrength(password: String): Int {
        var score = 0
        
        // Length bonus
        score += minOf(password.length * 4, 40)
        
        // Character variety bonus
        if (password.any { it.isUpperCase() }) score += 10
        if (password.any { it.isLowerCase() }) score += 10
        if (password.any { it.isDigit() }) score += 10
        if (password.any { !it.isLetterOrDigit() }) score += 15
        
        // Length penalties for short passwords
        if (password.length < 8) score -= 20
        if (password.length < 6) score -= 20
        
        return maxOf(0, minOf(100, score))
    }

    private fun copyPassword() {
        val password = binding.generatedPassword.text.toString()
        if (password.isNotEmpty() && password != "Select at least one character type") {
            val clipboard = requireContext().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("Generated Password", password)
            clipboard.setPrimaryClip(clip)
            
            Toast.makeText(requireContext(), "Password copied to clipboard", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
