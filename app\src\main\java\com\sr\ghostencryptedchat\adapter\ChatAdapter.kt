package com.sr.ghostencryptedchat.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.sr.ghostencryptedchat.R
import com.sr.ghostencryptedchat.model.ChatItem

class ChatAdapter(private val items: List<ChatItem>) :
    RecyclerView.Adapter<ChatAdapter.ChatViewHolder>() {

    class ChatViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val avatar: ImageView = view.findViewById(R.id.avatar)
        val name: TextView = view.findViewById(R.id.chatName)
        val preview: TextView = view.findViewById(R.id.chatMessagePreview)
        val time: TextView = view.findViewById(R.id.chatTimestamp)
        val unread: TextView = view.findViewById(R.id.unreadBadge)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_chat, parent, false)
        return ChatViewHolder(view)
    }

    override fun onBindViewHolder(holder: ChatViewHolder, position: Int) {
        val item = items[position]
        holder.name.text = item.name
        holder.preview.text = item.messagePreview
        holder.time.text = item.timestamp

        if (item.unreadCount > 0) {
            holder.unread.visibility = View.VISIBLE
            holder.unread.text = item.unreadCount.toString()
        } else {
            holder.unread.visibility = View.GONE
        }

        if (item.avatarResId != null) {
            holder.avatar.setImageResource(item.avatarResId)
        } else {
            holder.avatar.setImageResource(R.drawable.ic_avatar_placeholder)
        }
    }

    override fun getItemCount(): Int = items.size
}
