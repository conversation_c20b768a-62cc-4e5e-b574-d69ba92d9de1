<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    android:background="#121212">

    <TextView
        android:id="@+id/warning_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/holo_red_dark"
        android:layout_marginBottom="16dp"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Welcome to Ghost Chat"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="#FFFFFF"
        android:layout_marginBottom="32dp"/>

    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@mipmap/ic_launcher"
        android:layout_marginBottom="32dp"
        android:contentDescription="Ghost Chat Logo"/>

    <Button
        android:id="@+id/newAccountButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Create New Account"
        android:layout_marginBottom="16dp"
        android:backgroundTint="#00FF00"
        android:textColor="#000000"/>

    <Button
        android:id="@+id/recoverAccountButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Recover Existing Account"
        android:layout_marginBottom="16dp"
        android:backgroundTint="#00FF00"
        android:textColor="#000000"/>

    <LinearLayout
        android:id="@+id/newAccountLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <EditText
            android:id="@+id/username_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter username"
            android:inputType="text"
            android:background="#1C1C1C"
            android:textColor="#FFFFFF"
            android:textColorHint="#AAAAAA"
            android:padding="12dp"
            android:layout_marginBottom="16dp"/>

        <Button
            android:id="@+id/register_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Register Username"
            android:backgroundTint="#00FF00"
            android:textColor="#000000"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/recoverAccountLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <EditText
            android:id="@+id/seed_phrase_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter your seed phrase"
            android:inputType="textMultiLine"
            android:minLines="3"
            android:gravity="top"
            android:background="#1C1C1C"
            android:textColor="#FFFFFF"
            android:textColorHint="#AAAAAA"
            android:padding="12dp"
            android:layout_marginBottom="16dp"/>

        <Button
            android:id="@+id/recover_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Recover Account"
            android:backgroundTint="#00FF00"
            android:textColor="#000000"/>
    </LinearLayout>

</LinearLayout>
