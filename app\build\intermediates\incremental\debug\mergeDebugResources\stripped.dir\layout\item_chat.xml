<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    card_view:cardCornerRadius="12dp"
    card_view:cardBackgroundColor="#1F1F1F"
    android:elevation="4dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- Avatar Circle -->
        <ImageView
            android:id="@+id/avatar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_avatar_placeholder"
            android:background="@drawable/circle_bg"
            android:padding="4dp" />

        <!-- Name -->
        <TextView
            android:id="@+id/chatName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/avatar"
            android:layout_marginStart="12dp"
            android:text="Name"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Timestamp -->
        <TextView
            android:id="@+id/chatTimestamp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignTop="@id/chatName"
            android:text="12:11 PM"
            android:textColor="#CCCCCC"
            android:textSize="12sp" />

        <!-- Message Preview -->
        <TextView
            android:id="@+id/chatMessagePreview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/avatar"
            android:layout_marginTop="4dp"
            android:layout_marginStart="12dp"
            android:layout_below="@id/chatName"
            android:layout_alignParentEnd="true"
            android:text="This is the preview..."
            android:textColor="#AAAAAA"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="14sp" />

        <!-- Unread Badge -->
        <TextView
            android:id="@+id/unreadBadge"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentEnd="true"
            android:layout_below="@id/chatTimestamp"
            android:layout_marginTop="4dp"
            android:background="@drawable/bg_badge"
            android:text="3"
            android:textColor="#000000"
            android:gravity="center"
            android:textSize="12sp"
            android:visibility="visible" />
    </RelativeLayout>
</androidx.cardview.widget.CardView>
