package com.sr.ghostencryptedchat.ui

import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.firebase.firestore.FirebaseFirestore
import com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter
import com.sr.ghostencryptedchat.databinding.FragmentContactsBinding
import com.sr.ghostencryptedchat.DMChatActivity
import com.sr.ghostencryptedchat.util.KeyStoreUtil

class ContactsFragment : Fragment() {

    private var _binding: FragmentContactsBinding? = null
    private val binding get() = _binding!!

    private lateinit var db: FirebaseFirestore
    private lateinit var currentUser: String

    private val contactList = mutableListOf<Pair<String, Boolean>>() // (username, isPending)
    private lateinit var adapter: SimpleContactsAdapter

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentContactsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        db = FirebaseFirestore.getInstance()
        currentUser = KeyStoreUtil.getOrCreateUsername(requireContext())

        adapter = SimpleContactsAdapter(
            contactList,
            onClick = { name, isPending ->
                if (isPending) {
                    acceptChat(name)
                } else {
                    val intent = Intent(requireContext(), DMChatActivity::class.java)
                    intent.putExtra("recipient", name)
                    startActivity(intent)
                }
            },
            onLongClick = { name ->
                showDeleteContactDialog(name)
            }
        )

        binding.contactsRecycler.layoutManager = LinearLayoutManager(requireContext())
        binding.contactsRecycler.adapter = adapter

        setupSearch()
        fetchContactsAndIncomingDMs()
    }

    private fun showDeleteContactDialog(contactName: String) {
        AlertDialog.Builder(requireContext())
            .setTitle("Delete Contact")
            .setMessage("Are you sure you want to remove $contactName from your contacts?")
            .setPositiveButton("Delete") { _, _ -> deleteContact(contactName) }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun deleteContact(contactName: String) {
        db.collection("contacts").document(currentUser).get()
            .addOnSuccessListener { docSnapshot ->
                val list = (docSnapshot.get("list") as? List<String>)?.toMutableList() ?: mutableListOf()
                if (list.contains(contactName)) {
                    list.remove(contactName)
                    db.collection("contacts").document(currentUser)
                        .set(mapOf("list" to list))
                        .addOnSuccessListener {
                            Toast.makeText(requireContext(), "$contactName removed from contacts", Toast.LENGTH_SHORT).show()
                            fetchContactsAndIncomingDMs()
                        }
                        .addOnFailureListener {
                            Toast.makeText(requireContext(), "Failed to remove contact", Toast.LENGTH_SHORT).show()
                        }
                }
            }
    }

    private fun setupSearch() {
        binding.contactSearchInput.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val query = s.toString().trim()
                if (query.isNotEmpty()) {
                    searchUser(query)
                } else {
                    fetchContactsAndIncomingDMs()
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })
    }

    private fun searchUser(query: String) {
        val lowercaseQuery = query.lowercase()
        db.collection("users")
            .get()
            .addOnSuccessListener { result ->
                contactList.clear()
                val match = result.documents.firstOrNull { doc ->
                    val username = doc.getString("username") ?: ""
                    username.lowercase() == lowercaseQuery && username != currentUser
                }?.getString("username")
                if (match != null) {
                    contactList.add(Pair(match, false))
                }
                adapter.notifyDataSetChanged()
            }
    }

    private fun fetchContactsAndIncomingDMs() {
        contactList.clear()

        db.collection("contacts").document(currentUser).get()
            .addOnSuccessListener { doc ->
                val list = doc.get("list") as? List<*> ?: emptyList<String>()
                val validList = list.filterIsInstance<String>()
                contactList.addAll(validList.map { Pair(it, false) })

                db.collection("dm_chats")
                    .whereArrayContains("participants", currentUser)
                    .get()
                    .addOnSuccessListener { result ->
                        for (doc in result.documents) {
                            val participants = doc.get("participants") as? List<*> ?: continue
                            val status = doc.getString("status") ?: continue
                            val lastSender = doc.getString("lastSender") ?: continue

                            val otherUser = participants.firstOrNull { it != currentUser }?.toString()
                            if (status == "pending" &&
                                otherUser != null &&
                                lastSender != currentUser &&
                                !contactList.any { it.first == otherUser }
                            ) {
                                contactList.add(Pair(otherUser, true))
                            }
                        }
                        adapter.notifyDataSetChanged()
                    }
            }
    }

    private fun acceptChat(fromUser: String) {
        val chatId = listOf(currentUser, fromUser).sorted().joinToString("_")
        val chatRef = db.collection("dm_chats").document(chatId)

        chatRef.update("status", "accepted")
            .addOnSuccessListener {
                Toast.makeText(requireContext(), "DM accepted with $fromUser", Toast.LENGTH_SHORT).show()
                val intent = Intent(requireContext(), DMChatActivity::class.java)
                intent.putExtra("recipient", fromUser)
                startActivity(intent)
            }
            .addOnFailureListener {
                Toast.makeText(requireContext(), "Failed to accept DM", Toast.LENGTH_SHORT).show()
            }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
