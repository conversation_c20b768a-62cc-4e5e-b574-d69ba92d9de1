// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSettingsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button backupButton;

  @NonNull
  public final Button restoreButton;

  @NonNull
  public final TextView usernameText;

  private FragmentSettingsBinding(@NonNull LinearLayout rootView, @NonNull Button backupButton,
      @NonNull Button restoreButton, @NonNull TextView usernameText) {
    this.rootView = rootView;
    this.backupButton = backupButton;
    this.restoreButton = restoreButton;
    this.usernameText = usernameText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backupButton;
      Button backupButton = ViewBindings.findChildViewById(rootView, id);
      if (backupButton == null) {
        break missingId;
      }

      id = R.id.restoreButton;
      Button restoreButton = ViewBindings.findChildViewById(rootView, id);
      if (restoreButton == null) {
        break missingId;
      }

      id = R.id.usernameText;
      TextView usernameText = ViewBindings.findChildViewById(rootView, id);
      if (usernameText == null) {
        break missingId;
      }

      return new FragmentSettingsBinding((LinearLayout) rootView, backupButton, restoreButton,
          usernameText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
