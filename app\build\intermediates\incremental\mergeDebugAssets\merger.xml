<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\assets"><file name="images/ghost.png" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\assets\images\ghost.png"/><file name="models/ghost.obj" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\assets\models\ghost.obj"/><file name="models/ghost_simple.obj" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\assets\models\ghost_simple.obj"/><file name="models/ghost_test.obj" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\assets\models\ghost_test.obj"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\build\intermediates\shader_assets\debug\out"/></dataSet></merger>