R_DEF: Internal format may change without notice
local
color black
color ic_launcher_background
color nav_icon_selector
color nav_item_background_selector
color nav_text_selector
color neon_green
color white
drawable avatar_circle_bg
drawable bg_badge
drawable bg_bubble_me
drawable bg_bubble_other
drawable bg_chat_item
drawable bg_dm_message
drawable bg_input_border
drawable bg_message_me
drawable bg_message_other
drawable circle_bg
drawable circle_green
drawable dm_input_box
drawable ic_add_chat
drawable ic_add_contact
drawable ic_add_image
drawable ic_avatar_placeholder
drawable ic_chat
drawable ic_contacts
drawable ic_globe
drawable ic_globe_white
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_person
drawable ic_person_add
drawable ic_send
drawable ic_settings
drawable ic_wallet
drawable message_bubble_me
drawable message_bubble_other
drawable neon_border
drawable neon_input_border
drawable rounded_bg
drawable search_box_background
drawable splash_logo
id acceptButton
id action_add_to_contacts
id addToContactsButton
id avatar
id backgroundView
id backupButton
id bottomNav
id chatIcon
id chatLayout
id chatListRecycler
id chatMessagePreview
id chatName
id chatNewLabel
id chatTimestamp
id chatTitle
id contactList
id contactName
id contactSearchInput
id contactsHeader
id contactsLayout
id contactsRecycler
id contactsTitle
id directMessageLayout
id dmInput
id dmInputContainer
id dmList
id dmMessageInput
id dmRecyclerView
id dmRequestList
id dmSend
id dmTopBar
id fragmentContainer
id globalChatLayout
id lastMessage
id launchChatButton
id main
id menu_change_username
id menu_dm
id menu_dm_requests
id menu_group
id messageContainer
id messageInput
id messageText
id nav_chats
id nav_contacts
id nav_settings
id nav_wallet
id newAccountButton
id newAccountLayout
id newIndicator
id progressBar
id recipientName
id recoverAccountButton
id recoverAccountLayout
id recover_button
id recyclerView
id register_button
id rejectButton
id requestText
id restoreButton
id search_input
id seed_phrase_input
id sendButton
id sendDMButton
id senderName
id splashImage
id statusText
id textMessage
id timestamp
id title
id unreadBadge
id usernameText
id username_input
id warning_text
id webView
layout activity_contacts
layout activity_direct_message
layout activity_dm_chat
layout activity_dm_request
layout activity_dm_requests
layout activity_encrypted_chat
layout activity_group_chat
layout activity_main
layout activity_settings
layout activity_splash
layout activity_username_setup
layout fragment_chats
layout fragment_contacts
layout fragment_global_chat
layout fragment_settings
layout fragment_wallet
layout item_chat
layout item_chat_list
layout item_contact
layout item_direct_message
layout item_dm_message
layout item_dm_request
layout item_message
layout item_message_me
layout item_message_other
menu bottom_nav_menu
menu chat_menu
menu dm_menu
mipmap ic_launcher
mipmap ic_launcher_foreground
mipmap ic_launcher_round
raw background
string app_name
style Base.Theme.GhostEncryptedChat
style BottomNavigationViewStyle
style BottomNavigationView.ActiveIndicator
style TextAppearance.BottomNavigationView.Active
style TextAppearance.BottomNavigationView.Inactive
style Theme.GhostEncryptedChat
xml backup_rules
xml data_extraction_rules
xml file_paths
xml network_security_config
