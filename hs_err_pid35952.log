#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 536870912 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3825), pid=35952, tid=20360
#
# JRE version:  (17.0.11) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.11+0--11852314, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\build\20250516_17321406485010125886.compiler.options

Host: Intel(R) Core(TM) i7-10750H CPU @ 2.60GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.3958)
Time: Fri May 16 20:22:27 2025 US Mountain Standard Time elapsed time: 0.022842 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000245d4cb4860):  JavaThread "Unknown thread" [_thread_in_vm, id=20360, stack(0x000000880b000000,0x000000880b100000)]

Stack: [0x000000880b000000,0x000000880b100000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x687bb9]
V  [jvm.dll+0x84142a]
V  [jvm.dll+0x8430ae]
V  [jvm.dll+0x843713]
V  [jvm.dll+0x24a35f]
V  [jvm.dll+0x684989]
V  [jvm.dll+0x67923a]
V  [jvm.dll+0x30af0b]
V  [jvm.dll+0x3123b6]
V  [jvm.dll+0x361dfe]
V  [jvm.dll+0x36202f]
V  [jvm.dll+0x2e0d38]
V  [jvm.dll+0x2e1ca4]
V  [jvm.dll+0x811f21]
V  [jvm.dll+0x36fb68]
V  [jvm.dll+0x7f0896]
V  [jvm.dll+0x3f3d2f]
V  [jvm.dll+0x3f58e1]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1257d]
C  [ntdll.dll+0x5af08]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffe6c43efd8, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x00000245fa5648c0 GCTaskThread "GC Thread#0" [stack: 0x000000880b100000,0x000000880b200000] [id=41428]
  0x00000245d4d5dab0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000880b200000,0x000000880b300000] [id=55112]
  0x00000245d4d5eb80 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000880b300000,0x000000880b400000] [id=47364]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffe6bbf1907]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00000245d4cae230] Heap_lock - owner thread: 0x00000245d4cb4860

Heap address: 0x0000000602800000, size: 8152 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000602800000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)

Card table byte_map: [0x00000245e86c0000,0x00000245e96b0000] _byte_map_base: 0x00000245e56ac000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000245d4d5d1b0, (CMBitMap*) 0x00000245d4d5d1f0
 Prev Bits: [0x00000245ea6a0000, 0x00000245f2600000)
 Next Bits: [0x00000245f2600000, 0x00000245fa560000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.014 Loaded shared library D:\Android\Android Studio\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff751ae0000 - 0x00007ff751aea000 	D:\Android\Android Studio\jbr\bin\java.exe
0x00007ffe89550000 - 0x00007ffe89767000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe421c0000 - 0x00007ffe421da000 	C:\Program Files\Avast Software\Avast\aswhook.dll
0x00007ffe886a0000 - 0x00007ffe88764000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe86c10000 - 0x00007ffe86fc7000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe87040000 - 0x00007ffe87151000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe73570000 - 0x00007ffe73587000 	D:\Android\Android Studio\jbr\bin\jli.dll
0x00007ffe6e660000 - 0x00007ffe6e67b000 	D:\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffe87650000 - 0x00007ffe877ff000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe6afd0000 - 0x00007ffe6b263000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3672_none_2713b9d173822955\COMCTL32.dll
0x00007ffe86b30000 - 0x00007ffe86b56000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe87a10000 - 0x00007ffe87ab7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe88fd0000 - 0x00007ffe88ff9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe87160000 - 0x00007ffe87278000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe869d0000 - 0x00007ffe86a6a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe87280000 - 0x00007ffe872b1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe6e6d0000 - 0x00007ffe6e6dc000 	D:\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffe6d110000 - 0x00007ffe6d19d000 	D:\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffe6b900000 - 0x00007ffe6c583000 	D:\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffe89160000 - 0x00007ffe89212000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe88330000 - 0x00007ffe883d8000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe86be0000 - 0x00007ffe86c08000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe87800000 - 0x00007ffe87914000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe85710000 - 0x00007ffe8575d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffe80330000 - 0x00007ffe80364000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe5fc00000 - 0x00007ffe5fc09000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffe80ff0000 - 0x00007ffe80ffa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe88770000 - 0x00007ffe887e1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe856f0000 - 0x00007ffe85703000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffe859a0000 - 0x00007ffe859b8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe833d0000 - 0x00007ffe833da000 	D:\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffe83fb0000 - 0x00007ffe841e2000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe872c0000 - 0x00007ffe8764e000 	C:\WINDOWS\System32\combase.dll
0x00007ffe87930000 - 0x00007ffe87a07000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe780d0000 - 0x00007ffe78102000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe86b60000 - 0x00007ffe86bdb000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe804f0000 - 0x00007ffe80515000 	D:\Android\Android Studio\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Android\Android Studio\jbr\bin;C:\WINDOWS\SYSTEM32;C:\Program Files\Avast Software\Avast;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3672_none_2713b9d173822955;D:\Android\Android Studio\jbr\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\build\20250516_17321406485010125886.compiler.options
java_class_path (initial): C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.0\2c8c3a7402becca891f12739b3e9fd2dc2adbd7a\kotlin-compiler-embeddable-1.9.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.0\8ee15ef0c67dc83d874f412d84378d7f0eb50b63\kotlin-stdlib-1.9.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.0\4b3102cbbb7e1b58d9d1adf89563f00069ffa7d1\kotlin-script-runtime-1.9.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.0\186543f5e28cf8d9a0290fecc2cf34301c40a65c\kotlin-daemon-embeddable-1.9.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.9.0\cd65c21cfd1eec4d44ef09f9f52b6d9f8a720636\kotlin-stdlib-common-1.9.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8547991552                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8547991552                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Intel\Intel(R) Memory and Storage Tool\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\dotnet\;C:\Program Files (x86)\ZeroTier\One\;C:\Program Files\WireGuard\;C:\ProgramData\chocolatey\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\PuTTY\;C:\Program Files\Git\cmd;d:\cursor\resources\app\bin;%NVM_HOME%;%NVM_SYMLINK%;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;D:\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=C
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 12148K (0% of 33383428K total physical memory with 373460K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.3958)
OS uptime: 15 days 6:10 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 2 microcode 0xf8, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt
Processor Information for all 12 processors :
  Max Mhz: 2592, Current Mhz: 2592, Mhz Limit: 2592

Memory: 4k page, system-wide physical 32601M (364M free)
TotalPageFile size 93477M (AvailPageFile size 235M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 65M, peak: 576M

vm_info: OpenJDK 64-Bit Server VM (17.0.11+0--11852314) for windows-amd64 JRE (17.0.11+0--11852314), built on May 16 2024 21:29:20 by "androidbuild" with MS VC++ 16.10 / 16.11 (VS2019)

END.
