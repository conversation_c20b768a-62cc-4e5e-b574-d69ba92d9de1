  R android  color 	android.R  id 	android.R  layout 	android.R  darker_gray android.R.color  content android.R.id  simple_list_item_1 android.R.layout  SuppressLint android.annotation  Activity android.app  AlertDialog android.app  DownloadManager android.app  ActivityDmChatBinding android.app.Activity  ActivityDmRequestBinding android.app.Activity  ActivityEncryptedChatBinding android.app.Activity  AlertDialog android.app.Activity  
BackupUtil android.app.Activity  Bundle android.app.Activity  Button android.app.Activity  CharSequence android.app.Activity  ChatMessage android.app.Activity  
ChatsFragment android.app.Activity  ContactsFragment android.app.Activity  Context android.app.Activity  	DMAdapter android.app.Activity  	DMRequest android.app.Activity  DMRequestAdapter android.app.Activity  EditText android.app.Activity  Editable android.app.Activity  EncryptedChatActivity android.app.Activity  EncryptionUtil android.app.Activity  	Exception android.app.Activity  FirebaseApp android.app.Activity  FirebaseFirestore android.app.Activity  FirebaseOptions android.app.Activity  Fragment android.app.Activity  Handler android.app.Activity  InputMethodManager android.app.Activity  Int android.app.Activity  Intent android.app.Activity  KeyStoreUtil android.app.Activity  LinearLayoutManager android.app.Activity  List android.app.Activity  ListView android.app.Activity  Log android.app.Activity  Looper android.app.Activity  MainActivity android.app.Activity  R android.app.Activity  
SetOptions android.app.Activity  SettingsFragment android.app.Activity  String android.app.Activity  System android.app.Activity  TAG android.app.Activity  TextWatcher android.app.Activity  Toast android.app.Activity  
UpdateManager android.app.Activity  UsernameSetupActivity android.app.Activity  View android.app.Activity  WalletFragment android.app.Activity  adapter android.app.Activity  android android.app.Activity  androidx android.app.Activity  apply android.app.Activity  await android.app.Activity  binding android.app.Activity  checkForUpdates android.app.Activity  checkUsernameAndNavigate android.app.Activity  db android.app.Activity  findViewById android.app.Activity  finish android.app.Activity  firstOrNull android.app.Activity  getSystemService android.app.Activity  	getWINDOW android.app.Activity  	getWindow android.app.Activity  handleAccept android.app.Activity  handleChatsNavigation android.app.Activity  handleReject android.app.Activity  	hashMapOf android.app.Activity  
isNotBlank android.app.Activity  
isNotEmpty android.app.Activity  
isNullOrBlank android.app.Activity  java android.app.Activity  joinToString android.app.Activity  launch android.app.Activity  let android.app.Activity  lifecycleScope android.app.Activity  listOf android.app.Activity  listenForMessages android.app.Activity  listenToMessages android.app.Activity  loadDMRequests android.app.Activity  	lowercase android.app.Activity  
mapNotNull android.app.Activity  mapOf android.app.Activity  markChatAsRead android.app.Activity  
mutableListOf android.app.Activity  
onBackPressed android.app.Activity  onCreate android.app.Activity  onNewIntent android.app.Activity  onResume android.app.Activity  registerUsername android.app.Activity  
runOnUiThread android.app.Activity  scrollToBottom android.app.Activity  searchUserByUsername android.app.Activity  setContentView android.app.Activity  	setIntent android.app.Activity  	setWindow android.app.Activity  showAddToContactsDialog android.app.Activity  showFragment android.app.Activity  showRestoreDialog android.app.Activity  showSeedPhraseDialog android.app.Activity  sorted android.app.Activity  
startActivity android.app.Activity  startMainActivity android.app.Activity  take android.app.Activity  to android.app.Activity  
toMutableList android.app.Activity  toString android.app.Activity  trim android.app.Activity  users android.app.Activity  window android.app.Activity  Builder android.app.AlertDialog  
setMessage android.app.AlertDialog.Builder  setNegativeButton android.app.AlertDialog.Builder  setPositiveButton android.app.AlertDialog.Builder  setTitle android.app.AlertDialog.Builder  setView android.app.AlertDialog.Builder  show android.app.AlertDialog.Builder  ACTION_DOWNLOAD_COMPLETE android.app.DownloadManager  
COLUMN_STATUS android.app.DownloadManager  EXTRA_DOWNLOAD_ID android.app.DownloadManager  Query android.app.DownloadManager  Request android.app.DownloadManager  STATUS_SUCCESSFUL android.app.DownloadManager  enqueue android.app.DownloadManager  query android.app.DownloadManager  
setFilterById !android.app.DownloadManager.Query  VISIBILITY_VISIBLE #android.app.DownloadManager.Request  setAllowedOverMetered #android.app.DownloadManager.Request  setAllowedOverRoaming #android.app.DownloadManager.Request  setDescription #android.app.DownloadManager.Request  setDestinationUri #android.app.DownloadManager.Request  setNotificationVisibility #android.app.DownloadManager.Request  setTitle #android.app.DownloadManager.Request  BroadcastReceiver android.content  Context android.content  DialogInterface android.content  Intent android.content  IntentFilter android.content  SharedPreferences android.content  Context !android.content.BroadcastReceiver  DownloadManager !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  
downloadId !android.content.BroadcastReceiver  
installApk !android.content.BroadcastReceiver  removeUpdateOverlay !android.content.BroadcastReceiver  updateOverlayMessage !android.content.BroadcastReceiver  ActivityDmChatBinding android.content.Context  ActivityDmRequestBinding android.content.Context  ActivityEncryptedChatBinding android.content.Context  AlertDialog android.content.Context  
BackupUtil android.content.Context  Bundle android.content.Context  Button android.content.Context  CharSequence android.content.Context  ChatMessage android.content.Context  
ChatsFragment android.content.Context  ContactsFragment android.content.Context  Context android.content.Context  	DMAdapter android.content.Context  	DMRequest android.content.Context  DMRequestAdapter android.content.Context  DOWNLOAD_SERVICE android.content.Context  EditText android.content.Context  Editable android.content.Context  EncryptedChatActivity android.content.Context  EncryptionUtil android.content.Context  	Exception android.content.Context  FirebaseApp android.content.Context  FirebaseFirestore android.content.Context  FirebaseOptions android.content.Context  Fragment android.content.Context  Handler android.content.Context  INPUT_METHOD_SERVICE android.content.Context  InputMethodManager android.content.Context  Int android.content.Context  Intent android.content.Context  KeyStoreUtil android.content.Context  LinearLayoutManager android.content.Context  List android.content.Context  ListView android.content.Context  Log android.content.Context  Looper android.content.Context  MainActivity android.content.Context  R android.content.Context  
SetOptions android.content.Context  SettingsFragment android.content.Context  String android.content.Context  System android.content.Context  TAG android.content.Context  TextWatcher android.content.Context  Toast android.content.Context  
UpdateManager android.content.Context  UsernameSetupActivity android.content.Context  View android.content.Context  WalletFragment android.content.Context  adapter android.content.Context  android android.content.Context  androidx android.content.Context  apply android.content.Context  await android.content.Context  binding android.content.Context  checkForUpdates android.content.Context  checkUsernameAndNavigate android.content.Context  db android.content.Context  findViewById android.content.Context  finish android.content.Context  firstOrNull android.content.Context  getExternalFilesDir android.content.Context  getPACKAGEManager android.content.Context  getPACKAGEName android.content.Context  getPackageManager android.content.Context  getPackageName android.content.Context  getSystemService android.content.Context  handleAccept android.content.Context  handleChatsNavigation android.content.Context  handleReject android.content.Context  	hashMapOf android.content.Context  
isNotBlank android.content.Context  
isNotEmpty android.content.Context  
isNullOrBlank android.content.Context  java android.content.Context  joinToString android.content.Context  launch android.content.Context  let android.content.Context  lifecycleScope android.content.Context  listOf android.content.Context  listenForMessages android.content.Context  listenToMessages android.content.Context  loadDMRequests android.content.Context  	lowercase android.content.Context  
mapNotNull android.content.Context  mapOf android.content.Context  markChatAsRead android.content.Context  
mutableListOf android.content.Context  
onBackPressed android.content.Context  onCreate android.content.Context  onNewIntent android.content.Context  onResume android.content.Context  packageManager android.content.Context  packageName android.content.Context  registerReceiver android.content.Context  registerUsername android.content.Context  
runOnUiThread android.content.Context  scrollToBottom android.content.Context  searchUserByUsername android.content.Context  setContentView android.content.Context  	setIntent android.content.Context  setPackageManager android.content.Context  setPackageName android.content.Context  showAddToContactsDialog android.content.Context  showFragment android.content.Context  showRestoreDialog android.content.Context  showSeedPhraseDialog android.content.Context  sorted android.content.Context  
startActivity android.content.Context  startMainActivity android.content.Context  take android.content.Context  to android.content.Context  
toMutableList android.content.Context  toString android.content.Context  trim android.content.Context  unregisterReceiver android.content.Context  users android.content.Context  ActivityDmChatBinding android.content.ContextWrapper  ActivityDmRequestBinding android.content.ContextWrapper  ActivityEncryptedChatBinding android.content.ContextWrapper  AlertDialog android.content.ContextWrapper  
BackupUtil android.content.ContextWrapper  Bundle android.content.ContextWrapper  Button android.content.ContextWrapper  CharSequence android.content.ContextWrapper  ChatMessage android.content.ContextWrapper  
ChatsFragment android.content.ContextWrapper  ContactsFragment android.content.ContextWrapper  Context android.content.ContextWrapper  	DMAdapter android.content.ContextWrapper  	DMRequest android.content.ContextWrapper  DMRequestAdapter android.content.ContextWrapper  EditText android.content.ContextWrapper  Editable android.content.ContextWrapper  EncryptedChatActivity android.content.ContextWrapper  EncryptionUtil android.content.ContextWrapper  	Exception android.content.ContextWrapper  FirebaseApp android.content.ContextWrapper  FirebaseFirestore android.content.ContextWrapper  FirebaseOptions android.content.ContextWrapper  Fragment android.content.ContextWrapper  Handler android.content.ContextWrapper  InputMethodManager android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  KeyStoreUtil android.content.ContextWrapper  LinearLayoutManager android.content.ContextWrapper  List android.content.ContextWrapper  ListView android.content.ContextWrapper  Log android.content.ContextWrapper  Looper android.content.ContextWrapper  MainActivity android.content.ContextWrapper  R android.content.ContextWrapper  
SetOptions android.content.ContextWrapper  SettingsFragment android.content.ContextWrapper  String android.content.ContextWrapper  System android.content.ContextWrapper  TAG android.content.ContextWrapper  TextWatcher android.content.ContextWrapper  Toast android.content.ContextWrapper  
UpdateManager android.content.ContextWrapper  UsernameSetupActivity android.content.ContextWrapper  View android.content.ContextWrapper  WalletFragment android.content.ContextWrapper  adapter android.content.ContextWrapper  android android.content.ContextWrapper  androidx android.content.ContextWrapper  apply android.content.ContextWrapper  await android.content.ContextWrapper  binding android.content.ContextWrapper  checkForUpdates android.content.ContextWrapper  checkUsernameAndNavigate android.content.ContextWrapper  db android.content.ContextWrapper  findViewById android.content.ContextWrapper  finish android.content.ContextWrapper  firstOrNull android.content.ContextWrapper  getSystemService android.content.ContextWrapper  handleAccept android.content.ContextWrapper  handleChatsNavigation android.content.ContextWrapper  handleReject android.content.ContextWrapper  	hashMapOf android.content.ContextWrapper  
isNotBlank android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
isNullOrBlank android.content.ContextWrapper  java android.content.ContextWrapper  joinToString android.content.ContextWrapper  launch android.content.ContextWrapper  let android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  listOf android.content.ContextWrapper  listenForMessages android.content.ContextWrapper  listenToMessages android.content.ContextWrapper  loadDMRequests android.content.ContextWrapper  	lowercase android.content.ContextWrapper  
mapNotNull android.content.ContextWrapper  mapOf android.content.ContextWrapper  markChatAsRead android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  
onBackPressed android.content.ContextWrapper  onCreate android.content.ContextWrapper  onNewIntent android.content.ContextWrapper  onResume android.content.ContextWrapper  registerUsername android.content.ContextWrapper  
runOnUiThread android.content.ContextWrapper  scrollToBottom android.content.ContextWrapper  searchUserByUsername android.content.ContextWrapper  setContentView android.content.ContextWrapper  	setIntent android.content.ContextWrapper  showAddToContactsDialog android.content.ContextWrapper  showFragment android.content.ContextWrapper  showRestoreDialog android.content.ContextWrapper  showSeedPhraseDialog android.content.ContextWrapper  sorted android.content.ContextWrapper  
startActivity android.content.ContextWrapper  startMainActivity android.content.ContextWrapper  take android.content.ContextWrapper  to android.content.ContextWrapper  
toMutableList android.content.ContextWrapper  toString android.content.ContextWrapper  trim android.content.ContextWrapper  users android.content.ContextWrapper  dismiss android.content.DialogInterface  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  ACTION_VIEW android.content.Intent  FLAG_ACTIVITY_CLEAR_TASK android.content.Intent  FLAG_ACTIVITY_CLEAR_TOP android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  FLAG_ACTIVITY_SINGLE_TOP android.content.Intent  FLAG_GRANT_READ_URI_PERMISSION android.content.Intent  addFlags android.content.Intent  flags android.content.Intent  getFLAGS android.content.Intent  getFlags android.content.Intent  getLongExtra android.content.Intent  getStringExtra android.content.Intent  putExtra android.content.Intent  setDataAndType android.content.Intent  setFlags android.content.Intent  edit !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  PackageManager android.content.pm  getLONGVersionCode android.content.pm.PackageInfo  getLongVersionCode android.content.pm.PackageInfo  longVersionCode android.content.pm.PackageInfo  setLongVersionCode android.content.pm.PackageInfo  versionCode android.content.pm.PackageInfo  PackageInfoFlags !android.content.pm.PackageManager  getPackageInfo !android.content.pm.PackageManager  of 2android.content.pm.PackageManager.PackageInfoFlags  Cursor android.database  close android.database.Cursor  getColumnIndex android.database.Cursor  getInt android.database.Cursor  moveToFirst android.database.Cursor  Canvas android.graphics  Color android.graphics  LinearGradient android.graphics  Paint android.graphics  Path android.graphics  RadialGradient android.graphics  Shader android.graphics  Typeface android.graphics  	drawColor android.graphics.Canvas  drawPath android.graphics.Canvas  drawText android.graphics.Canvas  BLACK android.graphics.Color  WHITE android.graphics.Color  
parseColor android.graphics.Color  Color android.graphics.Paint  Paint android.graphics.Paint  Style android.graphics.Paint  Typeface android.graphics.Paint  alpha android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  getALPHA android.graphics.Paint  getAPPLY android.graphics.Paint  getAlpha android.graphics.Paint  getApply android.graphics.Paint  getCOLOR android.graphics.Paint  getColor android.graphics.Paint  getISAntiAlias android.graphics.Paint  getIsAntiAlias android.graphics.Paint  	getSHADER android.graphics.Paint  getSTYLE android.graphics.Paint  	getShader android.graphics.Paint  getStyle android.graphics.Paint  getTEXTSize android.graphics.Paint  getTYPEFACE android.graphics.Paint  getTextSize android.graphics.Paint  getTypeface android.graphics.Paint  isAntiAlias android.graphics.Paint  setAlpha android.graphics.Paint  setAntiAlias android.graphics.Paint  setColor android.graphics.Paint  	setShader android.graphics.Paint  setStyle android.graphics.Paint  setTextSize android.graphics.Paint  setTypeface android.graphics.Paint  shader android.graphics.Paint  style android.graphics.Paint  textSize android.graphics.Paint  typeface android.graphics.Paint  FILL android.graphics.Paint.Style  close android.graphics.Path  lineTo android.graphics.Path  moveTo android.graphics.Path  quadTo android.graphics.Path  TileMode android.graphics.Shader  CLAMP  android.graphics.Shader.TileMode  BOLD android.graphics.Typeface  	MONOSPACE android.graphics.Typeface  NORMAL android.graphics.Typeface  Uri android.net  fromFile android.net.Uri  parse android.net.Uri  Build 
android.os  Bundle 
android.os  Environment 
android.os  Handler 
android.os  IBinder 
android.os  Looper 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  N android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  equals android.os.Bundle  DIRECTORY_DOWNLOADS android.os.Environment  postDelayed android.os.Handler  
getMainLooper android.os.Looper  KeyGenParameterSpec android.security.keystore  
KeyProperties android.security.keystore  Builder -android.security.keystore.KeyGenParameterSpec  build 5android.security.keystore.KeyGenParameterSpec.Builder  
setBlockModes 5android.security.keystore.KeyGenParameterSpec.Builder  setEncryptionPaddings 5android.security.keystore.KeyGenParameterSpec.Builder  
setKeySize 5android.security.keystore.KeyGenParameterSpec.Builder  BLOCK_MODE_ECB 'android.security.keystore.KeyProperties  ENCRYPTION_PADDING_RSA_PKCS1 'android.security.keystore.KeyProperties  KEY_ALGORITHM_RSA 'android.security.keystore.KeyProperties  PURPOSE_DECRYPT 'android.security.keystore.KeyProperties  PURPOSE_ENCRYPT 'android.security.keystore.KeyProperties  Editable android.text  TextWatcher android.text  getISNullOrBlank android.text.Editable  getIsNullOrBlank android.text.Editable  getTOString android.text.Editable  getToString android.text.Editable  
isNullOrBlank android.text.Editable  toString android.text.Editable  
DateFormat android.text.format  format android.text.format.DateFormat  AttributeSet android.util  Base64 android.util  Log android.util  DEFAULT android.util.Base64  NO_WRAP android.util.Base64  decode android.util.Base64  encodeToString android.util.Base64  d android.util.Log  e android.util.Log  Gravity android.view  LayoutInflater android.view  MenuItem android.view  View android.view  	ViewGroup android.view  ActivityDmChatBinding  android.view.ContextThemeWrapper  ActivityDmRequestBinding  android.view.ContextThemeWrapper  ActivityEncryptedChatBinding  android.view.ContextThemeWrapper  AlertDialog  android.view.ContextThemeWrapper  
BackupUtil  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  Button  android.view.ContextThemeWrapper  CharSequence  android.view.ContextThemeWrapper  ChatMessage  android.view.ContextThemeWrapper  
ChatsFragment  android.view.ContextThemeWrapper  ContactsFragment  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  	DMAdapter  android.view.ContextThemeWrapper  	DMRequest  android.view.ContextThemeWrapper  DMRequestAdapter  android.view.ContextThemeWrapper  EditText  android.view.ContextThemeWrapper  Editable  android.view.ContextThemeWrapper  EncryptedChatActivity  android.view.ContextThemeWrapper  EncryptionUtil  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  FirebaseApp  android.view.ContextThemeWrapper  FirebaseFirestore  android.view.ContextThemeWrapper  FirebaseOptions  android.view.ContextThemeWrapper  Fragment  android.view.ContextThemeWrapper  Handler  android.view.ContextThemeWrapper  InputMethodManager  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  KeyStoreUtil  android.view.ContextThemeWrapper  LinearLayoutManager  android.view.ContextThemeWrapper  List  android.view.ContextThemeWrapper  ListView  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  Looper  android.view.ContextThemeWrapper  MainActivity  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  
SetOptions  android.view.ContextThemeWrapper  SettingsFragment  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  System  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  TextWatcher  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  
UpdateManager  android.view.ContextThemeWrapper  UsernameSetupActivity  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  WalletFragment  android.view.ContextThemeWrapper  adapter  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  androidx  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  await  android.view.ContextThemeWrapper  binding  android.view.ContextThemeWrapper  checkForUpdates  android.view.ContextThemeWrapper  checkUsernameAndNavigate  android.view.ContextThemeWrapper  db  android.view.ContextThemeWrapper  findViewById  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  firstOrNull  android.view.ContextThemeWrapper  getSystemService  android.view.ContextThemeWrapper  handleAccept  android.view.ContextThemeWrapper  handleChatsNavigation  android.view.ContextThemeWrapper  handleReject  android.view.ContextThemeWrapper  	hashMapOf  android.view.ContextThemeWrapper  
isNotBlank  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  
isNullOrBlank  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  joinToString  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  listenForMessages  android.view.ContextThemeWrapper  listenToMessages  android.view.ContextThemeWrapper  loadDMRequests  android.view.ContextThemeWrapper  	lowercase  android.view.ContextThemeWrapper  
mapNotNull  android.view.ContextThemeWrapper  mapOf  android.view.ContextThemeWrapper  markChatAsRead  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  
onBackPressed  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  onNewIntent  android.view.ContextThemeWrapper  onResume  android.view.ContextThemeWrapper  registerUsername  android.view.ContextThemeWrapper  
runOnUiThread  android.view.ContextThemeWrapper  scrollToBottom  android.view.ContextThemeWrapper  searchUserByUsername  android.view.ContextThemeWrapper  setContentView  android.view.ContextThemeWrapper  	setIntent  android.view.ContextThemeWrapper  showAddToContactsDialog  android.view.ContextThemeWrapper  showFragment  android.view.ContextThemeWrapper  showRestoreDialog  android.view.ContextThemeWrapper  showSeedPhraseDialog  android.view.ContextThemeWrapper  sorted  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  startMainActivity  android.view.ContextThemeWrapper  take  android.view.ContextThemeWrapper  to  android.view.ContextThemeWrapper  
toMutableList  android.view.ContextThemeWrapper  toString  android.view.ContextThemeWrapper  trim  android.view.ContextThemeWrapper  users  android.view.ContextThemeWrapper  CENTER android.view.Gravity  TOP android.view.Gravity  from android.view.LayoutInflater  inflate android.view.LayoutInflater  	getITEMId android.view.MenuItem  	getItemId android.view.MenuItem  itemId android.view.MenuItem  	setItemId android.view.MenuItem  AttributeSet android.view.View  Canvas android.view.View  Char android.view.View  Color android.view.View  Context android.view.View  Float android.view.View  
FloatArray android.view.View  GONE android.view.View  Int android.view.View  JvmOverloads android.view.View  LAYER_TYPE_HARDWARE android.view.View  LinearGradient android.view.View  Log android.view.View  Math android.view.View  MutableList android.view.View  Paint android.view.View  Pair android.view.View  Path android.view.View  RadialGradient android.view.View  Random android.view.View  Shader android.view.View  System android.view.View  TEXT_ALIGNMENT_VIEW_END android.view.View  TEXT_ALIGNMENT_VIEW_START android.view.View  Typeface android.view.View  VISIBLE android.view.View  addTextChangedListener android.view.View  addView android.view.View  apply android.view.View  	canGoBack android.view.View  
coerceAtLeast android.view.View  context android.view.View  cos android.view.View  destroy android.view.View  drawPolygon android.view.View  findViewById android.view.View  floatArrayOf android.view.View  
getCONTEXT android.view.View  
getContext android.view.View  
getVISIBILITY android.view.View  
getVisibility android.view.View  goBack android.view.View  
intArrayOf android.view.View  
invalidate android.view.View  
isNotEmpty android.view.View  loadUrl android.view.View  min android.view.View  minusAssign android.view.View  
mutableListOf android.view.View  onDraw android.view.View  onPause android.view.View  onResume android.view.View  
onSizeChanged android.view.View  
plusAssign android.view.View  post android.view.View  preventExcessiveOverlap android.view.View  random android.view.View  
removeView android.view.View  
requestLayout android.view.View  scrollToPosition android.view.View  setBackgroundColor android.view.View  setBackgroundResource android.view.View  
setContext android.view.View  setHasFixedSize android.view.View  setImageResource android.view.View  setLayerType android.view.View  setOnClickListener android.view.View  setOnItemSelectedListener android.view.View  setOnLongClickListener android.view.View  
setPadding android.view.View  setText android.view.View  setTextColor android.view.View  setTypeface android.view.View  
setVisibility android.view.View  sin android.view.View  smoothScrollToPosition android.view.View  until android.view.View  
updateColumns android.view.View  updatePolygons android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  <SAM-CONSTRUCTOR> %android.view.View.OnLongClickListener  LayoutParams android.view.ViewGroup  MarginLayoutParams android.view.ViewGroup  addView android.view.ViewGroup  	canGoBack android.view.ViewGroup  context android.view.ViewGroup  destroy android.view.ViewGroup  
getCONTEXT android.view.ViewGroup  
getContext android.view.ViewGroup  goBack android.view.ViewGroup  loadUrl android.view.ViewGroup  onPause android.view.ViewGroup  onResume android.view.ViewGroup  post android.view.ViewGroup  
removeView android.view.ViewGroup  scrollToPosition android.view.ViewGroup  setBackgroundColor android.view.ViewGroup  setBackgroundResource android.view.ViewGroup  
setContext android.view.ViewGroup  setHasFixedSize android.view.ViewGroup  setOnClickListener android.view.ViewGroup  setOnItemSelectedListener android.view.ViewGroup  setOnLongClickListener android.view.ViewGroup  smoothScrollToPosition android.view.ViewGroup  MATCH_PARENT #android.view.ViewGroup.LayoutParams  WRAP_CONTENT #android.view.ViewGroup.LayoutParams  apply #android.view.ViewGroup.LayoutParams  apply )android.view.ViewGroup.MarginLayoutParams  getAPPLY )android.view.ViewGroup.MarginLayoutParams  getApply )android.view.ViewGroup.MarginLayoutParams  getMARGINEnd )android.view.ViewGroup.MarginLayoutParams  getMARGINStart )android.view.ViewGroup.MarginLayoutParams  getMarginEnd )android.view.ViewGroup.MarginLayoutParams  getMarginStart )android.view.ViewGroup.MarginLayoutParams  	marginEnd )android.view.ViewGroup.MarginLayoutParams  marginStart )android.view.ViewGroup.MarginLayoutParams  setMarginEnd )android.view.ViewGroup.MarginLayoutParams  setMarginStart )android.view.ViewGroup.MarginLayoutParams  	decorView android.view.Window  getDECORView android.view.Window  getDecorView android.view.Window  setDecorView android.view.Window  InputMethodManager android.view.inputmethod  hideSoftInputFromWindow +android.view.inputmethod.InputMethodManager  WebChromeClient android.webkit  WebSettings android.webkit  WebView android.webkit  
WebViewClient android.webkit  Int android.webkit.WebChromeClient  View android.webkit.WebChromeClient  WebView android.webkit.WebChromeClient  onProgressChanged android.webkit.WebChromeClient  progressBar android.webkit.WebChromeClient  LOAD_DEFAULT android.webkit.WebSettings   MIXED_CONTENT_COMPATIBILITY_MODE android.webkit.WebSettings  allowContentAccess android.webkit.WebSettings  allowFileAccess android.webkit.WebSettings  builtInZoomControls android.webkit.WebSettings  	cacheMode android.webkit.WebSettings  databaseEnabled android.webkit.WebSettings  displayZoomControls android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  getALLOWContentAccess android.webkit.WebSettings  getALLOWFileAccess android.webkit.WebSettings  getAllowContentAccess android.webkit.WebSettings  getAllowFileAccess android.webkit.WebSettings  getBUILTInZoomControls android.webkit.WebSettings  getBuiltInZoomControls android.webkit.WebSettings  getCACHEMode android.webkit.WebSettings  getCacheMode android.webkit.WebSettings  getDATABASEEnabled android.webkit.WebSettings  getDISPLAYZoomControls android.webkit.WebSettings  getDOMStorageEnabled android.webkit.WebSettings  getDatabaseEnabled android.webkit.WebSettings  getDisplayZoomControls android.webkit.WebSettings  getDomStorageEnabled android.webkit.WebSettings  (getJAVAScriptCanOpenWindowsAutomatically android.webkit.WebSettings  getJAVAScriptEnabled android.webkit.WebSettings  (getJavaScriptCanOpenWindowsAutomatically android.webkit.WebSettings  getJavaScriptEnabled android.webkit.WebSettings  getLOADWithOverviewMode android.webkit.WebSettings  getLoadWithOverviewMode android.webkit.WebSettings  getMIXEDContentMode android.webkit.WebSettings  getMixedContentMode android.webkit.WebSettings  getUSERAgentString android.webkit.WebSettings  getUSEWideViewPort android.webkit.WebSettings  getUseWideViewPort android.webkit.WebSettings  getUserAgentString android.webkit.WebSettings  %javaScriptCanOpenWindowsAutomatically android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  loadWithOverviewMode android.webkit.WebSettings  mixedContentMode android.webkit.WebSettings  setAllowContentAccess android.webkit.WebSettings  setAllowFileAccess android.webkit.WebSettings  setBuiltInZoomControls android.webkit.WebSettings  setCacheMode android.webkit.WebSettings  setDatabaseEnabled android.webkit.WebSettings  setDisplayZoomControls android.webkit.WebSettings  setDomStorageEnabled android.webkit.WebSettings  (setJavaScriptCanOpenWindowsAutomatically android.webkit.WebSettings  setJavaScriptEnabled android.webkit.WebSettings  setLoadWithOverviewMode android.webkit.WebSettings  setMixedContentMode android.webkit.WebSettings  setSupportMultipleWindows android.webkit.WebSettings  setSupportZoom android.webkit.WebSettings  setUseWideViewPort android.webkit.WebSettings  setUserAgentString android.webkit.WebSettings  useWideViewPort android.webkit.WebSettings  userAgentString android.webkit.WebSettings  	canGoBack android.webkit.WebView  destroy android.webkit.WebView  getSETTINGS android.webkit.WebView  getSettings android.webkit.WebView  getWEBChromeClient android.webkit.WebView  getWEBViewClient android.webkit.WebView  getWebChromeClient android.webkit.WebView  getWebViewClient android.webkit.WebView  goBack android.webkit.WebView  loadUrl android.webkit.WebView  onPause android.webkit.WebView  onResume android.webkit.WebView  setSettings android.webkit.WebView  setWebChromeClient android.webkit.WebView  setWebViewClient android.webkit.WebView  settings android.webkit.WebView  webChromeClient android.webkit.WebView  
webViewClient android.webkit.WebView  Boolean android.webkit.WebViewClient  String android.webkit.WebViewClient  View android.webkit.WebViewClient  WebView android.webkit.WebViewClient  contains android.webkit.WebViewClient  onPageFinished android.webkit.WebViewClient  progressBar android.webkit.WebViewClient  ArrayAdapter android.widget  Button android.widget  EditText android.widget  FrameLayout android.widget  	ImageView android.widget  LinearLayout android.widget  ListAdapter android.widget  ListView android.widget  ProgressBar android.widget  TextView android.widget  Toast android.widget  smoothScrollToPosition android.widget.AbsListView  	canGoBack android.widget.AbsoluteLayout  destroy android.widget.AbsoluteLayout  goBack android.widget.AbsoluteLayout  loadUrl android.widget.AbsoluteLayout  onPause android.widget.AbsoluteLayout  onResume android.widget.AbsoluteLayout  smoothScrollToPosition android.widget.AdapterView  notifyDataSetChanged android.widget.ArrayAdapter  notifyDataSetChanged android.widget.BaseAdapter  setOnClickListener android.widget.Button  addTextChangedListener android.widget.EditText  getHINT android.widget.EditText  getHint android.widget.EditText  getTEXT android.widget.EditText  getText android.widget.EditText  getWINDOWToken android.widget.EditText  getWindowToken android.widget.EditText  hint android.widget.EditText  setHint android.widget.EditText  setText android.widget.EditText  setWindowToken android.widget.EditText  text android.widget.EditText  windowToken android.widget.EditText  LayoutParams android.widget.FrameLayout  setOnClickListener android.widget.FrameLayout  setOnItemSelectedListener android.widget.FrameLayout  setOnLongClickListener android.widget.FrameLayout  Gravity 'android.widget.FrameLayout.LayoutParams  apply 'android.widget.FrameLayout.LayoutParams  getAPPLY 'android.widget.FrameLayout.LayoutParams  getApply 'android.widget.FrameLayout.LayoutParams  gravity 'android.widget.FrameLayout.LayoutParams  getISEnabled android.widget.ImageButton  getIsEnabled android.widget.ImageButton  	isEnabled android.widget.ImageButton  
setEnabled android.widget.ImageButton  setOnClickListener android.widget.ImageButton  setImageResource android.widget.ImageView  setOnClickListener android.widget.ImageView  getLAYOUTParams android.widget.LinearLayout  getLayoutParams android.widget.LinearLayout  
getVISIBILITY android.widget.LinearLayout  
getVisibility android.widget.LinearLayout  layoutParams android.widget.LinearLayout  setBackgroundColor android.widget.LinearLayout  setBackgroundResource android.widget.LinearLayout  setLayoutParams android.widget.LinearLayout  
setVisibility android.widget.LinearLayout  
visibility android.widget.LinearLayout  adapter android.widget.ListView  
getADAPTER android.widget.ListView  
getAdapter android.widget.ListView  
setAdapter android.widget.ListView  smoothScrollToPosition android.widget.ListView  getPROGRESS android.widget.ProgressBar  getProgress android.widget.ProgressBar  
getVISIBILITY android.widget.ProgressBar  
getVisibility android.widget.ProgressBar  progress android.widget.ProgressBar  setProgress android.widget.ProgressBar  
setVisibility android.widget.ProgressBar  
visibility android.widget.ProgressBar  Color android.widget.TextView  Gravity android.widget.TextView  View android.widget.TextView  addTextChangedListener android.widget.TextView  android android.widget.TextView  apply android.widget.TextView  equals android.widget.TextView  
getANDROID android.widget.TextView  getAPPLY android.widget.TextView  
getAndroid android.widget.TextView  getApply android.widget.TextView  
getGRAVITY android.widget.TextView  
getGravity android.widget.TextView  getTEXT android.widget.TextView  getTEXTAlignment android.widget.TextView  getTEXTSize android.widget.TextView  getTYPEFACE android.widget.TextView  getText android.widget.TextView  getTextAlignment android.widget.TextView  getTextSize android.widget.TextView  getTypeface android.widget.TextView  
getVISIBILITY android.widget.TextView  
getVisibility android.widget.TextView  gravity android.widget.TextView  setBackgroundColor android.widget.TextView  
setGravity android.widget.TextView  setOnClickListener android.widget.TextView  setOnLongClickListener android.widget.TextView  
setPadding android.widget.TextView  setText android.widget.TextView  setTextAlignment android.widget.TextView  setTextColor android.widget.TextView  setTextSize android.widget.TextView  setTypeface android.widget.TextView  
setVisibility android.widget.TextView  text android.widget.TextView  
textAlignment android.widget.TextView  textSize android.widget.TextView  typeface android.widget.TextView  
visibility android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ActivityDmChatBinding #androidx.activity.ComponentActivity  ActivityDmRequestBinding #androidx.activity.ComponentActivity  ActivityEncryptedChatBinding #androidx.activity.ComponentActivity  AlertDialog #androidx.activity.ComponentActivity  
BackupUtil #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  CharSequence #androidx.activity.ComponentActivity  ChatMessage #androidx.activity.ComponentActivity  
ChatsFragment #androidx.activity.ComponentActivity  ContactsFragment #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  	DMAdapter #androidx.activity.ComponentActivity  	DMRequest #androidx.activity.ComponentActivity  DMRequestAdapter #androidx.activity.ComponentActivity  EditText #androidx.activity.ComponentActivity  Editable #androidx.activity.ComponentActivity  EncryptedChatActivity #androidx.activity.ComponentActivity  EncryptionUtil #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  FirebaseApp #androidx.activity.ComponentActivity  FirebaseFirestore #androidx.activity.ComponentActivity  FirebaseOptions #androidx.activity.ComponentActivity  Fragment #androidx.activity.ComponentActivity  Handler #androidx.activity.ComponentActivity  InputMethodManager #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  KeyStoreUtil #androidx.activity.ComponentActivity  LinearLayoutManager #androidx.activity.ComponentActivity  List #androidx.activity.ComponentActivity  ListView #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  Looper #androidx.activity.ComponentActivity  MainActivity #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  
SetOptions #androidx.activity.ComponentActivity  SettingsFragment #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  System #androidx.activity.ComponentActivity  TAG #androidx.activity.ComponentActivity  TextWatcher #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  
UpdateManager #androidx.activity.ComponentActivity  UsernameSetupActivity #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  WalletFragment #androidx.activity.ComponentActivity  adapter #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  androidx #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  await #androidx.activity.ComponentActivity  binding #androidx.activity.ComponentActivity  checkForUpdates #androidx.activity.ComponentActivity  checkUsernameAndNavigate #androidx.activity.ComponentActivity  db #androidx.activity.ComponentActivity  findViewById #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  firstOrNull #androidx.activity.ComponentActivity  getSystemService #androidx.activity.ComponentActivity  handleAccept #androidx.activity.ComponentActivity  handleChatsNavigation #androidx.activity.ComponentActivity  handleReject #androidx.activity.ComponentActivity  	hashMapOf #androidx.activity.ComponentActivity  
isNotBlank #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  
isNullOrBlank #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  joinToString #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  listOf #androidx.activity.ComponentActivity  listenForMessages #androidx.activity.ComponentActivity  listenToMessages #androidx.activity.ComponentActivity  loadDMRequests #androidx.activity.ComponentActivity  	lowercase #androidx.activity.ComponentActivity  
mapNotNull #androidx.activity.ComponentActivity  mapOf #androidx.activity.ComponentActivity  markChatAsRead #androidx.activity.ComponentActivity  
mutableListOf #androidx.activity.ComponentActivity  
onBackPressed #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  onNewIntent #androidx.activity.ComponentActivity  onResume #androidx.activity.ComponentActivity  registerUsername #androidx.activity.ComponentActivity  scrollToBottom #androidx.activity.ComponentActivity  searchUserByUsername #androidx.activity.ComponentActivity  setContentView #androidx.activity.ComponentActivity  	setIntent #androidx.activity.ComponentActivity  showAddToContactsDialog #androidx.activity.ComponentActivity  showFragment #androidx.activity.ComponentActivity  showRestoreDialog #androidx.activity.ComponentActivity  showSeedPhraseDialog #androidx.activity.ComponentActivity  sorted #androidx.activity.ComponentActivity  
startActivity #androidx.activity.ComponentActivity  startMainActivity #androidx.activity.ComponentActivity  take #androidx.activity.ComponentActivity  to #androidx.activity.ComponentActivity  
toMutableList #androidx.activity.ComponentActivity  toString #androidx.activity.ComponentActivity  trim #androidx.activity.ComponentActivity  users #androidx.activity.ComponentActivity  AlertDialog androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  Builder "androidx.appcompat.app.AlertDialog  
setMessage *androidx.appcompat.app.AlertDialog.Builder  setNegativeButton *androidx.appcompat.app.AlertDialog.Builder  setPositiveButton *androidx.appcompat.app.AlertDialog.Builder  setTitle *androidx.appcompat.app.AlertDialog.Builder  setView *androidx.appcompat.app.AlertDialog.Builder  show *androidx.appcompat.app.AlertDialog.Builder  ActivityDmChatBinding (androidx.appcompat.app.AppCompatActivity  ActivityDmRequestBinding (androidx.appcompat.app.AppCompatActivity  ActivityEncryptedChatBinding (androidx.appcompat.app.AppCompatActivity  AlertDialog (androidx.appcompat.app.AppCompatActivity  
BackupUtil (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  Button (androidx.appcompat.app.AppCompatActivity  CharSequence (androidx.appcompat.app.AppCompatActivity  ChatMessage (androidx.appcompat.app.AppCompatActivity  
ChatsFragment (androidx.appcompat.app.AppCompatActivity  ContactsFragment (androidx.appcompat.app.AppCompatActivity  Context (androidx.appcompat.app.AppCompatActivity  	DMAdapter (androidx.appcompat.app.AppCompatActivity  	DMRequest (androidx.appcompat.app.AppCompatActivity  DMRequestAdapter (androidx.appcompat.app.AppCompatActivity  EditText (androidx.appcompat.app.AppCompatActivity  Editable (androidx.appcompat.app.AppCompatActivity  EncryptedChatActivity (androidx.appcompat.app.AppCompatActivity  EncryptionUtil (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  FirebaseApp (androidx.appcompat.app.AppCompatActivity  FirebaseFirestore (androidx.appcompat.app.AppCompatActivity  FirebaseOptions (androidx.appcompat.app.AppCompatActivity  Fragment (androidx.appcompat.app.AppCompatActivity  Handler (androidx.appcompat.app.AppCompatActivity  InputMethodManager (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  KeyStoreUtil (androidx.appcompat.app.AppCompatActivity  LinearLayoutManager (androidx.appcompat.app.AppCompatActivity  List (androidx.appcompat.app.AppCompatActivity  ListView (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  Looper (androidx.appcompat.app.AppCompatActivity  MainActivity (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  
SetOptions (androidx.appcompat.app.AppCompatActivity  SettingsFragment (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  System (androidx.appcompat.app.AppCompatActivity  TAG (androidx.appcompat.app.AppCompatActivity  TextWatcher (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  
UpdateManager (androidx.appcompat.app.AppCompatActivity  UsernameSetupActivity (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  WalletFragment (androidx.appcompat.app.AppCompatActivity  adapter (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  androidx (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  await (androidx.appcompat.app.AppCompatActivity  binding (androidx.appcompat.app.AppCompatActivity  checkForUpdates (androidx.appcompat.app.AppCompatActivity  checkUsernameAndNavigate (androidx.appcompat.app.AppCompatActivity  db (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  finish (androidx.appcompat.app.AppCompatActivity  firstOrNull (androidx.appcompat.app.AppCompatActivity  getSystemService (androidx.appcompat.app.AppCompatActivity  handleAccept (androidx.appcompat.app.AppCompatActivity  handleChatsNavigation (androidx.appcompat.app.AppCompatActivity  handleReject (androidx.appcompat.app.AppCompatActivity  	hashMapOf (androidx.appcompat.app.AppCompatActivity  
isNotBlank (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  
isNullOrBlank (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  joinToString (androidx.appcompat.app.AppCompatActivity  launch (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  lifecycleScope (androidx.appcompat.app.AppCompatActivity  listOf (androidx.appcompat.app.AppCompatActivity  listenForMessages (androidx.appcompat.app.AppCompatActivity  listenToMessages (androidx.appcompat.app.AppCompatActivity  loadDMRequests (androidx.appcompat.app.AppCompatActivity  	lowercase (androidx.appcompat.app.AppCompatActivity  
mapNotNull (androidx.appcompat.app.AppCompatActivity  mapOf (androidx.appcompat.app.AppCompatActivity  markChatAsRead (androidx.appcompat.app.AppCompatActivity  
mutableListOf (androidx.appcompat.app.AppCompatActivity  
onBackPressed (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  onNewIntent (androidx.appcompat.app.AppCompatActivity  onResume (androidx.appcompat.app.AppCompatActivity  registerUsername (androidx.appcompat.app.AppCompatActivity  scrollToBottom (androidx.appcompat.app.AppCompatActivity  searchUserByUsername (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  	setIntent (androidx.appcompat.app.AppCompatActivity  showAddToContactsDialog (androidx.appcompat.app.AppCompatActivity  showFragment (androidx.appcompat.app.AppCompatActivity  showRestoreDialog (androidx.appcompat.app.AppCompatActivity  showSeedPhraseDialog (androidx.appcompat.app.AppCompatActivity  sorted (androidx.appcompat.app.AppCompatActivity  
startActivity (androidx.appcompat.app.AppCompatActivity  startMainActivity (androidx.appcompat.app.AppCompatActivity  take (androidx.appcompat.app.AppCompatActivity  to (androidx.appcompat.app.AppCompatActivity  
toMutableList (androidx.appcompat.app.AppCompatActivity  toString (androidx.appcompat.app.AppCompatActivity  trim (androidx.appcompat.app.AppCompatActivity  users (androidx.appcompat.app.AppCompatActivity  CardView androidx.cardview.widget  setOnClickListener !androidx.cardview.widget.CardView  setOnLongClickListener !androidx.cardview.widget.CardView  ConstraintLayout  androidx.constraintlayout.widget  setOnClickListener 1androidx.constraintlayout.widget.ConstraintLayout  CoordinatorLayout !androidx.coordinatorlayout.widget  ActivityDmChatBinding #androidx.core.app.ComponentActivity  ActivityDmRequestBinding #androidx.core.app.ComponentActivity  ActivityEncryptedChatBinding #androidx.core.app.ComponentActivity  AlertDialog #androidx.core.app.ComponentActivity  
BackupUtil #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  CharSequence #androidx.core.app.ComponentActivity  ChatMessage #androidx.core.app.ComponentActivity  
ChatsFragment #androidx.core.app.ComponentActivity  ContactsFragment #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  	DMAdapter #androidx.core.app.ComponentActivity  	DMRequest #androidx.core.app.ComponentActivity  DMRequestAdapter #androidx.core.app.ComponentActivity  EditText #androidx.core.app.ComponentActivity  Editable #androidx.core.app.ComponentActivity  EncryptedChatActivity #androidx.core.app.ComponentActivity  EncryptionUtil #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  FirebaseApp #androidx.core.app.ComponentActivity  FirebaseFirestore #androidx.core.app.ComponentActivity  FirebaseOptions #androidx.core.app.ComponentActivity  Fragment #androidx.core.app.ComponentActivity  Handler #androidx.core.app.ComponentActivity  InputMethodManager #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  KeyStoreUtil #androidx.core.app.ComponentActivity  LinearLayoutManager #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  ListView #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  Looper #androidx.core.app.ComponentActivity  MainActivity #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  
SetOptions #androidx.core.app.ComponentActivity  SettingsFragment #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  System #androidx.core.app.ComponentActivity  TAG #androidx.core.app.ComponentActivity  TextWatcher #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  
UpdateManager #androidx.core.app.ComponentActivity  UsernameSetupActivity #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  WalletFragment #androidx.core.app.ComponentActivity  adapter #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  androidx #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  await #androidx.core.app.ComponentActivity  binding #androidx.core.app.ComponentActivity  checkForUpdates #androidx.core.app.ComponentActivity  checkUsernameAndNavigate #androidx.core.app.ComponentActivity  db #androidx.core.app.ComponentActivity  findViewById #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  firstOrNull #androidx.core.app.ComponentActivity  getSystemService #androidx.core.app.ComponentActivity  handleAccept #androidx.core.app.ComponentActivity  handleChatsNavigation #androidx.core.app.ComponentActivity  handleReject #androidx.core.app.ComponentActivity  	hashMapOf #androidx.core.app.ComponentActivity  
isNotBlank #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  
isNullOrBlank #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  joinToString #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  listOf #androidx.core.app.ComponentActivity  listenForMessages #androidx.core.app.ComponentActivity  listenToMessages #androidx.core.app.ComponentActivity  loadDMRequests #androidx.core.app.ComponentActivity  	lowercase #androidx.core.app.ComponentActivity  
mapNotNull #androidx.core.app.ComponentActivity  mapOf #androidx.core.app.ComponentActivity  markChatAsRead #androidx.core.app.ComponentActivity  
mutableListOf #androidx.core.app.ComponentActivity  
onBackPressed #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  onNewIntent #androidx.core.app.ComponentActivity  onResume #androidx.core.app.ComponentActivity  registerUsername #androidx.core.app.ComponentActivity  scrollToBottom #androidx.core.app.ComponentActivity  searchUserByUsername #androidx.core.app.ComponentActivity  setContentView #androidx.core.app.ComponentActivity  	setIntent #androidx.core.app.ComponentActivity  showAddToContactsDialog #androidx.core.app.ComponentActivity  showFragment #androidx.core.app.ComponentActivity  showRestoreDialog #androidx.core.app.ComponentActivity  showSeedPhraseDialog #androidx.core.app.ComponentActivity  sorted #androidx.core.app.ComponentActivity  
startActivity #androidx.core.app.ComponentActivity  startMainActivity #androidx.core.app.ComponentActivity  take #androidx.core.app.ComponentActivity  to #androidx.core.app.ComponentActivity  
toMutableList #androidx.core.app.ComponentActivity  toString #androidx.core.app.ComponentActivity  trim #androidx.core.app.ComponentActivity  users #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  FileProvider androidx.core.content  getColor #androidx.core.content.ContextCompat  
getUriForFile "androidx.core.content.FileProvider  Fragment androidx.fragment.app  FragmentManager androidx.fragment.app  FragmentTransaction androidx.fragment.app  AlertDialog androidx.fragment.app.Fragment  
BackupUtil androidx.fragment.app.Fragment  Boolean androidx.fragment.app.Fragment  Bundle androidx.fragment.app.Fragment  CharSequence androidx.fragment.app.Fragment  ChatListAdapter androidx.fragment.app.Fragment  ChatMessage androidx.fragment.app.Fragment  Color androidx.fragment.app.Fragment  DMChatActivity androidx.fragment.app.Fragment  Editable androidx.fragment.app.Fragment  EncryptionUtil androidx.fragment.app.Fragment  	Exception androidx.fragment.app.Fragment  FirebaseFirestore androidx.fragment.app.Fragment  FragmentChatsBinding androidx.fragment.app.Fragment  FragmentContactsBinding androidx.fragment.app.Fragment  FragmentGlobalChatBinding androidx.fragment.app.Fragment  FragmentWalletBinding androidx.fragment.app.Fragment  GlobalChatFragment androidx.fragment.app.Fragment  Int androidx.fragment.app.Fragment  Intent androidx.fragment.app.Fragment  KeyStoreUtil androidx.fragment.app.Fragment  LayoutInflater androidx.fragment.app.Fragment  LinearLayoutManager androidx.fragment.app.Fragment  List androidx.fragment.app.Fragment  ListenerRegistration androidx.fragment.app.Fragment  Map androidx.fragment.app.Fragment  Number androidx.fragment.app.Fragment  Pair androidx.fragment.app.Fragment  ProgressBar androidx.fragment.app.Fragment  PublicChatAdapter androidx.fragment.app.Fragment  R androidx.fragment.app.Fragment  SimpleContactsAdapter androidx.fragment.app.Fragment  String androidx.fragment.app.Fragment  SuppressLint androidx.fragment.app.Fragment  System androidx.fragment.app.Fragment  TextWatcher androidx.fragment.app.Fragment  Toast androidx.fragment.app.Fragment  View androidx.fragment.app.Fragment  	ViewGroup androidx.fragment.app.Fragment  WebChromeClient androidx.fragment.app.Fragment  WebSettings androidx.fragment.app.Fragment  WebView androidx.fragment.app.Fragment  
WebViewClient androidx.fragment.app.Fragment  
acceptChat androidx.fragment.app.Fragment  android androidx.fragment.app.Fragment  any androidx.fragment.app.Fragment  	canGoBack androidx.fragment.app.Fragment  contains androidx.fragment.app.Fragment  context androidx.fragment.app.Fragment  
deleteChat androidx.fragment.app.Fragment  
deleteContact androidx.fragment.app.Fragment  	emptyList androidx.fragment.app.Fragment  
fetchChatList androidx.fragment.app.Fragment  fetchContactsAndIncomingDMs androidx.fragment.app.Fragment  filterIsInstance androidx.fragment.app.Fragment  firstOrNull androidx.fragment.app.Fragment  get androidx.fragment.app.Fragment  getLET androidx.fragment.app.Fragment  getLet androidx.fragment.app.Fragment  goBack androidx.fragment.app.Fragment  
isInitialized androidx.fragment.app.Fragment  
isNotBlank androidx.fragment.app.Fragment  
isNotEmpty androidx.fragment.app.Fragment  java androidx.fragment.app.Fragment  joinToString androidx.fragment.app.Fragment  launch androidx.fragment.app.Fragment  let androidx.fragment.app.Fragment  lifecycleScope androidx.fragment.app.Fragment  listOf androidx.fragment.app.Fragment  
loadWallet androidx.fragment.app.Fragment  	lowercase androidx.fragment.app.Fragment  map androidx.fragment.app.Fragment  
mapNotNull androidx.fragment.app.Fragment  mapOf androidx.fragment.app.Fragment  
mutableListOf androidx.fragment.app.Fragment  	onDestroy androidx.fragment.app.Fragment  
onDestroyView androidx.fragment.app.Fragment  onPause androidx.fragment.app.Fragment  onResume androidx.fragment.app.Fragment  
onViewCreated androidx.fragment.app.Fragment  progressBar androidx.fragment.app.Fragment  requireContext androidx.fragment.app.Fragment  requireView androidx.fragment.app.Fragment  
searchUser androidx.fragment.app.Fragment  setupSearch androidx.fragment.app.Fragment  setupWebView androidx.fragment.app.Fragment  showDeleteContactDialog androidx.fragment.app.Fragment  showDeleteDialog androidx.fragment.app.Fragment  showRestoreDialog androidx.fragment.app.Fragment  showSeedPhraseDialog androidx.fragment.app.Fragment  sorted androidx.fragment.app.Fragment  
startActivity androidx.fragment.app.Fragment  to androidx.fragment.app.Fragment  
toMutableList androidx.fragment.app.Fragment  toString androidx.fragment.app.Fragment  toggleMatrixBackground androidx.fragment.app.Fragment  trim androidx.fragment.app.Fragment  updateDisplayedUsername androidx.fragment.app.Fragment  webView androidx.fragment.app.Fragment  ActivityDmChatBinding &androidx.fragment.app.FragmentActivity  ActivityDmRequestBinding &androidx.fragment.app.FragmentActivity  ActivityEncryptedChatBinding &androidx.fragment.app.FragmentActivity  AlertDialog &androidx.fragment.app.FragmentActivity  
BackupUtil &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  Button &androidx.fragment.app.FragmentActivity  CharSequence &androidx.fragment.app.FragmentActivity  ChatMessage &androidx.fragment.app.FragmentActivity  
ChatsFragment &androidx.fragment.app.FragmentActivity  ContactsFragment &androidx.fragment.app.FragmentActivity  Context &androidx.fragment.app.FragmentActivity  	DMAdapter &androidx.fragment.app.FragmentActivity  	DMRequest &androidx.fragment.app.FragmentActivity  DMRequestAdapter &androidx.fragment.app.FragmentActivity  EditText &androidx.fragment.app.FragmentActivity  Editable &androidx.fragment.app.FragmentActivity  EncryptedChatActivity &androidx.fragment.app.FragmentActivity  EncryptionUtil &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  FirebaseApp &androidx.fragment.app.FragmentActivity  FirebaseFirestore &androidx.fragment.app.FragmentActivity  FirebaseOptions &androidx.fragment.app.FragmentActivity  Fragment &androidx.fragment.app.FragmentActivity  Handler &androidx.fragment.app.FragmentActivity  InputMethodManager &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  KeyStoreUtil &androidx.fragment.app.FragmentActivity  LinearLayoutManager &androidx.fragment.app.FragmentActivity  List &androidx.fragment.app.FragmentActivity  ListView &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  Looper &androidx.fragment.app.FragmentActivity  MainActivity &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  
SetOptions &androidx.fragment.app.FragmentActivity  SettingsFragment &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  System &androidx.fragment.app.FragmentActivity  TAG &androidx.fragment.app.FragmentActivity  TextWatcher &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  
UpdateManager &androidx.fragment.app.FragmentActivity  UsernameSetupActivity &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  WalletFragment &androidx.fragment.app.FragmentActivity  adapter &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  androidx &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  await &androidx.fragment.app.FragmentActivity  binding &androidx.fragment.app.FragmentActivity  checkForUpdates &androidx.fragment.app.FragmentActivity  checkUsernameAndNavigate &androidx.fragment.app.FragmentActivity  db &androidx.fragment.app.FragmentActivity  findViewById &androidx.fragment.app.FragmentActivity  finish &androidx.fragment.app.FragmentActivity  firstOrNull &androidx.fragment.app.FragmentActivity  getSystemService &androidx.fragment.app.FragmentActivity  handleAccept &androidx.fragment.app.FragmentActivity  handleChatsNavigation &androidx.fragment.app.FragmentActivity  handleReject &androidx.fragment.app.FragmentActivity  	hashMapOf &androidx.fragment.app.FragmentActivity  
isNotBlank &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  
isNullOrBlank &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  joinToString &androidx.fragment.app.FragmentActivity  launch &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  lifecycleScope &androidx.fragment.app.FragmentActivity  listOf &androidx.fragment.app.FragmentActivity  listenForMessages &androidx.fragment.app.FragmentActivity  listenToMessages &androidx.fragment.app.FragmentActivity  loadDMRequests &androidx.fragment.app.FragmentActivity  	lowercase &androidx.fragment.app.FragmentActivity  
mapNotNull &androidx.fragment.app.FragmentActivity  mapOf &androidx.fragment.app.FragmentActivity  markChatAsRead &androidx.fragment.app.FragmentActivity  
mutableListOf &androidx.fragment.app.FragmentActivity  
onBackPressed &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  onNewIntent &androidx.fragment.app.FragmentActivity  onResume &androidx.fragment.app.FragmentActivity  registerUsername &androidx.fragment.app.FragmentActivity  scrollToBottom &androidx.fragment.app.FragmentActivity  searchUserByUsername &androidx.fragment.app.FragmentActivity  setContentView &androidx.fragment.app.FragmentActivity  	setIntent &androidx.fragment.app.FragmentActivity  showAddToContactsDialog &androidx.fragment.app.FragmentActivity  showFragment &androidx.fragment.app.FragmentActivity  showRestoreDialog &androidx.fragment.app.FragmentActivity  showSeedPhraseDialog &androidx.fragment.app.FragmentActivity  sorted &androidx.fragment.app.FragmentActivity  
startActivity &androidx.fragment.app.FragmentActivity  startMainActivity &androidx.fragment.app.FragmentActivity  take &androidx.fragment.app.FragmentActivity  to &androidx.fragment.app.FragmentActivity  
toMutableList &androidx.fragment.app.FragmentActivity  toString &androidx.fragment.app.FragmentActivity  trim &androidx.fragment.app.FragmentActivity  users &androidx.fragment.app.FragmentActivity  POP_BACK_STACK_INCLUSIVE %androidx.fragment.app.FragmentManager  addOnBackStackChangedListener %androidx.fragment.app.FragmentManager  backStackEntryCount %androidx.fragment.app.FragmentManager  beginTransaction %androidx.fragment.app.FragmentManager  getBACKStackEntryCount %androidx.fragment.app.FragmentManager  getBackStackEntryCount %androidx.fragment.app.FragmentManager  popBackStack %androidx.fragment.app.FragmentManager  setBackStackEntryCount %androidx.fragment.app.FragmentManager  <SAM-CONSTRUCTOR> @androidx.fragment.app.FragmentManager.OnBackStackChangedListener  add )androidx.fragment.app.FragmentTransaction  addToBackStack )androidx.fragment.app.FragmentTransaction  commit )androidx.fragment.app.FragmentTransaction  hide )androidx.fragment.app.FragmentTransaction  show )androidx.fragment.app.FragmentTransaction  LifecycleCoroutineScope androidx.lifecycle  lifecycleScope androidx.lifecycle  	getLAUNCH *androidx.lifecycle.LifecycleCoroutineScope  	getLaunch *androidx.lifecycle.LifecycleCoroutineScope  launch *androidx.lifecycle.LifecycleCoroutineScope  PreferenceManager androidx.preference  getDefaultSharedPreferences %androidx.preference.PreferenceManager  DiffUtil androidx.recyclerview.widget  LinearLayoutManager androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  Boolean 2androidx.recyclerview.widget.DiffUtil.ItemCallback  
DirectMessage 2androidx.recyclerview.widget.DiffUtil.ItemCallback  getSTACKFromEnd 0androidx.recyclerview.widget.LinearLayoutManager  getStackFromEnd 0androidx.recyclerview.widget.LinearLayoutManager  setStackFromEnd 0androidx.recyclerview.widget.LinearLayoutManager  stackFromEnd 0androidx.recyclerview.widget.LinearLayoutManager  Boolean (androidx.recyclerview.widget.ListAdapter  DiffUtil (androidx.recyclerview.widget.ListAdapter  
DirectMessage (androidx.recyclerview.widget.ListAdapter  Int (androidx.recyclerview.widget.ListAdapter  ItemDirectMessageBinding (androidx.recyclerview.widget.ListAdapter  LayoutInflater (androidx.recyclerview.widget.ListAdapter  RecyclerView (androidx.recyclerview.widget.ListAdapter  Unit (androidx.recyclerview.widget.ListAdapter  	ViewGroup (androidx.recyclerview.widget.ListAdapter  android (androidx.recyclerview.widget.ListAdapter  apply (androidx.recyclerview.widget.ListAdapter  getItem (androidx.recyclerview.widget.ListAdapter  invoke (androidx.recyclerview.widget.ListAdapter  onItemClick (androidx.recyclerview.widget.ListAdapter  Adapter )androidx.recyclerview.widget.RecyclerView  
LayoutManager )androidx.recyclerview.widget.RecyclerView  NO_POSITION )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  adapter )androidx.recyclerview.widget.RecyclerView  
getADAPTER )androidx.recyclerview.widget.RecyclerView  
getAdapter )androidx.recyclerview.widget.RecyclerView  getLAYOUTManager )androidx.recyclerview.widget.RecyclerView  getLayoutManager )androidx.recyclerview.widget.RecyclerView  
getVISIBILITY )androidx.recyclerview.widget.RecyclerView  
getVisibility )androidx.recyclerview.widget.RecyclerView  
layoutManager )androidx.recyclerview.widget.RecyclerView  post )androidx.recyclerview.widget.RecyclerView  scrollToPosition )androidx.recyclerview.widget.RecyclerView  
setAdapter )androidx.recyclerview.widget.RecyclerView  setHasFixedSize )androidx.recyclerview.widget.RecyclerView  setLayoutManager )androidx.recyclerview.widget.RecyclerView  
setVisibility )androidx.recyclerview.widget.RecyclerView  smoothScrollToPosition )androidx.recyclerview.widget.RecyclerView  
visibility )androidx.recyclerview.widget.RecyclerView  Boolean 1androidx.recyclerview.widget.RecyclerView.Adapter  ChatItem 1androidx.recyclerview.widget.RecyclerView.Adapter  ChatMessage 1androidx.recyclerview.widget.RecyclerView.Adapter  ChatViewHolder 1androidx.recyclerview.widget.RecyclerView.Adapter  Color 1androidx.recyclerview.widget.RecyclerView.Adapter  
ContextCompat 1androidx.recyclerview.widget.RecyclerView.Adapter  	DMMessage 1androidx.recyclerview.widget.RecyclerView.Adapter  	DMRequest 1androidx.recyclerview.widget.RecyclerView.Adapter  Date 1androidx.recyclerview.widget.RecyclerView.Adapter  DiffUtil 1androidx.recyclerview.widget.RecyclerView.Adapter  
DirectMessage 1androidx.recyclerview.widget.RecyclerView.Adapter  EncryptionUtil 1androidx.recyclerview.widget.RecyclerView.Adapter  	Exception 1androidx.recyclerview.widget.RecyclerView.Adapter  	ImageView 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemChatListBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemContactBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemDirectMessageBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemDmMessageBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemDmRequestBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemMessageBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemMessageMeBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemMessageOtherBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  List 1androidx.recyclerview.widget.RecyclerView.Adapter  Locale 1androidx.recyclerview.widget.RecyclerView.Adapter  Log 1androidx.recyclerview.widget.RecyclerView.Adapter  Long 1androidx.recyclerview.widget.RecyclerView.Adapter  Pair 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  SimpleDateFormat 1androidx.recyclerview.widget.RecyclerView.Adapter  String 1androidx.recyclerview.widget.RecyclerView.Adapter  TextView 1androidx.recyclerview.widget.RecyclerView.Adapter  Unit 1androidx.recyclerview.widget.RecyclerView.Adapter  View 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  android 1androidx.recyclerview.widget.RecyclerView.Adapter  apply 1androidx.recyclerview.widget.RecyclerView.Adapter  formatTimestamp 1androidx.recyclerview.widget.RecyclerView.Adapter  getItem 1androidx.recyclerview.widget.RecyclerView.Adapter  getUserColor 1androidx.recyclerview.widget.RecyclerView.Adapter  invoke 1androidx.recyclerview.widget.RecyclerView.Adapter  listOf 1androidx.recyclerview.widget.RecyclerView.Adapter  mutableMapOf 1androidx.recyclerview.widget.RecyclerView.Adapter  notifyDataSetChanged 1androidx.recyclerview.widget.RecyclerView.Adapter  onAccept 1androidx.recyclerview.widget.RecyclerView.Adapter  onClick 1androidx.recyclerview.widget.RecyclerView.Adapter  onItemClick 1androidx.recyclerview.widget.RecyclerView.Adapter  onLongClick 1androidx.recyclerview.widget.RecyclerView.Adapter  onReject 1androidx.recyclerview.widget.RecyclerView.Adapter  set 1androidx.recyclerview.widget.RecyclerView.Adapter  take 1androidx.recyclerview.widget.RecyclerView.Adapter  
DirectMessage 4androidx.recyclerview.widget.RecyclerView.ViewHolder  	ImageView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemChatListBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemContactBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemDirectMessageBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemDmMessageBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemDmRequestBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemMessageBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemMessageMeBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemMessageOtherBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  RecyclerView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  String 4androidx.recyclerview.widget.RecyclerView.ViewHolder  TextView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  View 4androidx.recyclerview.widget.RecyclerView.ViewHolder  android 4androidx.recyclerview.widget.RecyclerView.ViewHolder  apply 4androidx.recyclerview.widget.RecyclerView.ViewHolder  bind 4androidx.recyclerview.widget.RecyclerView.ViewHolder  binding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  getItem 4androidx.recyclerview.widget.RecyclerView.ViewHolder  invoke 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onItemClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  await !com.google.android.gms.tasks.Task  getAWAIT !com.google.android.gms.tasks.Task  getAwait !com.google.android.gms.tasks.Task  getSELECTEDItemId Acom.google.android.material.bottomnavigation.BottomNavigationView  getSelectedItemId Acom.google.android.material.bottomnavigation.BottomNavigationView  selectedItemId Acom.google.android.material.bottomnavigation.BottomNavigationView  setOnItemSelectedListener Acom.google.android.material.bottomnavigation.BottomNavigationView  setSelectedItemId Acom.google.android.material.bottomnavigation.BottomNavigationView  setOnItemSelectedListener 8com.google.android.material.navigation.NavigationBarView  <SAM-CONSTRUCTOR> Ocom.google.android.material.navigation.NavigationBarView.OnItemSelectedListener  FirebaseApp com.google.firebase  FirebaseOptions com.google.firebase  getApps com.google.firebase.FirebaseApp  
initializeApp com.google.firebase.FirebaseApp  Builder #com.google.firebase.FirebaseOptions  build +com.google.firebase.FirebaseOptions.Builder  	setApiKey +com.google.firebase.FirebaseOptions.Builder  setApplicationId +com.google.firebase.FirebaseOptions.Builder  setProjectId +com.google.firebase.FirebaseOptions.Builder  CollectionReference com.google.firebase.firestore  DocumentReference com.google.firebase.firestore  DocumentSnapshot com.google.firebase.firestore  FirebaseFirestore com.google.firebase.firestore  FirebaseFirestoreException com.google.firebase.firestore  ListenerRegistration com.google.firebase.firestore  
QuerySnapshot com.google.firebase.firestore  
SetOptions com.google.firebase.firestore  add 1com.google.firebase.firestore.CollectionReference  document 1com.google.firebase.firestore.CollectionReference  get 1com.google.firebase.firestore.CollectionReference  orderBy 1com.google.firebase.firestore.CollectionReference  whereArrayContains 1com.google.firebase.firestore.CollectionReference  whereEqualTo 1com.google.firebase.firestore.CollectionReference  
collection /com.google.firebase.firestore.DocumentReference  delete /com.google.firebase.firestore.DocumentReference  get /com.google.firebase.firestore.DocumentReference  set /com.google.firebase.firestore.DocumentReference  update /com.google.firebase.firestore.DocumentReference  data .com.google.firebase.firestore.DocumentSnapshot  exists .com.google.firebase.firestore.DocumentSnapshot  get .com.google.firebase.firestore.DocumentSnapshot  getDATA .com.google.firebase.firestore.DocumentSnapshot  getData .com.google.firebase.firestore.DocumentSnapshot  getID .com.google.firebase.firestore.DocumentSnapshot  getId .com.google.firebase.firestore.DocumentSnapshot  getLong .com.google.firebase.firestore.DocumentSnapshot  	getString .com.google.firebase.firestore.DocumentSnapshot  id .com.google.firebase.firestore.DocumentSnapshot  setData .com.google.firebase.firestore.DocumentSnapshot  setId .com.google.firebase.firestore.DocumentSnapshot  toObject .com.google.firebase.firestore.DocumentSnapshot  <SAM-CONSTRUCTOR> +com.google.firebase.firestore.EventListener  
collection /com.google.firebase.firestore.FirebaseFirestore  getInstance /com.google.firebase.firestore.FirebaseFirestore  equals 8com.google.firebase.firestore.FirebaseFirestoreException  message 8com.google.firebase.firestore.FirebaseFirestoreException  remove 2com.google.firebase.firestore.ListenerRegistration  add #com.google.firebase.firestore.Query  addSnapshotListener #com.google.firebase.firestore.Query  document #com.google.firebase.firestore.Query  get #com.google.firebase.firestore.Query  orderBy #com.google.firebase.firestore.Query  whereArrayContains #com.google.firebase.firestore.Query  whereEqualTo #com.google.firebase.firestore.Query  	documents +com.google.firebase.firestore.QuerySnapshot  equals +com.google.firebase.firestore.QuerySnapshot  getDOCUMENTS +com.google.firebase.firestore.QuerySnapshot  getDocuments +com.google.firebase.firestore.QuerySnapshot  
getISEmpty +com.google.firebase.firestore.QuerySnapshot  
getIsEmpty +com.google.firebase.firestore.QuerySnapshot  isEmpty +com.google.firebase.firestore.QuerySnapshot  setDocuments +com.google.firebase.firestore.QuerySnapshot  setEmpty +com.google.firebase.firestore.QuerySnapshot  merge (com.google.firebase.firestore.SetOptions  Gson com.google.gson  fromJson com.google.gson.Gson  ActivityDmChatBinding com.sr.ghostencryptedchat  ActivityDmRequestBinding com.sr.ghostencryptedchat  ActivityEncryptedChatBinding com.sr.ghostencryptedchat  AlertDialog com.sr.ghostencryptedchat  
BackupUtil com.sr.ghostencryptedchat  CharSequence com.sr.ghostencryptedchat  ChatMessage com.sr.ghostencryptedchat  
ChatsFragment com.sr.ghostencryptedchat  ContactsActivity com.sr.ghostencryptedchat  ContactsFragment com.sr.ghostencryptedchat  Context com.sr.ghostencryptedchat  	DMAdapter com.sr.ghostencryptedchat  DMChatActivity com.sr.ghostencryptedchat  	DMRequest com.sr.ghostencryptedchat  DMRequestActivity com.sr.ghostencryptedchat  DMRequestAdapter com.sr.ghostencryptedchat  DirectMessageActivity com.sr.ghostencryptedchat  EncryptedChatActivity com.sr.ghostencryptedchat  EncryptionUtil com.sr.ghostencryptedchat  	Exception com.sr.ghostencryptedchat  FirebaseApp com.sr.ghostencryptedchat  FirebaseFirestore com.sr.ghostencryptedchat  FirebaseOptions com.sr.ghostencryptedchat  GroupChatActivity com.sr.ghostencryptedchat  Handler com.sr.ghostencryptedchat  Int com.sr.ghostencryptedchat  Intent com.sr.ghostencryptedchat  KeyStoreUtil com.sr.ghostencryptedchat  LinearLayoutManager com.sr.ghostencryptedchat  List com.sr.ghostencryptedchat  Log com.sr.ghostencryptedchat  Looper com.sr.ghostencryptedchat  MainActivity com.sr.ghostencryptedchat  R com.sr.ghostencryptedchat  
SetOptions com.sr.ghostencryptedchat  SettingsActivity com.sr.ghostencryptedchat  SettingsFragment com.sr.ghostencryptedchat  SplashActivity com.sr.ghostencryptedchat  String com.sr.ghostencryptedchat  System com.sr.ghostencryptedchat  TAG com.sr.ghostencryptedchat  Toast com.sr.ghostencryptedchat  
UpdateManager com.sr.ghostencryptedchat  UsernameSetupActivity com.sr.ghostencryptedchat  View com.sr.ghostencryptedchat  WalletFragment com.sr.ghostencryptedchat  adapter com.sr.ghostencryptedchat  android com.sr.ghostencryptedchat  androidx com.sr.ghostencryptedchat  apply com.sr.ghostencryptedchat  await com.sr.ghostencryptedchat  binding com.sr.ghostencryptedchat  db com.sr.ghostencryptedchat  finish com.sr.ghostencryptedchat  firstOrNull com.sr.ghostencryptedchat  	hashMapOf com.sr.ghostencryptedchat  
isNotBlank com.sr.ghostencryptedchat  
isNotEmpty com.sr.ghostencryptedchat  
isNullOrBlank com.sr.ghostencryptedchat  java com.sr.ghostencryptedchat  joinToString com.sr.ghostencryptedchat  launch com.sr.ghostencryptedchat  let com.sr.ghostencryptedchat  lifecycleScope com.sr.ghostencryptedchat  listOf com.sr.ghostencryptedchat  	lowercase com.sr.ghostencryptedchat  
mapNotNull com.sr.ghostencryptedchat  mapOf com.sr.ghostencryptedchat  
mutableListOf com.sr.ghostencryptedchat  searchUserByUsername com.sr.ghostencryptedchat  showSeedPhraseDialog com.sr.ghostencryptedchat  sorted com.sr.ghostencryptedchat  
startActivity com.sr.ghostencryptedchat  startMainActivity com.sr.ghostencryptedchat  take com.sr.ghostencryptedchat  to com.sr.ghostencryptedchat  
toMutableList com.sr.ghostencryptedchat  toString com.sr.ghostencryptedchat  trim com.sr.ghostencryptedchat  users com.sr.ghostencryptedchat  Bundle *com.sr.ghostencryptedchat.ContactsActivity  CharSequence *com.sr.ghostencryptedchat.ContactsActivity  EditText *com.sr.ghostencryptedchat.ContactsActivity  Editable *com.sr.ghostencryptedchat.ContactsActivity  FirebaseFirestore *com.sr.ghostencryptedchat.ContactsActivity  Int *com.sr.ghostencryptedchat.ContactsActivity  KeyStoreUtil *com.sr.ghostencryptedchat.ContactsActivity  ListView *com.sr.ghostencryptedchat.ContactsActivity  R *com.sr.ghostencryptedchat.ContactsActivity  String *com.sr.ghostencryptedchat.ContactsActivity  TextWatcher *com.sr.ghostencryptedchat.ContactsActivity  Toast *com.sr.ghostencryptedchat.ContactsActivity  adapter *com.sr.ghostencryptedchat.ContactsActivity  android *com.sr.ghostencryptedchat.ContactsActivity  db *com.sr.ghostencryptedchat.ContactsActivity  findViewById *com.sr.ghostencryptedchat.ContactsActivity  firstOrNull *com.sr.ghostencryptedchat.ContactsActivity  
getANDROID *com.sr.ghostencryptedchat.ContactsActivity  
getAndroid *com.sr.ghostencryptedchat.ContactsActivity  getFIRSTOrNull *com.sr.ghostencryptedchat.ContactsActivity  getFirstOrNull *com.sr.ghostencryptedchat.ContactsActivity  
getISNotEmpty *com.sr.ghostencryptedchat.ContactsActivity  
getIsNotEmpty *com.sr.ghostencryptedchat.ContactsActivity  getLOWERCASE *com.sr.ghostencryptedchat.ContactsActivity  getLowercase *com.sr.ghostencryptedchat.ContactsActivity  getMUTABLEListOf *com.sr.ghostencryptedchat.ContactsActivity  getMutableListOf *com.sr.ghostencryptedchat.ContactsActivity  getTOString *com.sr.ghostencryptedchat.ContactsActivity  getTRIM *com.sr.ghostencryptedchat.ContactsActivity  getToString *com.sr.ghostencryptedchat.ContactsActivity  getTrim *com.sr.ghostencryptedchat.ContactsActivity  
isNotEmpty *com.sr.ghostencryptedchat.ContactsActivity  listView *com.sr.ghostencryptedchat.ContactsActivity  	lowercase *com.sr.ghostencryptedchat.ContactsActivity  
mutableListOf *com.sr.ghostencryptedchat.ContactsActivity  searchInput *com.sr.ghostencryptedchat.ContactsActivity  searchUserByUsername *com.sr.ghostencryptedchat.ContactsActivity  setContentView *com.sr.ghostencryptedchat.ContactsActivity  toString *com.sr.ghostencryptedchat.ContactsActivity  trim *com.sr.ghostencryptedchat.ContactsActivity  username *com.sr.ghostencryptedchat.ContactsActivity  users *com.sr.ghostencryptedchat.ContactsActivity  
getADAPTER Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  
getAdapter Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  
getISNotEmpty Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  
getIsNotEmpty Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  getLOWERCASE Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  getLowercase Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  getSEARCHUserByUsername Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  getSearchUserByUsername Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  getTOString Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  getTRIM Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  getToString Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  getTrim Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  getUSERS Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  getUsers Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  
isNotEmpty Fcom.sr.ghostencryptedchat.ContactsActivity.onCreate.<no name provided>  ActivityDmChatBinding (com.sr.ghostencryptedchat.DMChatActivity  AlertDialog (com.sr.ghostencryptedchat.DMChatActivity  Bundle (com.sr.ghostencryptedchat.DMChatActivity  CharSequence (com.sr.ghostencryptedchat.DMChatActivity  ChatMessage (com.sr.ghostencryptedchat.DMChatActivity  Context (com.sr.ghostencryptedchat.DMChatActivity  	DMAdapter (com.sr.ghostencryptedchat.DMChatActivity  Editable (com.sr.ghostencryptedchat.DMChatActivity  EncryptedChatActivity (com.sr.ghostencryptedchat.DMChatActivity  EncryptionUtil (com.sr.ghostencryptedchat.DMChatActivity  	Exception (com.sr.ghostencryptedchat.DMChatActivity  FirebaseFirestore (com.sr.ghostencryptedchat.DMChatActivity  InputMethodManager (com.sr.ghostencryptedchat.DMChatActivity  Int (com.sr.ghostencryptedchat.DMChatActivity  Intent (com.sr.ghostencryptedchat.DMChatActivity  KeyStoreUtil (com.sr.ghostencryptedchat.DMChatActivity  LinearLayoutManager (com.sr.ghostencryptedchat.DMChatActivity  List (com.sr.ghostencryptedchat.DMChatActivity  Log (com.sr.ghostencryptedchat.DMChatActivity  R (com.sr.ghostencryptedchat.DMChatActivity  
SetOptions (com.sr.ghostencryptedchat.DMChatActivity  String (com.sr.ghostencryptedchat.DMChatActivity  System (com.sr.ghostencryptedchat.DMChatActivity  TextWatcher (com.sr.ghostencryptedchat.DMChatActivity  Toast (com.sr.ghostencryptedchat.DMChatActivity  View (com.sr.ghostencryptedchat.DMChatActivity  adapter (com.sr.ghostencryptedchat.DMChatActivity  binding (com.sr.ghostencryptedchat.DMChatActivity  chatId (com.sr.ghostencryptedchat.DMChatActivity  currentUser (com.sr.ghostencryptedchat.DMChatActivity  db (com.sr.ghostencryptedchat.DMChatActivity  finish (com.sr.ghostencryptedchat.DMChatActivity  	getINTENT (com.sr.ghostencryptedchat.DMChatActivity  
getISNotBlank (com.sr.ghostencryptedchat.DMChatActivity  
getISNotEmpty (com.sr.ghostencryptedchat.DMChatActivity  getISNullOrBlank (com.sr.ghostencryptedchat.DMChatActivity  	getIntent (com.sr.ghostencryptedchat.DMChatActivity  
getIsNotBlank (com.sr.ghostencryptedchat.DMChatActivity  
getIsNotEmpty (com.sr.ghostencryptedchat.DMChatActivity  getIsNullOrBlank (com.sr.ghostencryptedchat.DMChatActivity  getJOINToString (com.sr.ghostencryptedchat.DMChatActivity  getJoinToString (com.sr.ghostencryptedchat.DMChatActivity  getLAYOUTInflater (com.sr.ghostencryptedchat.DMChatActivity  	getLISTOf (com.sr.ghostencryptedchat.DMChatActivity  getLayoutInflater (com.sr.ghostencryptedchat.DMChatActivity  	getListOf (com.sr.ghostencryptedchat.DMChatActivity  getMAPOf (com.sr.ghostencryptedchat.DMChatActivity  getMUTABLEListOf (com.sr.ghostencryptedchat.DMChatActivity  getMapOf (com.sr.ghostencryptedchat.DMChatActivity  getMutableListOf (com.sr.ghostencryptedchat.DMChatActivity  	getSORTED (com.sr.ghostencryptedchat.DMChatActivity  	getSorted (com.sr.ghostencryptedchat.DMChatActivity  getSystemService (com.sr.ghostencryptedchat.DMChatActivity  getTAKE (com.sr.ghostencryptedchat.DMChatActivity  getTO (com.sr.ghostencryptedchat.DMChatActivity  getTOMutableList (com.sr.ghostencryptedchat.DMChatActivity  getTake (com.sr.ghostencryptedchat.DMChatActivity  getTo (com.sr.ghostencryptedchat.DMChatActivity  getToMutableList (com.sr.ghostencryptedchat.DMChatActivity  intent (com.sr.ghostencryptedchat.DMChatActivity  
isNotBlank (com.sr.ghostencryptedchat.DMChatActivity  
isNotEmpty (com.sr.ghostencryptedchat.DMChatActivity  
isNullOrBlank (com.sr.ghostencryptedchat.DMChatActivity  java (com.sr.ghostencryptedchat.DMChatActivity  joinToString (com.sr.ghostencryptedchat.DMChatActivity  layoutInflater (com.sr.ghostencryptedchat.DMChatActivity  listOf (com.sr.ghostencryptedchat.DMChatActivity  listenForMessages (com.sr.ghostencryptedchat.DMChatActivity  mapOf (com.sr.ghostencryptedchat.DMChatActivity  markChatAsRead (com.sr.ghostencryptedchat.DMChatActivity  messages (com.sr.ghostencryptedchat.DMChatActivity  
mutableListOf (com.sr.ghostencryptedchat.DMChatActivity  	recipient (com.sr.ghostencryptedchat.DMChatActivity  scrollToBottom (com.sr.ghostencryptedchat.DMChatActivity  setContentView (com.sr.ghostencryptedchat.DMChatActivity  	setIntent (com.sr.ghostencryptedchat.DMChatActivity  setLayoutInflater (com.sr.ghostencryptedchat.DMChatActivity  showAddToContactsDialog (com.sr.ghostencryptedchat.DMChatActivity  sorted (com.sr.ghostencryptedchat.DMChatActivity  
startActivity (com.sr.ghostencryptedchat.DMChatActivity  take (com.sr.ghostencryptedchat.DMChatActivity  to (com.sr.ghostencryptedchat.DMChatActivity  
toMutableList (com.sr.ghostencryptedchat.DMChatActivity  
getBINDING Dcom.sr.ghostencryptedchat.DMChatActivity.onCreate.<no name provided>  
getBinding Dcom.sr.ghostencryptedchat.DMChatActivity.onCreate.<no name provided>  getISNullOrBlank Dcom.sr.ghostencryptedchat.DMChatActivity.onCreate.<no name provided>  getIsNullOrBlank Dcom.sr.ghostencryptedchat.DMChatActivity.onCreate.<no name provided>  
isNullOrBlank Dcom.sr.ghostencryptedchat.DMChatActivity.onCreate.<no name provided>  ActivityDmRequestBinding +com.sr.ghostencryptedchat.DMRequestActivity  Bundle +com.sr.ghostencryptedchat.DMRequestActivity  	DMRequest +com.sr.ghostencryptedchat.DMRequestActivity  DMRequestAdapter +com.sr.ghostencryptedchat.DMRequestActivity  FirebaseFirestore +com.sr.ghostencryptedchat.DMRequestActivity  KeyStoreUtil +com.sr.ghostencryptedchat.DMRequestActivity  LinearLayoutManager +com.sr.ghostencryptedchat.DMRequestActivity  String +com.sr.ghostencryptedchat.DMRequestActivity  System +com.sr.ghostencryptedchat.DMRequestActivity  Toast +com.sr.ghostencryptedchat.DMRequestActivity  adapter +com.sr.ghostencryptedchat.DMRequestActivity  apply +com.sr.ghostencryptedchat.DMRequestActivity  binding +com.sr.ghostencryptedchat.DMRequestActivity  db +com.sr.ghostencryptedchat.DMRequestActivity  getAPPLY +com.sr.ghostencryptedchat.DMRequestActivity  getApply +com.sr.ghostencryptedchat.DMRequestActivity  getJOINToString +com.sr.ghostencryptedchat.DMRequestActivity  getJoinToString +com.sr.ghostencryptedchat.DMRequestActivity  getLAYOUTInflater +com.sr.ghostencryptedchat.DMRequestActivity  	getLISTOf +com.sr.ghostencryptedchat.DMRequestActivity  getLayoutInflater +com.sr.ghostencryptedchat.DMRequestActivity  	getListOf +com.sr.ghostencryptedchat.DMRequestActivity  
getMAPNotNull +com.sr.ghostencryptedchat.DMRequestActivity  getMAPOf +com.sr.ghostencryptedchat.DMRequestActivity  getMUTABLEListOf +com.sr.ghostencryptedchat.DMRequestActivity  
getMapNotNull +com.sr.ghostencryptedchat.DMRequestActivity  getMapOf +com.sr.ghostencryptedchat.DMRequestActivity  getMutableListOf +com.sr.ghostencryptedchat.DMRequestActivity  	getSORTED +com.sr.ghostencryptedchat.DMRequestActivity  	getSorted +com.sr.ghostencryptedchat.DMRequestActivity  getTO +com.sr.ghostencryptedchat.DMRequestActivity  getTo +com.sr.ghostencryptedchat.DMRequestActivity  handleAccept +com.sr.ghostencryptedchat.DMRequestActivity  handleReject +com.sr.ghostencryptedchat.DMRequestActivity  java +com.sr.ghostencryptedchat.DMRequestActivity  joinToString +com.sr.ghostencryptedchat.DMRequestActivity  layoutInflater +com.sr.ghostencryptedchat.DMRequestActivity  listOf +com.sr.ghostencryptedchat.DMRequestActivity  loadDMRequests +com.sr.ghostencryptedchat.DMRequestActivity  
mapNotNull +com.sr.ghostencryptedchat.DMRequestActivity  mapOf +com.sr.ghostencryptedchat.DMRequestActivity  
mutableListOf +com.sr.ghostencryptedchat.DMRequestActivity  requests +com.sr.ghostencryptedchat.DMRequestActivity  setContentView +com.sr.ghostencryptedchat.DMRequestActivity  setLayoutInflater +com.sr.ghostencryptedchat.DMRequestActivity  sorted +com.sr.ghostencryptedchat.DMRequestActivity  to +com.sr.ghostencryptedchat.DMRequestActivity  username +com.sr.ghostencryptedchat.DMRequestActivity  Bundle /com.sr.ghostencryptedchat.DirectMessageActivity  Button /com.sr.ghostencryptedchat.DirectMessageActivity  EditText /com.sr.ghostencryptedchat.DirectMessageActivity  EncryptionUtil /com.sr.ghostencryptedchat.DirectMessageActivity  FirebaseFirestore /com.sr.ghostencryptedchat.DirectMessageActivity  KeyStoreUtil /com.sr.ghostencryptedchat.DirectMessageActivity  ListView /com.sr.ghostencryptedchat.DirectMessageActivity  R /com.sr.ghostencryptedchat.DirectMessageActivity  String /com.sr.ghostencryptedchat.DirectMessageActivity  System /com.sr.ghostencryptedchat.DirectMessageActivity  Toast /com.sr.ghostencryptedchat.DirectMessageActivity  adapter /com.sr.ghostencryptedchat.DirectMessageActivity  android /com.sr.ghostencryptedchat.DirectMessageActivity  chatId /com.sr.ghostencryptedchat.DirectMessageActivity  currentUser /com.sr.ghostencryptedchat.DirectMessageActivity  db /com.sr.ghostencryptedchat.DirectMessageActivity  dmList /com.sr.ghostencryptedchat.DirectMessageActivity  findViewById /com.sr.ghostencryptedchat.DirectMessageActivity  
getANDROID /com.sr.ghostencryptedchat.DirectMessageActivity  
getAndroid /com.sr.ghostencryptedchat.DirectMessageActivity  getHASHMapOf /com.sr.ghostencryptedchat.DirectMessageActivity  getHashMapOf /com.sr.ghostencryptedchat.DirectMessageActivity  	getINTENT /com.sr.ghostencryptedchat.DirectMessageActivity  
getISNotBlank /com.sr.ghostencryptedchat.DirectMessageActivity  	getIntent /com.sr.ghostencryptedchat.DirectMessageActivity  
getIsNotBlank /com.sr.ghostencryptedchat.DirectMessageActivity  getMUTABLEListOf /com.sr.ghostencryptedchat.DirectMessageActivity  getMutableListOf /com.sr.ghostencryptedchat.DirectMessageActivity  getTO /com.sr.ghostencryptedchat.DirectMessageActivity  getTo /com.sr.ghostencryptedchat.DirectMessageActivity  	hashMapOf /com.sr.ghostencryptedchat.DirectMessageActivity  input /com.sr.ghostencryptedchat.DirectMessageActivity  intent /com.sr.ghostencryptedchat.DirectMessageActivity  
isNotBlank /com.sr.ghostencryptedchat.DirectMessageActivity  listenToMessages /com.sr.ghostencryptedchat.DirectMessageActivity  messages /com.sr.ghostencryptedchat.DirectMessageActivity  
mutableListOf /com.sr.ghostencryptedchat.DirectMessageActivity  	recipient /com.sr.ghostencryptedchat.DirectMessageActivity  
sendButton /com.sr.ghostencryptedchat.DirectMessageActivity  setContentView /com.sr.ghostencryptedchat.DirectMessageActivity  	setIntent /com.sr.ghostencryptedchat.DirectMessageActivity  to /com.sr.ghostencryptedchat.DirectMessageActivity  ActivityEncryptedChatBinding /com.sr.ghostencryptedchat.EncryptedChatActivity  Bundle /com.sr.ghostencryptedchat.EncryptedChatActivity  
ChatsFragment /com.sr.ghostencryptedchat.EncryptedChatActivity  ContactsFragment /com.sr.ghostencryptedchat.EncryptedChatActivity  FirebaseApp /com.sr.ghostencryptedchat.EncryptedChatActivity  FirebaseFirestore /com.sr.ghostencryptedchat.EncryptedChatActivity  FirebaseOptions /com.sr.ghostencryptedchat.EncryptedChatActivity  Fragment /com.sr.ghostencryptedchat.EncryptedChatActivity  Intent /com.sr.ghostencryptedchat.EncryptedChatActivity  KeyStoreUtil /com.sr.ghostencryptedchat.EncryptedChatActivity  R /com.sr.ghostencryptedchat.EncryptedChatActivity  SettingsFragment /com.sr.ghostencryptedchat.EncryptedChatActivity  String /com.sr.ghostencryptedchat.EncryptedChatActivity  WalletFragment /com.sr.ghostencryptedchat.EncryptedChatActivity  androidx /com.sr.ghostencryptedchat.EncryptedChatActivity  binding /com.sr.ghostencryptedchat.EncryptedChatActivity  
chatsFragment /com.sr.ghostencryptedchat.EncryptedChatActivity  contactsFragment /com.sr.ghostencryptedchat.EncryptedChatActivity  currentFragment /com.sr.ghostencryptedchat.EncryptedChatActivity  
currentTab /com.sr.ghostencryptedchat.EncryptedChatActivity  db /com.sr.ghostencryptedchat.EncryptedChatActivity  getANDROIDX /com.sr.ghostencryptedchat.EncryptedChatActivity  getAndroidx /com.sr.ghostencryptedchat.EncryptedChatActivity  	getINTENT /com.sr.ghostencryptedchat.EncryptedChatActivity  	getIntent /com.sr.ghostencryptedchat.EncryptedChatActivity  getLAYOUTInflater /com.sr.ghostencryptedchat.EncryptedChatActivity  getLET /com.sr.ghostencryptedchat.EncryptedChatActivity  getLayoutInflater /com.sr.ghostencryptedchat.EncryptedChatActivity  getLet /com.sr.ghostencryptedchat.EncryptedChatActivity  getSUPPORTFragmentManager /com.sr.ghostencryptedchat.EncryptedChatActivity  getSupportFragmentManager /com.sr.ghostencryptedchat.EncryptedChatActivity  handleChatsNavigation /com.sr.ghostencryptedchat.EncryptedChatActivity  intent /com.sr.ghostencryptedchat.EncryptedChatActivity  layoutInflater /com.sr.ghostencryptedchat.EncryptedChatActivity  let /com.sr.ghostencryptedchat.EncryptedChatActivity  setContentView /com.sr.ghostencryptedchat.EncryptedChatActivity  	setIntent /com.sr.ghostencryptedchat.EncryptedChatActivity  setLayoutInflater /com.sr.ghostencryptedchat.EncryptedChatActivity  setSupportFragmentManager /com.sr.ghostencryptedchat.EncryptedChatActivity  settingsFragment /com.sr.ghostencryptedchat.EncryptedChatActivity  showFragment /com.sr.ghostencryptedchat.EncryptedChatActivity  supportFragmentManager /com.sr.ghostencryptedchat.EncryptedChatActivity  username /com.sr.ghostencryptedchat.EncryptedChatActivity  walletFragment /com.sr.ghostencryptedchat.EncryptedChatActivity  Bundle +com.sr.ghostencryptedchat.GroupChatActivity  R +com.sr.ghostencryptedchat.GroupChatActivity  setContentView +com.sr.ghostencryptedchat.GroupChatActivity  Bundle &com.sr.ghostencryptedchat.MainActivity  Button &com.sr.ghostencryptedchat.MainActivity  EncryptedChatActivity &com.sr.ghostencryptedchat.MainActivity  Intent &com.sr.ghostencryptedchat.MainActivity  R &com.sr.ghostencryptedchat.MainActivity  findViewById &com.sr.ghostencryptedchat.MainActivity  java &com.sr.ghostencryptedchat.MainActivity  setContentView &com.sr.ghostencryptedchat.MainActivity  
startActivity &com.sr.ghostencryptedchat.MainActivity  color com.sr.ghostencryptedchat.R  drawable com.sr.ghostencryptedchat.R  id com.sr.ghostencryptedchat.R  layout com.sr.ghostencryptedchat.R  
neon_green !com.sr.ghostencryptedchat.R.color  
bg_message_me $com.sr.ghostencryptedchat.R.drawable  bg_message_other $com.sr.ghostencryptedchat.R.drawable  ic_avatar_placeholder $com.sr.ghostencryptedchat.R.drawable  ic_globe $com.sr.ghostencryptedchat.R.drawable  	ic_person $com.sr.ghostencryptedchat.R.drawable  avatar com.sr.ghostencryptedchat.R.id  backupButton com.sr.ghostencryptedchat.R.id  chatMessagePreview com.sr.ghostencryptedchat.R.id  chatName com.sr.ghostencryptedchat.R.id  
chatTimestamp com.sr.ghostencryptedchat.R.id  contactList com.sr.ghostencryptedchat.R.id  dmInput com.sr.ghostencryptedchat.R.id  dmList com.sr.ghostencryptedchat.R.id  dmSend com.sr.ghostencryptedchat.R.id  fragmentContainer com.sr.ghostencryptedchat.R.id  launchChatButton com.sr.ghostencryptedchat.R.id  	nav_chats com.sr.ghostencryptedchat.R.id  nav_contacts com.sr.ghostencryptedchat.R.id  nav_settings com.sr.ghostencryptedchat.R.id  
nav_wallet com.sr.ghostencryptedchat.R.id  newAccountButton com.sr.ghostencryptedchat.R.id  newAccountLayout com.sr.ghostencryptedchat.R.id  recoverAccountButton com.sr.ghostencryptedchat.R.id  recoverAccountLayout com.sr.ghostencryptedchat.R.id  recover_button com.sr.ghostencryptedchat.R.id  register_button com.sr.ghostencryptedchat.R.id  
restoreButton com.sr.ghostencryptedchat.R.id  search_input com.sr.ghostencryptedchat.R.id  seed_phrase_input com.sr.ghostencryptedchat.R.id  unreadBadge com.sr.ghostencryptedchat.R.id  usernameText com.sr.ghostencryptedchat.R.id  username_input com.sr.ghostencryptedchat.R.id  activity_contacts "com.sr.ghostencryptedchat.R.layout  activity_direct_message "com.sr.ghostencryptedchat.R.layout  activity_group_chat "com.sr.ghostencryptedchat.R.layout  
activity_main "com.sr.ghostencryptedchat.R.layout  activity_settings "com.sr.ghostencryptedchat.R.layout  activity_splash "com.sr.ghostencryptedchat.R.layout  activity_username_setup "com.sr.ghostencryptedchat.R.layout  fragment_settings "com.sr.ghostencryptedchat.R.layout  	item_chat "com.sr.ghostencryptedchat.R.layout  AlertDialog *com.sr.ghostencryptedchat.SettingsActivity  
BackupUtil *com.sr.ghostencryptedchat.SettingsActivity  Bundle *com.sr.ghostencryptedchat.SettingsActivity  	Exception *com.sr.ghostencryptedchat.SettingsActivity  Intent *com.sr.ghostencryptedchat.SettingsActivity  MainActivity *com.sr.ghostencryptedchat.SettingsActivity  R *com.sr.ghostencryptedchat.SettingsActivity  String *com.sr.ghostencryptedchat.SettingsActivity  Toast *com.sr.ghostencryptedchat.SettingsActivity  android *com.sr.ghostencryptedchat.SettingsActivity  findViewById *com.sr.ghostencryptedchat.SettingsActivity  finish *com.sr.ghostencryptedchat.SettingsActivity  
getANDROID *com.sr.ghostencryptedchat.SettingsActivity  
getAndroid *com.sr.ghostencryptedchat.SettingsActivity  
getISNotEmpty *com.sr.ghostencryptedchat.SettingsActivity  
getIsNotEmpty *com.sr.ghostencryptedchat.SettingsActivity  	getLAUNCH *com.sr.ghostencryptedchat.SettingsActivity  getLIFECYCLEScope *com.sr.ghostencryptedchat.SettingsActivity  	getLaunch *com.sr.ghostencryptedchat.SettingsActivity  getLifecycleScope *com.sr.ghostencryptedchat.SettingsActivity  getTRIM *com.sr.ghostencryptedchat.SettingsActivity  getTrim *com.sr.ghostencryptedchat.SettingsActivity  
isNotEmpty *com.sr.ghostencryptedchat.SettingsActivity  java *com.sr.ghostencryptedchat.SettingsActivity  launch *com.sr.ghostencryptedchat.SettingsActivity  lifecycleScope *com.sr.ghostencryptedchat.SettingsActivity  setContentView *com.sr.ghostencryptedchat.SettingsActivity  showRestoreDialog *com.sr.ghostencryptedchat.SettingsActivity  showSeedPhraseDialog *com.sr.ghostencryptedchat.SettingsActivity  
startActivity *com.sr.ghostencryptedchat.SettingsActivity  trim *com.sr.ghostencryptedchat.SettingsActivity  Bundle (com.sr.ghostencryptedchat.SplashActivity  EncryptedChatActivity (com.sr.ghostencryptedchat.SplashActivity  	Exception (com.sr.ghostencryptedchat.SplashActivity  FirebaseApp (com.sr.ghostencryptedchat.SplashActivity  FirebaseFirestore (com.sr.ghostencryptedchat.SplashActivity  FirebaseOptions (com.sr.ghostencryptedchat.SplashActivity  Handler (com.sr.ghostencryptedchat.SplashActivity  Intent (com.sr.ghostencryptedchat.SplashActivity  KeyStoreUtil (com.sr.ghostencryptedchat.SplashActivity  Log (com.sr.ghostencryptedchat.SplashActivity  Looper (com.sr.ghostencryptedchat.SplashActivity  R (com.sr.ghostencryptedchat.SplashActivity  SPLASH_DELAY (com.sr.ghostencryptedchat.SplashActivity  TAG (com.sr.ghostencryptedchat.SplashActivity  
UpdateManager (com.sr.ghostencryptedchat.SplashActivity  UsernameSetupActivity (com.sr.ghostencryptedchat.SplashActivity  checkForUpdates (com.sr.ghostencryptedchat.SplashActivity  checkUsernameAndNavigate (com.sr.ghostencryptedchat.SplashActivity  finish (com.sr.ghostencryptedchat.SplashActivity  	getLAUNCH (com.sr.ghostencryptedchat.SplashActivity  getLIFECYCLEScope (com.sr.ghostencryptedchat.SplashActivity  	getLaunch (com.sr.ghostencryptedchat.SplashActivity  getLifecycleScope (com.sr.ghostencryptedchat.SplashActivity  java (com.sr.ghostencryptedchat.SplashActivity  launch (com.sr.ghostencryptedchat.SplashActivity  lifecycleScope (com.sr.ghostencryptedchat.SplashActivity  setContentView (com.sr.ghostencryptedchat.SplashActivity  
startActivity (com.sr.ghostencryptedchat.SplashActivity  
BackupUtil /com.sr.ghostencryptedchat.UsernameSetupActivity  Bundle /com.sr.ghostencryptedchat.UsernameSetupActivity  EncryptedChatActivity /com.sr.ghostencryptedchat.UsernameSetupActivity  	Exception /com.sr.ghostencryptedchat.UsernameSetupActivity  FirebaseFirestore /com.sr.ghostencryptedchat.UsernameSetupActivity  Intent /com.sr.ghostencryptedchat.UsernameSetupActivity  KeyStoreUtil /com.sr.ghostencryptedchat.UsernameSetupActivity  R /com.sr.ghostencryptedchat.UsernameSetupActivity  String /com.sr.ghostencryptedchat.UsernameSetupActivity  System /com.sr.ghostencryptedchat.UsernameSetupActivity  Toast /com.sr.ghostencryptedchat.UsernameSetupActivity  android /com.sr.ghostencryptedchat.UsernameSetupActivity  await /com.sr.ghostencryptedchat.UsernameSetupActivity  db /com.sr.ghostencryptedchat.UsernameSetupActivity  findViewById /com.sr.ghostencryptedchat.UsernameSetupActivity  finish /com.sr.ghostencryptedchat.UsernameSetupActivity  
getANDROID /com.sr.ghostencryptedchat.UsernameSetupActivity  getAWAIT /com.sr.ghostencryptedchat.UsernameSetupActivity  
getAndroid /com.sr.ghostencryptedchat.UsernameSetupActivity  getAwait /com.sr.ghostencryptedchat.UsernameSetupActivity  getHASHMapOf /com.sr.ghostencryptedchat.UsernameSetupActivity  getHashMapOf /com.sr.ghostencryptedchat.UsernameSetupActivity  
getISNotEmpty /com.sr.ghostencryptedchat.UsernameSetupActivity  
getIsNotEmpty /com.sr.ghostencryptedchat.UsernameSetupActivity  	getLAUNCH /com.sr.ghostencryptedchat.UsernameSetupActivity  getLIFECYCLEScope /com.sr.ghostencryptedchat.UsernameSetupActivity  	getLaunch /com.sr.ghostencryptedchat.UsernameSetupActivity  getLifecycleScope /com.sr.ghostencryptedchat.UsernameSetupActivity  getTO /com.sr.ghostencryptedchat.UsernameSetupActivity  getTRIM /com.sr.ghostencryptedchat.UsernameSetupActivity  getTo /com.sr.ghostencryptedchat.UsernameSetupActivity  getTrim /com.sr.ghostencryptedchat.UsernameSetupActivity  	hashMapOf /com.sr.ghostencryptedchat.UsernameSetupActivity  
isNotEmpty /com.sr.ghostencryptedchat.UsernameSetupActivity  java /com.sr.ghostencryptedchat.UsernameSetupActivity  launch /com.sr.ghostencryptedchat.UsernameSetupActivity  lifecycleScope /com.sr.ghostencryptedchat.UsernameSetupActivity  registerUsername /com.sr.ghostencryptedchat.UsernameSetupActivity  setContentView /com.sr.ghostencryptedchat.UsernameSetupActivity  
startActivity /com.sr.ghostencryptedchat.UsernameSetupActivity  startMainActivity /com.sr.ghostencryptedchat.UsernameSetupActivity  to /com.sr.ghostencryptedchat.UsernameSetupActivity  trim /com.sr.ghostencryptedchat.UsernameSetupActivity  Boolean !com.sr.ghostencryptedchat.adapter  ChatAdapter !com.sr.ghostencryptedchat.adapter  ChatListAdapter !com.sr.ghostencryptedchat.adapter  ChatViewHolder !com.sr.ghostencryptedchat.adapter  Color !com.sr.ghostencryptedchat.adapter  ContactAdapter !com.sr.ghostencryptedchat.adapter  
ContextCompat !com.sr.ghostencryptedchat.adapter  	DMAdapter !com.sr.ghostencryptedchat.adapter  
DMChatAdapter !com.sr.ghostencryptedchat.adapter  DMRequestAdapter !com.sr.ghostencryptedchat.adapter  Date !com.sr.ghostencryptedchat.adapter  DirectMessageAdapter !com.sr.ghostencryptedchat.adapter  EncryptionUtil !com.sr.ghostencryptedchat.adapter  	Exception !com.sr.ghostencryptedchat.adapter  Int !com.sr.ghostencryptedchat.adapter  ItemChatListBinding !com.sr.ghostencryptedchat.adapter  ItemContactBinding !com.sr.ghostencryptedchat.adapter  ItemDirectMessageBinding !com.sr.ghostencryptedchat.adapter  ItemDmMessageBinding !com.sr.ghostencryptedchat.adapter  ItemDmRequestBinding !com.sr.ghostencryptedchat.adapter  ItemMessageBinding !com.sr.ghostencryptedchat.adapter  ItemMessageMeBinding !com.sr.ghostencryptedchat.adapter  ItemMessageOtherBinding !com.sr.ghostencryptedchat.adapter  LayoutInflater !com.sr.ghostencryptedchat.adapter  List !com.sr.ghostencryptedchat.adapter  Locale !com.sr.ghostencryptedchat.adapter  Log !com.sr.ghostencryptedchat.adapter  Long !com.sr.ghostencryptedchat.adapter  Pair !com.sr.ghostencryptedchat.adapter  PublicChatAdapter !com.sr.ghostencryptedchat.adapter  R !com.sr.ghostencryptedchat.adapter  RecyclerView !com.sr.ghostencryptedchat.adapter  SimpleContactsAdapter !com.sr.ghostencryptedchat.adapter  SimpleDateFormat !com.sr.ghostencryptedchat.adapter  String !com.sr.ghostencryptedchat.adapter  Unit !com.sr.ghostencryptedchat.adapter  View !com.sr.ghostencryptedchat.adapter  android !com.sr.ghostencryptedchat.adapter  apply !com.sr.ghostencryptedchat.adapter  getItem !com.sr.ghostencryptedchat.adapter  listOf !com.sr.ghostencryptedchat.adapter  mutableMapOf !com.sr.ghostencryptedchat.adapter  onClick !com.sr.ghostencryptedchat.adapter  onItemClick !com.sr.ghostencryptedchat.adapter  set !com.sr.ghostencryptedchat.adapter  take !com.sr.ghostencryptedchat.adapter  ChatItem -com.sr.ghostencryptedchat.adapter.ChatAdapter  ChatViewHolder -com.sr.ghostencryptedchat.adapter.ChatAdapter  	ImageView -com.sr.ghostencryptedchat.adapter.ChatAdapter  Int -com.sr.ghostencryptedchat.adapter.ChatAdapter  LayoutInflater -com.sr.ghostencryptedchat.adapter.ChatAdapter  List -com.sr.ghostencryptedchat.adapter.ChatAdapter  R -com.sr.ghostencryptedchat.adapter.ChatAdapter  RecyclerView -com.sr.ghostencryptedchat.adapter.ChatAdapter  TextView -com.sr.ghostencryptedchat.adapter.ChatAdapter  View -com.sr.ghostencryptedchat.adapter.ChatAdapter  	ViewGroup -com.sr.ghostencryptedchat.adapter.ChatAdapter  items -com.sr.ghostencryptedchat.adapter.ChatAdapter  	ImageView <com.sr.ghostencryptedchat.adapter.ChatAdapter.ChatViewHolder  R <com.sr.ghostencryptedchat.adapter.ChatAdapter.ChatViewHolder  TextView <com.sr.ghostencryptedchat.adapter.ChatAdapter.ChatViewHolder  View <com.sr.ghostencryptedchat.adapter.ChatAdapter.ChatViewHolder  avatar <com.sr.ghostencryptedchat.adapter.ChatAdapter.ChatViewHolder  name <com.sr.ghostencryptedchat.adapter.ChatAdapter.ChatViewHolder  preview <com.sr.ghostencryptedchat.adapter.ChatAdapter.ChatViewHolder  time <com.sr.ghostencryptedchat.adapter.ChatAdapter.ChatViewHolder  unread <com.sr.ghostencryptedchat.adapter.ChatAdapter.ChatViewHolder  Boolean 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  ChatViewHolder 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  Color 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  Int 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  ItemChatListBinding 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  LayoutInflater 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  List 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  Pair 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  R 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  RecyclerView 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  String 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  Unit 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  View 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  	ViewGroup 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  android 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  apply 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  
getANDROID 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  getAPPLY 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  
getAndroid 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  getApply 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  invoke 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  items 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  notifyDataSetChanged 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  onClick 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  onLongClick 1com.sr.ghostencryptedchat.adapter.ChatListAdapter  ItemChatListBinding @com.sr.ghostencryptedchat.adapter.ChatListAdapter.ChatViewHolder  binding @com.sr.ghostencryptedchat.adapter.ChatListAdapter.ChatViewHolder  Int 0com.sr.ghostencryptedchat.adapter.ContactAdapter  ItemContactBinding 0com.sr.ghostencryptedchat.adapter.ContactAdapter  LayoutInflater 0com.sr.ghostencryptedchat.adapter.ContactAdapter  List 0com.sr.ghostencryptedchat.adapter.ContactAdapter  RecyclerView 0com.sr.ghostencryptedchat.adapter.ContactAdapter  String 0com.sr.ghostencryptedchat.adapter.ContactAdapter  Unit 0com.sr.ghostencryptedchat.adapter.ContactAdapter  	ViewGroup 0com.sr.ghostencryptedchat.adapter.ContactAdapter  
ViewHolder 0com.sr.ghostencryptedchat.adapter.ContactAdapter  invoke 0com.sr.ghostencryptedchat.adapter.ContactAdapter  onClick 0com.sr.ghostencryptedchat.adapter.ContactAdapter  users 0com.sr.ghostencryptedchat.adapter.ContactAdapter  ItemContactBinding ;com.sr.ghostencryptedchat.adapter.ContactAdapter.ViewHolder  String ;com.sr.ghostencryptedchat.adapter.ContactAdapter.ViewHolder  bind ;com.sr.ghostencryptedchat.adapter.ContactAdapter.ViewHolder  binding ;com.sr.ghostencryptedchat.adapter.ContactAdapter.ViewHolder  
getONClick ;com.sr.ghostencryptedchat.adapter.ContactAdapter.ViewHolder  
getOnClick ;com.sr.ghostencryptedchat.adapter.ContactAdapter.ViewHolder  invoke ;com.sr.ghostencryptedchat.adapter.ContactAdapter.ViewHolder  onClick ;com.sr.ghostencryptedchat.adapter.ContactAdapter.ViewHolder  ChatMessage +com.sr.ghostencryptedchat.adapter.DMAdapter  EncryptionUtil +com.sr.ghostencryptedchat.adapter.DMAdapter  	Exception +com.sr.ghostencryptedchat.adapter.DMAdapter  Int +com.sr.ghostencryptedchat.adapter.DMAdapter  ItemMessageMeBinding +com.sr.ghostencryptedchat.adapter.DMAdapter  ItemMessageOtherBinding +com.sr.ghostencryptedchat.adapter.DMAdapter  LayoutInflater +com.sr.ghostencryptedchat.adapter.DMAdapter  List +com.sr.ghostencryptedchat.adapter.DMAdapter  Log +com.sr.ghostencryptedchat.adapter.DMAdapter  MeViewHolder +com.sr.ghostencryptedchat.adapter.DMAdapter  OtherViewHolder +com.sr.ghostencryptedchat.adapter.DMAdapter  RecyclerView +com.sr.ghostencryptedchat.adapter.DMAdapter  String +com.sr.ghostencryptedchat.adapter.DMAdapter  VIEW_TYPE_ME +com.sr.ghostencryptedchat.adapter.DMAdapter  VIEW_TYPE_OTHER +com.sr.ghostencryptedchat.adapter.DMAdapter  	ViewGroup +com.sr.ghostencryptedchat.adapter.DMAdapter  android +com.sr.ghostencryptedchat.adapter.DMAdapter  currentUser +com.sr.ghostencryptedchat.adapter.DMAdapter  
getANDROID +com.sr.ghostencryptedchat.adapter.DMAdapter  
getAndroid +com.sr.ghostencryptedchat.adapter.DMAdapter  getTAKE +com.sr.ghostencryptedchat.adapter.DMAdapter  getTake +com.sr.ghostencryptedchat.adapter.DMAdapter  messages +com.sr.ghostencryptedchat.adapter.DMAdapter  notifyDataSetChanged +com.sr.ghostencryptedchat.adapter.DMAdapter  take +com.sr.ghostencryptedchat.adapter.DMAdapter  ItemMessageMeBinding 8com.sr.ghostencryptedchat.adapter.DMAdapter.MeViewHolder  binding 8com.sr.ghostencryptedchat.adapter.DMAdapter.MeViewHolder  ItemMessageOtherBinding ;com.sr.ghostencryptedchat.adapter.DMAdapter.OtherViewHolder  binding ;com.sr.ghostencryptedchat.adapter.DMAdapter.OtherViewHolder  	DMMessage /com.sr.ghostencryptedchat.adapter.DMChatAdapter  DMViewHolder /com.sr.ghostencryptedchat.adapter.DMChatAdapter  EncryptionUtil /com.sr.ghostencryptedchat.adapter.DMChatAdapter  Int /com.sr.ghostencryptedchat.adapter.DMChatAdapter  ItemDmMessageBinding /com.sr.ghostencryptedchat.adapter.DMChatAdapter  LayoutInflater /com.sr.ghostencryptedchat.adapter.DMChatAdapter  List /com.sr.ghostencryptedchat.adapter.DMChatAdapter  RecyclerView /com.sr.ghostencryptedchat.adapter.DMChatAdapter  String /com.sr.ghostencryptedchat.adapter.DMChatAdapter  	ViewGroup /com.sr.ghostencryptedchat.adapter.DMChatAdapter  android /com.sr.ghostencryptedchat.adapter.DMChatAdapter  
getANDROID /com.sr.ghostencryptedchat.adapter.DMChatAdapter  
getAndroid /com.sr.ghostencryptedchat.adapter.DMChatAdapter  messages /com.sr.ghostencryptedchat.adapter.DMChatAdapter  username /com.sr.ghostencryptedchat.adapter.DMChatAdapter  ItemDmMessageBinding <com.sr.ghostencryptedchat.adapter.DMChatAdapter.DMViewHolder  binding <com.sr.ghostencryptedchat.adapter.DMChatAdapter.DMViewHolder  	DMRequest 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  DMRequestViewHolder 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  Int 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  ItemDmRequestBinding 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  LayoutInflater 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  List 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  RecyclerView 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  Unit 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  	ViewGroup 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  invoke 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  notifyDataSetChanged 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  onAccept 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  onReject 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  requests 2com.sr.ghostencryptedchat.adapter.DMRequestAdapter  ItemDmRequestBinding Fcom.sr.ghostencryptedchat.adapter.DMRequestAdapter.DMRequestViewHolder  binding Fcom.sr.ghostencryptedchat.adapter.DMRequestAdapter.DMRequestViewHolder  Boolean 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  DiffUtil 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  
DirectMessage 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  DirectMessageDiffCallback 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  Int 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  ItemDirectMessageBinding 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  LayoutInflater 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  RecyclerView 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  Unit 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  	ViewGroup 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  
ViewHolder 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  android 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  apply 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  
getANDROID 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  getAPPLY 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  
getAndroid 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  getApply 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  getItem 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  invoke 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  onItemClick 6com.sr.ghostencryptedchat.adapter.DirectMessageAdapter  Boolean Pcom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.DirectMessageDiffCallback  
DirectMessage Pcom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.DirectMessageDiffCallback  
DirectMessage Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  ItemDirectMessageBinding Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  RecyclerView Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  android Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  apply Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  bind Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  binding Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  bindingAdapterPosition Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  
getANDROID Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  getAPPLY Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  
getAndroid Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  getApply Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  getBINDINGAdapterPosition Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  getBindingAdapterPosition Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  
getGETItem Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  
getGetItem Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  getItem Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  getONItemClick Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  getOnItemClick Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  invoke Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  onItemClick Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  setBindingAdapterPosition Acom.sr.ghostencryptedchat.adapter.DirectMessageAdapter.ViewHolder  ChatMessage 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  ChatViewHolder 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  Color 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  Date 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  EncryptionUtil 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  Int 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  ItemMessageBinding 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  LayoutInflater 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  List 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  Locale 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  Long 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  R 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  RecyclerView 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  SimpleDateFormat 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  String 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  View 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  	ViewGroup 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  apply 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  	colorList 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  currentUsername 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  formatTimestamp 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  getAPPLY 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  getApply 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  	getLISTOf 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  	getListOf 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  getMUTABLEMapOf 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  getMutableMapOf 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  getSET 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  getSet 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  getUserColor 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  listOf 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  messages 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  mutableMapOf 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  notifyDataSetChanged 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  set 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  
userColors 3com.sr.ghostencryptedchat.adapter.PublicChatAdapter  ItemMessageBinding Bcom.sr.ghostencryptedchat.adapter.PublicChatAdapter.ChatViewHolder  binding Bcom.sr.ghostencryptedchat.adapter.PublicChatAdapter.ChatViewHolder  Boolean 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  ContactViewHolder 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  
ContextCompat 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  Int 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  ItemContactBinding 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  LayoutInflater 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  List 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  Pair 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  R 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  RecyclerView 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  String 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  Unit 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  	ViewGroup 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  android 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  
getANDROID 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  
getAndroid 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  invoke 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  notifyDataSetChanged 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  onClick 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  onLongClick 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  users 7com.sr.ghostencryptedchat.adapter.SimpleContactsAdapter  ItemContactBinding Icom.sr.ghostencryptedchat.adapter.SimpleContactsAdapter.ContactViewHolder  binding Icom.sr.ghostencryptedchat.adapter.SimpleContactsAdapter.ContactViewHolder  itemView Icom.sr.ghostencryptedchat.adapter.SimpleContactsAdapter.ContactViewHolder  ActivityDmChatBinding %com.sr.ghostencryptedchat.databinding  ActivityDmRequestBinding %com.sr.ghostencryptedchat.databinding  ActivityEncryptedChatBinding %com.sr.ghostencryptedchat.databinding  FragmentChatsBinding %com.sr.ghostencryptedchat.databinding  FragmentContactsBinding %com.sr.ghostencryptedchat.databinding  FragmentGlobalChatBinding %com.sr.ghostencryptedchat.databinding  FragmentWalletBinding %com.sr.ghostencryptedchat.databinding  ItemChatListBinding %com.sr.ghostencryptedchat.databinding  ItemContactBinding %com.sr.ghostencryptedchat.databinding  ItemDirectMessageBinding %com.sr.ghostencryptedchat.databinding  ItemDmMessageBinding %com.sr.ghostencryptedchat.databinding  ItemDmRequestBinding %com.sr.ghostencryptedchat.databinding  ItemMessageBinding %com.sr.ghostencryptedchat.databinding  ItemMessageMeBinding %com.sr.ghostencryptedchat.databinding  ItemMessageOtherBinding %com.sr.ghostencryptedchat.databinding  addToContactsButton ;com.sr.ghostencryptedchat.databinding.ActivityDmChatBinding  	bottomNav ;com.sr.ghostencryptedchat.databinding.ActivityDmChatBinding  	chatTitle ;com.sr.ghostencryptedchat.databinding.ActivityDmChatBinding  dmMessageInput ;com.sr.ghostencryptedchat.databinding.ActivityDmChatBinding  dmRecyclerView ;com.sr.ghostencryptedchat.databinding.ActivityDmChatBinding  getROOT ;com.sr.ghostencryptedchat.databinding.ActivityDmChatBinding  getRoot ;com.sr.ghostencryptedchat.databinding.ActivityDmChatBinding  inflate ;com.sr.ghostencryptedchat.databinding.ActivityDmChatBinding  root ;com.sr.ghostencryptedchat.databinding.ActivityDmChatBinding  sendDMButton ;com.sr.ghostencryptedchat.databinding.ActivityDmChatBinding  setRoot ;com.sr.ghostencryptedchat.databinding.ActivityDmChatBinding  
dmRequestList >com.sr.ghostencryptedchat.databinding.ActivityDmRequestBinding  getROOT >com.sr.ghostencryptedchat.databinding.ActivityDmRequestBinding  getRoot >com.sr.ghostencryptedchat.databinding.ActivityDmRequestBinding  inflate >com.sr.ghostencryptedchat.databinding.ActivityDmRequestBinding  root >com.sr.ghostencryptedchat.databinding.ActivityDmRequestBinding  setRoot >com.sr.ghostencryptedchat.databinding.ActivityDmRequestBinding  	bottomNav Bcom.sr.ghostencryptedchat.databinding.ActivityEncryptedChatBinding  getROOT Bcom.sr.ghostencryptedchat.databinding.ActivityEncryptedChatBinding  getRoot Bcom.sr.ghostencryptedchat.databinding.ActivityEncryptedChatBinding  inflate Bcom.sr.ghostencryptedchat.databinding.ActivityEncryptedChatBinding  root Bcom.sr.ghostencryptedchat.databinding.ActivityEncryptedChatBinding  setRoot Bcom.sr.ghostencryptedchat.databinding.ActivityEncryptedChatBinding  backgroundView :com.sr.ghostencryptedchat.databinding.FragmentChatsBinding  
chatLayout :com.sr.ghostencryptedchat.databinding.FragmentChatsBinding  chatListRecycler :com.sr.ghostencryptedchat.databinding.FragmentChatsBinding  	chatTitle :com.sr.ghostencryptedchat.databinding.FragmentChatsBinding  getROOT :com.sr.ghostencryptedchat.databinding.FragmentChatsBinding  getRoot :com.sr.ghostencryptedchat.databinding.FragmentChatsBinding  inflate :com.sr.ghostencryptedchat.databinding.FragmentChatsBinding  root :com.sr.ghostencryptedchat.databinding.FragmentChatsBinding  setRoot :com.sr.ghostencryptedchat.databinding.FragmentChatsBinding  contactSearchInput =com.sr.ghostencryptedchat.databinding.FragmentContactsBinding  contactsRecycler =com.sr.ghostencryptedchat.databinding.FragmentContactsBinding  getROOT =com.sr.ghostencryptedchat.databinding.FragmentContactsBinding  getRoot =com.sr.ghostencryptedchat.databinding.FragmentContactsBinding  inflate =com.sr.ghostencryptedchat.databinding.FragmentContactsBinding  root =com.sr.ghostencryptedchat.databinding.FragmentContactsBinding  setRoot =com.sr.ghostencryptedchat.databinding.FragmentContactsBinding  equals ?com.sr.ghostencryptedchat.databinding.FragmentGlobalChatBinding  getROOT ?com.sr.ghostencryptedchat.databinding.FragmentGlobalChatBinding  getRoot ?com.sr.ghostencryptedchat.databinding.FragmentGlobalChatBinding  inflate ?com.sr.ghostencryptedchat.databinding.FragmentGlobalChatBinding  messageInput ?com.sr.ghostencryptedchat.databinding.FragmentGlobalChatBinding  recyclerView ?com.sr.ghostencryptedchat.databinding.FragmentGlobalChatBinding  root ?com.sr.ghostencryptedchat.databinding.FragmentGlobalChatBinding  
sendButton ?com.sr.ghostencryptedchat.databinding.FragmentGlobalChatBinding  setRoot ?com.sr.ghostencryptedchat.databinding.FragmentGlobalChatBinding  getROOT ;com.sr.ghostencryptedchat.databinding.FragmentWalletBinding  getRoot ;com.sr.ghostencryptedchat.databinding.FragmentWalletBinding  inflate ;com.sr.ghostencryptedchat.databinding.FragmentWalletBinding  progressBar ;com.sr.ghostencryptedchat.databinding.FragmentWalletBinding  root ;com.sr.ghostencryptedchat.databinding.FragmentWalletBinding  setRoot ;com.sr.ghostencryptedchat.databinding.FragmentWalletBinding  webView ;com.sr.ghostencryptedchat.databinding.FragmentWalletBinding  chatIcon 9com.sr.ghostencryptedchat.databinding.ItemChatListBinding  chatName 9com.sr.ghostencryptedchat.databinding.ItemChatListBinding  chatNewLabel 9com.sr.ghostencryptedchat.databinding.ItemChatListBinding  getROOT 9com.sr.ghostencryptedchat.databinding.ItemChatListBinding  getRoot 9com.sr.ghostencryptedchat.databinding.ItemChatListBinding  inflate 9com.sr.ghostencryptedchat.databinding.ItemChatListBinding  root 9com.sr.ghostencryptedchat.databinding.ItemChatListBinding  setRoot 9com.sr.ghostencryptedchat.databinding.ItemChatListBinding  contactName 8com.sr.ghostencryptedchat.databinding.ItemContactBinding  getROOT 8com.sr.ghostencryptedchat.databinding.ItemContactBinding  getRoot 8com.sr.ghostencryptedchat.databinding.ItemContactBinding  inflate 8com.sr.ghostencryptedchat.databinding.ItemContactBinding  root 8com.sr.ghostencryptedchat.databinding.ItemContactBinding  setRoot 8com.sr.ghostencryptedchat.databinding.ItemContactBinding  
statusText 8com.sr.ghostencryptedchat.databinding.ItemContactBinding  android >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  apply >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  
getANDROID >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  getAPPLY >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  
getAndroid >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  getApply >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  getROOT >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  getRoot >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  inflate >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  lastMessage >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  newIndicator >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  
recipientName >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  root >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  setRoot >com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding  getROOT :com.sr.ghostencryptedchat.databinding.ItemDmMessageBinding  getRoot :com.sr.ghostencryptedchat.databinding.ItemDmMessageBinding  inflate :com.sr.ghostencryptedchat.databinding.ItemDmMessageBinding  messageText :com.sr.ghostencryptedchat.databinding.ItemDmMessageBinding  root :com.sr.ghostencryptedchat.databinding.ItemDmMessageBinding  setRoot :com.sr.ghostencryptedchat.databinding.ItemDmMessageBinding  acceptButton :com.sr.ghostencryptedchat.databinding.ItemDmRequestBinding  getROOT :com.sr.ghostencryptedchat.databinding.ItemDmRequestBinding  getRoot :com.sr.ghostencryptedchat.databinding.ItemDmRequestBinding  inflate :com.sr.ghostencryptedchat.databinding.ItemDmRequestBinding  rejectButton :com.sr.ghostencryptedchat.databinding.ItemDmRequestBinding  requestText :com.sr.ghostencryptedchat.databinding.ItemDmRequestBinding  root :com.sr.ghostencryptedchat.databinding.ItemDmRequestBinding  setRoot :com.sr.ghostencryptedchat.databinding.ItemDmRequestBinding  getROOT 8com.sr.ghostencryptedchat.databinding.ItemMessageBinding  getRoot 8com.sr.ghostencryptedchat.databinding.ItemMessageBinding  inflate 8com.sr.ghostencryptedchat.databinding.ItemMessageBinding  messageContainer 8com.sr.ghostencryptedchat.databinding.ItemMessageBinding  messageText 8com.sr.ghostencryptedchat.databinding.ItemMessageBinding  root 8com.sr.ghostencryptedchat.databinding.ItemMessageBinding  
senderName 8com.sr.ghostencryptedchat.databinding.ItemMessageBinding  setRoot 8com.sr.ghostencryptedchat.databinding.ItemMessageBinding  	timestamp 8com.sr.ghostencryptedchat.databinding.ItemMessageBinding  getROOT :com.sr.ghostencryptedchat.databinding.ItemMessageMeBinding  getRoot :com.sr.ghostencryptedchat.databinding.ItemMessageMeBinding  inflate :com.sr.ghostencryptedchat.databinding.ItemMessageMeBinding  root :com.sr.ghostencryptedchat.databinding.ItemMessageMeBinding  setRoot :com.sr.ghostencryptedchat.databinding.ItemMessageMeBinding  textMessage :com.sr.ghostencryptedchat.databinding.ItemMessageMeBinding  	timestamp :com.sr.ghostencryptedchat.databinding.ItemMessageMeBinding  getROOT =com.sr.ghostencryptedchat.databinding.ItemMessageOtherBinding  getRoot =com.sr.ghostencryptedchat.databinding.ItemMessageOtherBinding  inflate =com.sr.ghostencryptedchat.databinding.ItemMessageOtherBinding  root =com.sr.ghostencryptedchat.databinding.ItemMessageOtherBinding  setRoot =com.sr.ghostencryptedchat.databinding.ItemMessageOtherBinding  textMessage =com.sr.ghostencryptedchat.databinding.ItemMessageOtherBinding  	timestamp =com.sr.ghostencryptedchat.databinding.ItemMessageOtherBinding  Boolean com.sr.ghostencryptedchat.model  ChatItem com.sr.ghostencryptedchat.model  ChatMessage com.sr.ghostencryptedchat.model  Contact com.sr.ghostencryptedchat.model  ContactUser com.sr.ghostencryptedchat.model  	DMMessage com.sr.ghostencryptedchat.model  	DMRequest com.sr.ghostencryptedchat.model  
DirectMessage com.sr.ghostencryptedchat.model  Int com.sr.ghostencryptedchat.model  List com.sr.ghostencryptedchat.model  Long com.sr.ghostencryptedchat.model  String com.sr.ghostencryptedchat.model  
UpdateInfo com.sr.ghostencryptedchat.model  listOf com.sr.ghostencryptedchat.model  Int (com.sr.ghostencryptedchat.model.ChatItem  String (com.sr.ghostencryptedchat.model.ChatItem  avatarResId (com.sr.ghostencryptedchat.model.ChatItem  messagePreview (com.sr.ghostencryptedchat.model.ChatItem  name (com.sr.ghostencryptedchat.model.ChatItem  	timestamp (com.sr.ghostencryptedchat.model.ChatItem  unreadCount (com.sr.ghostencryptedchat.model.ChatItem  Long +com.sr.ghostencryptedchat.model.ChatMessage  String +com.sr.ghostencryptedchat.model.ChatMessage  equals +com.sr.ghostencryptedchat.model.ChatMessage  message +com.sr.ghostencryptedchat.model.ChatMessage  sender +com.sr.ghostencryptedchat.model.ChatMessage  	timestamp +com.sr.ghostencryptedchat.model.ChatMessage  Long 'com.sr.ghostencryptedchat.model.Contact  String 'com.sr.ghostencryptedchat.model.Contact  String +com.sr.ghostencryptedchat.model.ContactUser  Long )com.sr.ghostencryptedchat.model.DMMessage  String )com.sr.ghostencryptedchat.model.DMMessage  content )com.sr.ghostencryptedchat.model.DMMessage  from )com.sr.ghostencryptedchat.model.DMMessage  Long )com.sr.ghostencryptedchat.model.DMRequest  String )com.sr.ghostencryptedchat.model.DMRequest  apply )com.sr.ghostencryptedchat.model.DMRequest  from )com.sr.ghostencryptedchat.model.DMRequest  getAPPLY )com.sr.ghostencryptedchat.model.DMRequest  getApply )com.sr.ghostencryptedchat.model.DMRequest  id )com.sr.ghostencryptedchat.model.DMRequest  to )com.sr.ghostencryptedchat.model.DMRequest  Boolean -com.sr.ghostencryptedchat.model.DirectMessage  List -com.sr.ghostencryptedchat.model.DirectMessage  Long -com.sr.ghostencryptedchat.model.DirectMessage  String -com.sr.ghostencryptedchat.model.DirectMessage  id -com.sr.ghostencryptedchat.model.DirectMessage  isNew -com.sr.ghostencryptedchat.model.DirectMessage  lastMessage -com.sr.ghostencryptedchat.model.DirectMessage  lastMessageTime -com.sr.ghostencryptedchat.model.DirectMessage  listOf -com.sr.ghostencryptedchat.model.DirectMessage  participants -com.sr.ghostencryptedchat.model.DirectMessage  recipientId -com.sr.ghostencryptedchat.model.DirectMessage  
recipientName -com.sr.ghostencryptedchat.model.DirectMessage  Int *com.sr.ghostencryptedchat.model.UpdateInfo  String *com.sr.ghostencryptedchat.model.UpdateInfo  apkUrl *com.sr.ghostencryptedchat.model.UpdateInfo  equals *com.sr.ghostencryptedchat.model.UpdateInfo  versionCode *com.sr.ghostencryptedchat.model.UpdateInfo  versionName *com.sr.ghostencryptedchat.model.UpdateInfo  AlertDialog com.sr.ghostencryptedchat.ui  
BackupUtil com.sr.ghostencryptedchat.ui  Boolean com.sr.ghostencryptedchat.ui  CharSequence com.sr.ghostencryptedchat.ui  ChatListAdapter com.sr.ghostencryptedchat.ui  ChatMessage com.sr.ghostencryptedchat.ui  
ChatsFragment com.sr.ghostencryptedchat.ui  Color com.sr.ghostencryptedchat.ui  ContactsFragment com.sr.ghostencryptedchat.ui  DMChatActivity com.sr.ghostencryptedchat.ui  EncryptionUtil com.sr.ghostencryptedchat.ui  	Exception com.sr.ghostencryptedchat.ui  FirebaseFirestore com.sr.ghostencryptedchat.ui  FragmentChatsBinding com.sr.ghostencryptedchat.ui  FragmentContactsBinding com.sr.ghostencryptedchat.ui  FragmentGlobalChatBinding com.sr.ghostencryptedchat.ui  FragmentWalletBinding com.sr.ghostencryptedchat.ui  GlobalChatFragment com.sr.ghostencryptedchat.ui  Int com.sr.ghostencryptedchat.ui  Intent com.sr.ghostencryptedchat.ui  KeyStoreUtil com.sr.ghostencryptedchat.ui  LinearLayoutManager com.sr.ghostencryptedchat.ui  List com.sr.ghostencryptedchat.ui  Map com.sr.ghostencryptedchat.ui  Number com.sr.ghostencryptedchat.ui  Pair com.sr.ghostencryptedchat.ui  PublicChatAdapter com.sr.ghostencryptedchat.ui  R com.sr.ghostencryptedchat.ui  SettingsFragment com.sr.ghostencryptedchat.ui  SimpleContactsAdapter com.sr.ghostencryptedchat.ui  String com.sr.ghostencryptedchat.ui  System com.sr.ghostencryptedchat.ui  Toast com.sr.ghostencryptedchat.ui  View com.sr.ghostencryptedchat.ui  WalletFragment com.sr.ghostencryptedchat.ui  WebSettings com.sr.ghostencryptedchat.ui  android com.sr.ghostencryptedchat.ui  any com.sr.ghostencryptedchat.ui  contains com.sr.ghostencryptedchat.ui  context com.sr.ghostencryptedchat.ui  	emptyList com.sr.ghostencryptedchat.ui  fetchContactsAndIncomingDMs com.sr.ghostencryptedchat.ui  filterIsInstance com.sr.ghostencryptedchat.ui  firstOrNull com.sr.ghostencryptedchat.ui  get com.sr.ghostencryptedchat.ui  
isInitialized com.sr.ghostencryptedchat.ui  
isNotBlank com.sr.ghostencryptedchat.ui  
isNotEmpty com.sr.ghostencryptedchat.ui  java com.sr.ghostencryptedchat.ui  joinToString com.sr.ghostencryptedchat.ui  launch com.sr.ghostencryptedchat.ui  lifecycleScope com.sr.ghostencryptedchat.ui  listOf com.sr.ghostencryptedchat.ui  	lowercase com.sr.ghostencryptedchat.ui  map com.sr.ghostencryptedchat.ui  
mapNotNull com.sr.ghostencryptedchat.ui  mapOf com.sr.ghostencryptedchat.ui  
mutableListOf com.sr.ghostencryptedchat.ui  progressBar com.sr.ghostencryptedchat.ui  requireContext com.sr.ghostencryptedchat.ui  requireView com.sr.ghostencryptedchat.ui  
searchUser com.sr.ghostencryptedchat.ui  showSeedPhraseDialog com.sr.ghostencryptedchat.ui  sorted com.sr.ghostencryptedchat.ui  to com.sr.ghostencryptedchat.ui  
toMutableList com.sr.ghostencryptedchat.ui  toString com.sr.ghostencryptedchat.ui  trim com.sr.ghostencryptedchat.ui  updateDisplayedUsername com.sr.ghostencryptedchat.ui  AlertDialog *com.sr.ghostencryptedchat.ui.ChatsFragment  Boolean *com.sr.ghostencryptedchat.ui.ChatsFragment  Bundle *com.sr.ghostencryptedchat.ui.ChatsFragment  ChatListAdapter *com.sr.ghostencryptedchat.ui.ChatsFragment  Color *com.sr.ghostencryptedchat.ui.ChatsFragment  DMChatActivity *com.sr.ghostencryptedchat.ui.ChatsFragment  FirebaseFirestore *com.sr.ghostencryptedchat.ui.ChatsFragment  FragmentChatsBinding *com.sr.ghostencryptedchat.ui.ChatsFragment  GlobalChatFragment *com.sr.ghostencryptedchat.ui.ChatsFragment  Intent *com.sr.ghostencryptedchat.ui.ChatsFragment  KeyStoreUtil *com.sr.ghostencryptedchat.ui.ChatsFragment  LayoutInflater *com.sr.ghostencryptedchat.ui.ChatsFragment  LinearLayoutManager *com.sr.ghostencryptedchat.ui.ChatsFragment  List *com.sr.ghostencryptedchat.ui.ChatsFragment  Map *com.sr.ghostencryptedchat.ui.ChatsFragment  Number *com.sr.ghostencryptedchat.ui.ChatsFragment  Pair *com.sr.ghostencryptedchat.ui.ChatsFragment  R *com.sr.ghostencryptedchat.ui.ChatsFragment  String *com.sr.ghostencryptedchat.ui.ChatsFragment  Toast *com.sr.ghostencryptedchat.ui.ChatsFragment  View *com.sr.ghostencryptedchat.ui.ChatsFragment  	ViewGroup *com.sr.ghostencryptedchat.ui.ChatsFragment  _binding *com.sr.ghostencryptedchat.ui.ChatsFragment  adapter *com.sr.ghostencryptedchat.ui.ChatsFragment  any *com.sr.ghostencryptedchat.ui.ChatsFragment  binding *com.sr.ghostencryptedchat.ui.ChatsFragment  chatList *com.sr.ghostencryptedchat.ui.ChatsFragment  db *com.sr.ghostencryptedchat.ui.ChatsFragment  
deleteChat *com.sr.ghostencryptedchat.ui.ChatsFragment  equals *com.sr.ghostencryptedchat.ui.ChatsFragment  
fetchChatList *com.sr.ghostencryptedchat.ui.ChatsFragment  firstOrNull *com.sr.ghostencryptedchat.ui.ChatsFragment  get *com.sr.ghostencryptedchat.ui.ChatsFragment  getANY *com.sr.ghostencryptedchat.ui.ChatsFragment  getAny *com.sr.ghostencryptedchat.ui.ChatsFragment  getFIRSTOrNull *com.sr.ghostencryptedchat.ui.ChatsFragment  getFirstOrNull *com.sr.ghostencryptedchat.ui.ChatsFragment  getGET *com.sr.ghostencryptedchat.ui.ChatsFragment  getGet *com.sr.ghostencryptedchat.ui.ChatsFragment  getISHidden *com.sr.ghostencryptedchat.ui.ChatsFragment  getIsHidden *com.sr.ghostencryptedchat.ui.ChatsFragment  getJOINToString *com.sr.ghostencryptedchat.ui.ChatsFragment  getJoinToString *com.sr.ghostencryptedchat.ui.ChatsFragment  getLET *com.sr.ghostencryptedchat.ui.ChatsFragment  	getLISTOf *com.sr.ghostencryptedchat.ui.ChatsFragment  getLet *com.sr.ghostencryptedchat.ui.ChatsFragment  	getListOf *com.sr.ghostencryptedchat.ui.ChatsFragment  getMUTABLEListOf *com.sr.ghostencryptedchat.ui.ChatsFragment  getMutableListOf *com.sr.ghostencryptedchat.ui.ChatsFragment  getPARENTFragmentManager *com.sr.ghostencryptedchat.ui.ChatsFragment  getParentFragmentManager *com.sr.ghostencryptedchat.ui.ChatsFragment  	getSORTED *com.sr.ghostencryptedchat.ui.ChatsFragment  	getSorted *com.sr.ghostencryptedchat.ui.ChatsFragment  isHidden *com.sr.ghostencryptedchat.ui.ChatsFragment  java *com.sr.ghostencryptedchat.ui.ChatsFragment  joinToString *com.sr.ghostencryptedchat.ui.ChatsFragment  let *com.sr.ghostencryptedchat.ui.ChatsFragment  listOf *com.sr.ghostencryptedchat.ui.ChatsFragment  
mutableListOf *com.sr.ghostencryptedchat.ui.ChatsFragment  parentFragmentManager *com.sr.ghostencryptedchat.ui.ChatsFragment  requireContext *com.sr.ghostencryptedchat.ui.ChatsFragment  	setHidden *com.sr.ghostencryptedchat.ui.ChatsFragment  setParentFragmentManager *com.sr.ghostencryptedchat.ui.ChatsFragment  showDeleteDialog *com.sr.ghostencryptedchat.ui.ChatsFragment  sorted *com.sr.ghostencryptedchat.ui.ChatsFragment  
startActivity *com.sr.ghostencryptedchat.ui.ChatsFragment  toggleMatrixBackground *com.sr.ghostencryptedchat.ui.ChatsFragment  username *com.sr.ghostencryptedchat.ui.ChatsFragment  AlertDialog -com.sr.ghostencryptedchat.ui.ContactsFragment  Boolean -com.sr.ghostencryptedchat.ui.ContactsFragment  Bundle -com.sr.ghostencryptedchat.ui.ContactsFragment  CharSequence -com.sr.ghostencryptedchat.ui.ContactsFragment  DMChatActivity -com.sr.ghostencryptedchat.ui.ContactsFragment  Editable -com.sr.ghostencryptedchat.ui.ContactsFragment  FirebaseFirestore -com.sr.ghostencryptedchat.ui.ContactsFragment  FragmentContactsBinding -com.sr.ghostencryptedchat.ui.ContactsFragment  Int -com.sr.ghostencryptedchat.ui.ContactsFragment  Intent -com.sr.ghostencryptedchat.ui.ContactsFragment  KeyStoreUtil -com.sr.ghostencryptedchat.ui.ContactsFragment  LayoutInflater -com.sr.ghostencryptedchat.ui.ContactsFragment  LinearLayoutManager -com.sr.ghostencryptedchat.ui.ContactsFragment  List -com.sr.ghostencryptedchat.ui.ContactsFragment  Pair -com.sr.ghostencryptedchat.ui.ContactsFragment  SimpleContactsAdapter -com.sr.ghostencryptedchat.ui.ContactsFragment  String -com.sr.ghostencryptedchat.ui.ContactsFragment  TextWatcher -com.sr.ghostencryptedchat.ui.ContactsFragment  Toast -com.sr.ghostencryptedchat.ui.ContactsFragment  View -com.sr.ghostencryptedchat.ui.ContactsFragment  	ViewGroup -com.sr.ghostencryptedchat.ui.ContactsFragment  _binding -com.sr.ghostencryptedchat.ui.ContactsFragment  
acceptChat -com.sr.ghostencryptedchat.ui.ContactsFragment  adapter -com.sr.ghostencryptedchat.ui.ContactsFragment  any -com.sr.ghostencryptedchat.ui.ContactsFragment  binding -com.sr.ghostencryptedchat.ui.ContactsFragment  contactList -com.sr.ghostencryptedchat.ui.ContactsFragment  currentUser -com.sr.ghostencryptedchat.ui.ContactsFragment  db -com.sr.ghostencryptedchat.ui.ContactsFragment  
deleteContact -com.sr.ghostencryptedchat.ui.ContactsFragment  	emptyList -com.sr.ghostencryptedchat.ui.ContactsFragment  equals -com.sr.ghostencryptedchat.ui.ContactsFragment  fetchContactsAndIncomingDMs -com.sr.ghostencryptedchat.ui.ContactsFragment  filterIsInstance -com.sr.ghostencryptedchat.ui.ContactsFragment  firstOrNull -com.sr.ghostencryptedchat.ui.ContactsFragment  getANY -com.sr.ghostencryptedchat.ui.ContactsFragment  getAny -com.sr.ghostencryptedchat.ui.ContactsFragment  getEMPTYList -com.sr.ghostencryptedchat.ui.ContactsFragment  getEmptyList -com.sr.ghostencryptedchat.ui.ContactsFragment  getFILTERIsInstance -com.sr.ghostencryptedchat.ui.ContactsFragment  getFIRSTOrNull -com.sr.ghostencryptedchat.ui.ContactsFragment  getFilterIsInstance -com.sr.ghostencryptedchat.ui.ContactsFragment  getFirstOrNull -com.sr.ghostencryptedchat.ui.ContactsFragment  
getISNotEmpty -com.sr.ghostencryptedchat.ui.ContactsFragment  
getIsNotEmpty -com.sr.ghostencryptedchat.ui.ContactsFragment  getJOINToString -com.sr.ghostencryptedchat.ui.ContactsFragment  getJoinToString -com.sr.ghostencryptedchat.ui.ContactsFragment  	getLISTOf -com.sr.ghostencryptedchat.ui.ContactsFragment  getLOWERCASE -com.sr.ghostencryptedchat.ui.ContactsFragment  	getListOf -com.sr.ghostencryptedchat.ui.ContactsFragment  getLowercase -com.sr.ghostencryptedchat.ui.ContactsFragment  getMAP -com.sr.ghostencryptedchat.ui.ContactsFragment  getMAPOf -com.sr.ghostencryptedchat.ui.ContactsFragment  getMUTABLEListOf -com.sr.ghostencryptedchat.ui.ContactsFragment  getMap -com.sr.ghostencryptedchat.ui.ContactsFragment  getMapOf -com.sr.ghostencryptedchat.ui.ContactsFragment  getMutableListOf -com.sr.ghostencryptedchat.ui.ContactsFragment  	getSORTED -com.sr.ghostencryptedchat.ui.ContactsFragment  	getSorted -com.sr.ghostencryptedchat.ui.ContactsFragment  getTO -com.sr.ghostencryptedchat.ui.ContactsFragment  getTOMutableList -com.sr.ghostencryptedchat.ui.ContactsFragment  getTOString -com.sr.ghostencryptedchat.ui.ContactsFragment  getTRIM -com.sr.ghostencryptedchat.ui.ContactsFragment  getTo -com.sr.ghostencryptedchat.ui.ContactsFragment  getToMutableList -com.sr.ghostencryptedchat.ui.ContactsFragment  getToString -com.sr.ghostencryptedchat.ui.ContactsFragment  getTrim -com.sr.ghostencryptedchat.ui.ContactsFragment  
isNotEmpty -com.sr.ghostencryptedchat.ui.ContactsFragment  java -com.sr.ghostencryptedchat.ui.ContactsFragment  joinToString -com.sr.ghostencryptedchat.ui.ContactsFragment  listOf -com.sr.ghostencryptedchat.ui.ContactsFragment  	lowercase -com.sr.ghostencryptedchat.ui.ContactsFragment  map -com.sr.ghostencryptedchat.ui.ContactsFragment  mapOf -com.sr.ghostencryptedchat.ui.ContactsFragment  
mutableListOf -com.sr.ghostencryptedchat.ui.ContactsFragment  requireContext -com.sr.ghostencryptedchat.ui.ContactsFragment  
searchUser -com.sr.ghostencryptedchat.ui.ContactsFragment  setupSearch -com.sr.ghostencryptedchat.ui.ContactsFragment  showDeleteContactDialog -com.sr.ghostencryptedchat.ui.ContactsFragment  sorted -com.sr.ghostencryptedchat.ui.ContactsFragment  
startActivity -com.sr.ghostencryptedchat.ui.ContactsFragment  to -com.sr.ghostencryptedchat.ui.ContactsFragment  
toMutableList -com.sr.ghostencryptedchat.ui.ContactsFragment  toString -com.sr.ghostencryptedchat.ui.ContactsFragment  trim -com.sr.ghostencryptedchat.ui.ContactsFragment  getFETCHContactsAndIncomingDMs Lcom.sr.ghostencryptedchat.ui.ContactsFragment.setupSearch.<no name provided>  getFetchContactsAndIncomingDMs Lcom.sr.ghostencryptedchat.ui.ContactsFragment.setupSearch.<no name provided>  
getISNotEmpty Lcom.sr.ghostencryptedchat.ui.ContactsFragment.setupSearch.<no name provided>  
getIsNotEmpty Lcom.sr.ghostencryptedchat.ui.ContactsFragment.setupSearch.<no name provided>  
getSEARCHUser Lcom.sr.ghostencryptedchat.ui.ContactsFragment.setupSearch.<no name provided>  
getSearchUser Lcom.sr.ghostencryptedchat.ui.ContactsFragment.setupSearch.<no name provided>  getTOString Lcom.sr.ghostencryptedchat.ui.ContactsFragment.setupSearch.<no name provided>  getTRIM Lcom.sr.ghostencryptedchat.ui.ContactsFragment.setupSearch.<no name provided>  getToString Lcom.sr.ghostencryptedchat.ui.ContactsFragment.setupSearch.<no name provided>  getTrim Lcom.sr.ghostencryptedchat.ui.ContactsFragment.setupSearch.<no name provided>  
isNotEmpty Lcom.sr.ghostencryptedchat.ui.ContactsFragment.setupSearch.<no name provided>  Bundle /com.sr.ghostencryptedchat.ui.GlobalChatFragment  ChatMessage /com.sr.ghostencryptedchat.ui.GlobalChatFragment  EncryptionUtil /com.sr.ghostencryptedchat.ui.GlobalChatFragment  FirebaseFirestore /com.sr.ghostencryptedchat.ui.GlobalChatFragment  FragmentGlobalChatBinding /com.sr.ghostencryptedchat.ui.GlobalChatFragment  KeyStoreUtil /com.sr.ghostencryptedchat.ui.GlobalChatFragment  LayoutInflater /com.sr.ghostencryptedchat.ui.GlobalChatFragment  LinearLayoutManager /com.sr.ghostencryptedchat.ui.GlobalChatFragment  ListenerRegistration /com.sr.ghostencryptedchat.ui.GlobalChatFragment  PublicChatAdapter /com.sr.ghostencryptedchat.ui.GlobalChatFragment  String /com.sr.ghostencryptedchat.ui.GlobalChatFragment  System /com.sr.ghostencryptedchat.ui.GlobalChatFragment  View /com.sr.ghostencryptedchat.ui.GlobalChatFragment  	ViewGroup /com.sr.ghostencryptedchat.ui.GlobalChatFragment  _binding /com.sr.ghostencryptedchat.ui.GlobalChatFragment  adapter /com.sr.ghostencryptedchat.ui.GlobalChatFragment  binding /com.sr.ghostencryptedchat.ui.GlobalChatFragment  db /com.sr.ghostencryptedchat.ui.GlobalChatFragment  
getISNotBlank /com.sr.ghostencryptedchat.ui.GlobalChatFragment  
getIsNotBlank /com.sr.ghostencryptedchat.ui.GlobalChatFragment  
getMAPNotNull /com.sr.ghostencryptedchat.ui.GlobalChatFragment  getMUTABLEListOf /com.sr.ghostencryptedchat.ui.GlobalChatFragment  
getMapNotNull /com.sr.ghostencryptedchat.ui.GlobalChatFragment  getMutableListOf /com.sr.ghostencryptedchat.ui.GlobalChatFragment  
isNotBlank /com.sr.ghostencryptedchat.ui.GlobalChatFragment  java /com.sr.ghostencryptedchat.ui.GlobalChatFragment  listener /com.sr.ghostencryptedchat.ui.GlobalChatFragment  
mapNotNull /com.sr.ghostencryptedchat.ui.GlobalChatFragment  messages /com.sr.ghostencryptedchat.ui.GlobalChatFragment  
mutableListOf /com.sr.ghostencryptedchat.ui.GlobalChatFragment  requireContext /com.sr.ghostencryptedchat.ui.GlobalChatFragment  username /com.sr.ghostencryptedchat.ui.GlobalChatFragment  AlertDialog -com.sr.ghostencryptedchat.ui.SettingsFragment  
BackupUtil -com.sr.ghostencryptedchat.ui.SettingsFragment  Bundle -com.sr.ghostencryptedchat.ui.SettingsFragment  	Exception -com.sr.ghostencryptedchat.ui.SettingsFragment  KeyStoreUtil -com.sr.ghostencryptedchat.ui.SettingsFragment  LayoutInflater -com.sr.ghostencryptedchat.ui.SettingsFragment  R -com.sr.ghostencryptedchat.ui.SettingsFragment  String -com.sr.ghostencryptedchat.ui.SettingsFragment  Toast -com.sr.ghostencryptedchat.ui.SettingsFragment  View -com.sr.ghostencryptedchat.ui.SettingsFragment  	ViewGroup -com.sr.ghostencryptedchat.ui.SettingsFragment  android -com.sr.ghostencryptedchat.ui.SettingsFragment  context -com.sr.ghostencryptedchat.ui.SettingsFragment  equals -com.sr.ghostencryptedchat.ui.SettingsFragment  
getANDROID -com.sr.ghostencryptedchat.ui.SettingsFragment  
getAndroid -com.sr.ghostencryptedchat.ui.SettingsFragment  
getCONTEXT -com.sr.ghostencryptedchat.ui.SettingsFragment  
getContext -com.sr.ghostencryptedchat.ui.SettingsFragment  
getISNotEmpty -com.sr.ghostencryptedchat.ui.SettingsFragment  
getIsNotEmpty -com.sr.ghostencryptedchat.ui.SettingsFragment  	getLAUNCH -com.sr.ghostencryptedchat.ui.SettingsFragment  getLIFECYCLEScope -com.sr.ghostencryptedchat.ui.SettingsFragment  	getLaunch -com.sr.ghostencryptedchat.ui.SettingsFragment  getLifecycleScope -com.sr.ghostencryptedchat.ui.SettingsFragment  getTRIM -com.sr.ghostencryptedchat.ui.SettingsFragment  getTrim -com.sr.ghostencryptedchat.ui.SettingsFragment  
isNotEmpty -com.sr.ghostencryptedchat.ui.SettingsFragment  launch -com.sr.ghostencryptedchat.ui.SettingsFragment  lifecycleScope -com.sr.ghostencryptedchat.ui.SettingsFragment  requireContext -com.sr.ghostencryptedchat.ui.SettingsFragment  requireView -com.sr.ghostencryptedchat.ui.SettingsFragment  
setContext -com.sr.ghostencryptedchat.ui.SettingsFragment  showRestoreDialog -com.sr.ghostencryptedchat.ui.SettingsFragment  showSeedPhraseDialog -com.sr.ghostencryptedchat.ui.SettingsFragment  trim -com.sr.ghostencryptedchat.ui.SettingsFragment  updateDisplayedUsername -com.sr.ghostencryptedchat.ui.SettingsFragment  Boolean +com.sr.ghostencryptedchat.ui.WalletFragment  Bundle +com.sr.ghostencryptedchat.ui.WalletFragment  FragmentWalletBinding +com.sr.ghostencryptedchat.ui.WalletFragment  Int +com.sr.ghostencryptedchat.ui.WalletFragment  LayoutInflater +com.sr.ghostencryptedchat.ui.WalletFragment  ProgressBar +com.sr.ghostencryptedchat.ui.WalletFragment  String +com.sr.ghostencryptedchat.ui.WalletFragment  SuppressLint +com.sr.ghostencryptedchat.ui.WalletFragment  View +com.sr.ghostencryptedchat.ui.WalletFragment  	ViewGroup +com.sr.ghostencryptedchat.ui.WalletFragment  WebChromeClient +com.sr.ghostencryptedchat.ui.WalletFragment  WebSettings +com.sr.ghostencryptedchat.ui.WalletFragment  WebView +com.sr.ghostencryptedchat.ui.WalletFragment  
WebViewClient +com.sr.ghostencryptedchat.ui.WalletFragment  _binding +com.sr.ghostencryptedchat.ui.WalletFragment  binding +com.sr.ghostencryptedchat.ui.WalletFragment  	canGoBack +com.sr.ghostencryptedchat.ui.WalletFragment  contains +com.sr.ghostencryptedchat.ui.WalletFragment  equals +com.sr.ghostencryptedchat.ui.WalletFragment  getCONTAINS +com.sr.ghostencryptedchat.ui.WalletFragment  getContains +com.sr.ghostencryptedchat.ui.WalletFragment  goBack +com.sr.ghostencryptedchat.ui.WalletFragment  
isInitialized +com.sr.ghostencryptedchat.ui.WalletFragment  isWebViewInitialized +com.sr.ghostencryptedchat.ui.WalletFragment  
loadWallet +com.sr.ghostencryptedchat.ui.WalletFragment  progressBar +com.sr.ghostencryptedchat.ui.WalletFragment  setupWebView +com.sr.ghostencryptedchat.ui.WalletFragment  webView +com.sr.ghostencryptedchat.ui.WalletFragment  getCONTAINS Kcom.sr.ghostencryptedchat.ui.WalletFragment.setupWebView.<no name provided>  getContains Kcom.sr.ghostencryptedchat.ui.WalletFragment.setupWebView.<no name provided>  getPROGRESSBar Kcom.sr.ghostencryptedchat.ui.WalletFragment.setupWebView.<no name provided>  getProgressBar Kcom.sr.ghostencryptedchat.ui.WalletFragment.setupWebView.<no name provided>  AlertDialog com.sr.ghostencryptedchat.util  
BackupUtil com.sr.ghostencryptedchat.util  Base64 com.sr.ghostencryptedchat.util  Boolean com.sr.ghostencryptedchat.util  Build com.sr.ghostencryptedchat.util  Cipher com.sr.ghostencryptedchat.util  Color com.sr.ghostencryptedchat.util  Context com.sr.ghostencryptedchat.util  Dispatchers com.sr.ghostencryptedchat.util  DownloadManager com.sr.ghostencryptedchat.util  EditText com.sr.ghostencryptedchat.util  EncryptionUtil com.sr.ghostencryptedchat.util  Environment com.sr.ghostencryptedchat.util  	Exception com.sr.ghostencryptedchat.util  File com.sr.ghostencryptedchat.util  FileProvider com.sr.ghostencryptedchat.util  FirebaseFirestore com.sr.ghostencryptedchat.util  FrameLayout com.sr.ghostencryptedchat.util  Gravity com.sr.ghostencryptedchat.util  Gson com.sr.ghostencryptedchat.util  Int com.sr.ghostencryptedchat.util  Intent com.sr.ghostencryptedchat.util  IntentFilter com.sr.ghostencryptedchat.util  KeyGenParameterSpec com.sr.ghostencryptedchat.util  KeyPair com.sr.ghostencryptedchat.util  KeyPairGenerator com.sr.ghostencryptedchat.util  
KeyProperties com.sr.ghostencryptedchat.util  KeyStore com.sr.ghostencryptedchat.util  KeyStoreUtil com.sr.ghostencryptedchat.util  Log com.sr.ghostencryptedchat.util  Long com.sr.ghostencryptedchat.util  
MessageDigest com.sr.ghostencryptedchat.util  PackageManager com.sr.ghostencryptedchat.util  PreferenceManager com.sr.ghostencryptedchat.util  Random com.sr.ghostencryptedchat.util  
SecretKeySpec com.sr.ghostencryptedchat.util  String com.sr.ghostencryptedchat.util  Suppress com.sr.ghostencryptedchat.util  System com.sr.ghostencryptedchat.util  TAG com.sr.ghostencryptedchat.util  TextView com.sr.ghostencryptedchat.util  Toast com.sr.ghostencryptedchat.util  
UPDATE_URL com.sr.ghostencryptedchat.util  URL com.sr.ghostencryptedchat.util  UUID com.sr.ghostencryptedchat.util  Unit com.sr.ghostencryptedchat.util  
UpdateInfo com.sr.ghostencryptedchat.util  
UpdateManager com.sr.ghostencryptedchat.util  Uri com.sr.ghostencryptedchat.util  	ViewGroup com.sr.ghostencryptedchat.util  android com.sr.ghostencryptedchat.util  apply com.sr.ghostencryptedchat.util  await com.sr.ghostencryptedchat.util  bufferedReader com.sr.ghostencryptedchat.util  
component1 com.sr.ghostencryptedchat.util  
component2 com.sr.ghostencryptedchat.util  
component3 com.sr.ghostencryptedchat.util  
downloadId com.sr.ghostencryptedchat.util  getValue com.sr.ghostencryptedchat.util  	hashMapOf com.sr.ghostencryptedchat.util  
installApk com.sr.ghostencryptedchat.util  invoke com.sr.ghostencryptedchat.util  
isNotBlank com.sr.ghostencryptedchat.util  java com.sr.ghostencryptedchat.util  joinToString com.sr.ghostencryptedchat.util  lazy com.sr.ghostencryptedchat.util  let com.sr.ghostencryptedchat.util  listOf com.sr.ghostencryptedchat.util  
mutableListOf com.sr.ghostencryptedchat.util  provideDelegate com.sr.ghostencryptedchat.util  readText com.sr.ghostencryptedchat.util  removeUpdateOverlay com.sr.ghostencryptedchat.util  split com.sr.ghostencryptedchat.util  	substring com.sr.ghostencryptedchat.util  to com.sr.ghostencryptedchat.util  toByteArray com.sr.ghostencryptedchat.util  trim com.sr.ghostencryptedchat.util  updateOverlayMessage com.sr.ghostencryptedchat.util  use com.sr.ghostencryptedchat.util  withContext com.sr.ghostencryptedchat.util  BACKUP_COLLECTION )com.sr.ghostencryptedchat.util.BackupUtil  Base64 )com.sr.ghostencryptedchat.util.BackupUtil  Boolean )com.sr.ghostencryptedchat.util.BackupUtil  Cipher )com.sr.ghostencryptedchat.util.BackupUtil  Context )com.sr.ghostencryptedchat.util.BackupUtil  	Exception )com.sr.ghostencryptedchat.util.BackupUtil  FirebaseFirestore )com.sr.ghostencryptedchat.util.BackupUtil  
MessageDigest )com.sr.ghostencryptedchat.util.BackupUtil  PreferenceManager )com.sr.ghostencryptedchat.util.BackupUtil  Random )com.sr.ghostencryptedchat.util.BackupUtil  SEED_PHRASE_KEY )com.sr.ghostencryptedchat.util.BackupUtil  	SecretKey )com.sr.ghostencryptedchat.util.BackupUtil  
SecretKeySpec )com.sr.ghostencryptedchat.util.BackupUtil  String )com.sr.ghostencryptedchat.util.BackupUtil  System )com.sr.ghostencryptedchat.util.BackupUtil  WORD_LIST_SIZE )com.sr.ghostencryptedchat.util.BackupUtil  await )com.sr.ghostencryptedchat.util.BackupUtil  
component1 )com.sr.ghostencryptedchat.util.BackupUtil  
component2 )com.sr.ghostencryptedchat.util.BackupUtil  
component3 )com.sr.ghostencryptedchat.util.BackupUtil  createBackup )com.sr.ghostencryptedchat.util.BackupUtil  db )com.sr.ghostencryptedchat.util.BackupUtil  decrypt )com.sr.ghostencryptedchat.util.BackupUtil  encrypt )com.sr.ghostencryptedchat.util.BackupUtil  generateSeedPhrase )com.sr.ghostencryptedchat.util.BackupUtil  getAWAIT )com.sr.ghostencryptedchat.util.BackupUtil  getAwait )com.sr.ghostencryptedchat.util.BackupUtil  
getComponent1 )com.sr.ghostencryptedchat.util.BackupUtil  
getComponent2 )com.sr.ghostencryptedchat.util.BackupUtil  
getComponent3 )com.sr.ghostencryptedchat.util.BackupUtil  getDocumentIdFromSeedPhrase )com.sr.ghostencryptedchat.util.BackupUtil  getHASHMapOf )com.sr.ghostencryptedchat.util.BackupUtil  getHashMapOf )com.sr.ghostencryptedchat.util.BackupUtil  getJOINToString )com.sr.ghostencryptedchat.util.BackupUtil  getJoinToString )com.sr.ghostencryptedchat.util.BackupUtil  getKeyFromSeedPhrase )com.sr.ghostencryptedchat.util.BackupUtil  getLET )com.sr.ghostencryptedchat.util.BackupUtil  	getLISTOf )com.sr.ghostencryptedchat.util.BackupUtil  getLet )com.sr.ghostencryptedchat.util.BackupUtil  	getListOf )com.sr.ghostencryptedchat.util.BackupUtil  getMUTABLEListOf )com.sr.ghostencryptedchat.util.BackupUtil  getMutableListOf )com.sr.ghostencryptedchat.util.BackupUtil  getSPLIT )com.sr.ghostencryptedchat.util.BackupUtil  getSplit )com.sr.ghostencryptedchat.util.BackupUtil  getTO )com.sr.ghostencryptedchat.util.BackupUtil  getTOByteArray )com.sr.ghostencryptedchat.util.BackupUtil  getTo )com.sr.ghostencryptedchat.util.BackupUtil  getToByteArray )com.sr.ghostencryptedchat.util.BackupUtil  	hashMapOf )com.sr.ghostencryptedchat.util.BackupUtil  invoke )com.sr.ghostencryptedchat.util.BackupUtil  joinToString )com.sr.ghostencryptedchat.util.BackupUtil  let )com.sr.ghostencryptedchat.util.BackupUtil  listOf )com.sr.ghostencryptedchat.util.BackupUtil  
mutableListOf )com.sr.ghostencryptedchat.util.BackupUtil  restoreFromSeedPhrase )com.sr.ghostencryptedchat.util.BackupUtil  split )com.sr.ghostencryptedchat.util.BackupUtil  to )com.sr.ghostencryptedchat.util.BackupUtil  toByteArray )com.sr.ghostencryptedchat.util.BackupUtil  wordList )com.sr.ghostencryptedchat.util.BackupUtil  Base64 -com.sr.ghostencryptedchat.util.EncryptionUtil  Cipher -com.sr.ghostencryptedchat.util.EncryptionUtil  	Exception -com.sr.ghostencryptedchat.util.EncryptionUtil  
SecretKeySpec -com.sr.ghostencryptedchat.util.EncryptionUtil  String -com.sr.ghostencryptedchat.util.EncryptionUtil  decrypt -com.sr.ghostencryptedchat.util.EncryptionUtil  encrypt -com.sr.ghostencryptedchat.util.EncryptionUtil  getTOByteArray -com.sr.ghostencryptedchat.util.EncryptionUtil  getToByteArray -com.sr.ghostencryptedchat.util.EncryptionUtil  invoke -com.sr.ghostencryptedchat.util.EncryptionUtil  key -com.sr.ghostencryptedchat.util.EncryptionUtil  toByteArray -com.sr.ghostencryptedchat.util.EncryptionUtil  AlertDialog +com.sr.ghostencryptedchat.util.KeyStoreUtil  Context +com.sr.ghostencryptedchat.util.KeyStoreUtil  EditText +com.sr.ghostencryptedchat.util.KeyStoreUtil  InputMethodManager +com.sr.ghostencryptedchat.util.KeyStoreUtil  KEYSTORE_PROVIDER +com.sr.ghostencryptedchat.util.KeyStoreUtil  	KEY_ALIAS +com.sr.ghostencryptedchat.util.KeyStoreUtil  KeyGenParameterSpec +com.sr.ghostencryptedchat.util.KeyStoreUtil  KeyPair +com.sr.ghostencryptedchat.util.KeyStoreUtil  KeyPairGenerator +com.sr.ghostencryptedchat.util.KeyStoreUtil  
KeyProperties +com.sr.ghostencryptedchat.util.KeyStoreUtil  KeyStore +com.sr.ghostencryptedchat.util.KeyStoreUtil  PreferenceManager +com.sr.ghostencryptedchat.util.KeyStoreUtil  
PrivateKey +com.sr.ghostencryptedchat.util.KeyStoreUtil  	PublicKey +com.sr.ghostencryptedchat.util.KeyStoreUtil  String +com.sr.ghostencryptedchat.util.KeyStoreUtil  USERNAME_KEY +com.sr.ghostencryptedchat.util.KeyStoreUtil  UUID +com.sr.ghostencryptedchat.util.KeyStoreUtil  Unit +com.sr.ghostencryptedchat.util.KeyStoreUtil  
getISNotBlank +com.sr.ghostencryptedchat.util.KeyStoreUtil  
getIsNotBlank +com.sr.ghostencryptedchat.util.KeyStoreUtil  getOrCreateKeyPair +com.sr.ghostencryptedchat.util.KeyStoreUtil  getOrCreateUsername +com.sr.ghostencryptedchat.util.KeyStoreUtil  getSUBSTRING +com.sr.ghostencryptedchat.util.KeyStoreUtil  getSubstring +com.sr.ghostencryptedchat.util.KeyStoreUtil  getTRIM +com.sr.ghostencryptedchat.util.KeyStoreUtil  getTrim +com.sr.ghostencryptedchat.util.KeyStoreUtil  
isNotBlank +com.sr.ghostencryptedchat.util.KeyStoreUtil  setUsername +com.sr.ghostencryptedchat.util.KeyStoreUtil  	substring +com.sr.ghostencryptedchat.util.KeyStoreUtil  trim +com.sr.ghostencryptedchat.util.KeyStoreUtil  Activity ,com.sr.ghostencryptedchat.util.UpdateManager  BroadcastReceiver ,com.sr.ghostencryptedchat.util.UpdateManager  Build ,com.sr.ghostencryptedchat.util.UpdateManager  Color ,com.sr.ghostencryptedchat.util.UpdateManager  Context ,com.sr.ghostencryptedchat.util.UpdateManager  Dispatchers ,com.sr.ghostencryptedchat.util.UpdateManager  DownloadManager ,com.sr.ghostencryptedchat.util.UpdateManager  Environment ,com.sr.ghostencryptedchat.util.UpdateManager  	Exception ,com.sr.ghostencryptedchat.util.UpdateManager  File ,com.sr.ghostencryptedchat.util.UpdateManager  FileProvider ,com.sr.ghostencryptedchat.util.UpdateManager  FrameLayout ,com.sr.ghostencryptedchat.util.UpdateManager  Gravity ,com.sr.ghostencryptedchat.util.UpdateManager  Gson ,com.sr.ghostencryptedchat.util.UpdateManager  HttpURLConnection ,com.sr.ghostencryptedchat.util.UpdateManager  Int ,com.sr.ghostencryptedchat.util.UpdateManager  Intent ,com.sr.ghostencryptedchat.util.UpdateManager  IntentFilter ,com.sr.ghostencryptedchat.util.UpdateManager  Log ,com.sr.ghostencryptedchat.util.UpdateManager  Long ,com.sr.ghostencryptedchat.util.UpdateManager  PackageManager ,com.sr.ghostencryptedchat.util.UpdateManager  String ,com.sr.ghostencryptedchat.util.UpdateManager  Suppress ,com.sr.ghostencryptedchat.util.UpdateManager  TAG ,com.sr.ghostencryptedchat.util.UpdateManager  TextView ,com.sr.ghostencryptedchat.util.UpdateManager  Toast ,com.sr.ghostencryptedchat.util.UpdateManager  
UPDATE_URL ,com.sr.ghostencryptedchat.util.UpdateManager  URL ,com.sr.ghostencryptedchat.util.UpdateManager  
UpdateInfo ,com.sr.ghostencryptedchat.util.UpdateManager  Uri ,com.sr.ghostencryptedchat.util.UpdateManager  	ViewGroup ,com.sr.ghostencryptedchat.util.UpdateManager  android ,com.sr.ghostencryptedchat.util.UpdateManager  apply ,com.sr.ghostencryptedchat.util.UpdateManager  bufferedReader ,com.sr.ghostencryptedchat.util.UpdateManager  checkForUpdate ,com.sr.ghostencryptedchat.util.UpdateManager  context ,com.sr.ghostencryptedchat.util.UpdateManager  currentVersionCode ,com.sr.ghostencryptedchat.util.UpdateManager  downloadAndInstallUpdate ,com.sr.ghostencryptedchat.util.UpdateManager  
downloadId ,com.sr.ghostencryptedchat.util.UpdateManager  fetchUpdateInfo ,com.sr.ghostencryptedchat.util.UpdateManager  
getANDROID ,com.sr.ghostencryptedchat.util.UpdateManager  getAPPLY ,com.sr.ghostencryptedchat.util.UpdateManager  
getAndroid ,com.sr.ghostencryptedchat.util.UpdateManager  getApply ,com.sr.ghostencryptedchat.util.UpdateManager  getBUFFEREDReader ,com.sr.ghostencryptedchat.util.UpdateManager  getBufferedReader ,com.sr.ghostencryptedchat.util.UpdateManager  getGETValue ,com.sr.ghostencryptedchat.util.UpdateManager  getGetValue ,com.sr.ghostencryptedchat.util.UpdateManager  getLAZY ,com.sr.ghostencryptedchat.util.UpdateManager  getLazy ,com.sr.ghostencryptedchat.util.UpdateManager  getPROVIDEDelegate ,com.sr.ghostencryptedchat.util.UpdateManager  getProvideDelegate ,com.sr.ghostencryptedchat.util.UpdateManager  getREADText ,com.sr.ghostencryptedchat.util.UpdateManager  getReadText ,com.sr.ghostencryptedchat.util.UpdateManager  getUSE ,com.sr.ghostencryptedchat.util.UpdateManager  getUse ,com.sr.ghostencryptedchat.util.UpdateManager  getValue ,com.sr.ghostencryptedchat.util.UpdateManager  getWITHContext ,com.sr.ghostencryptedchat.util.UpdateManager  getWithContext ,com.sr.ghostencryptedchat.util.UpdateManager  
installApk ,com.sr.ghostencryptedchat.util.UpdateManager  java ,com.sr.ghostencryptedchat.util.UpdateManager  lazy ,com.sr.ghostencryptedchat.util.UpdateManager  provideDelegate ,com.sr.ghostencryptedchat.util.UpdateManager  readText ,com.sr.ghostencryptedchat.util.UpdateManager  removeUpdateOverlay ,com.sr.ghostencryptedchat.util.UpdateManager  showUpdateOverlay ,com.sr.ghostencryptedchat.util.UpdateManager  
updateOverlay ,com.sr.ghostencryptedchat.util.UpdateManager  updateOverlayMessage ,com.sr.ghostencryptedchat.util.UpdateManager  use ,com.sr.ghostencryptedchat.util.UpdateManager  withContext ,com.sr.ghostencryptedchat.util.UpdateManager  
getDOWNLOADId Xcom.sr.ghostencryptedchat.util.UpdateManager.downloadAndInstallUpdate.<no name provided>  
getDownloadId Xcom.sr.ghostencryptedchat.util.UpdateManager.downloadAndInstallUpdate.<no name provided>  
getINSTALLApk Xcom.sr.ghostencryptedchat.util.UpdateManager.downloadAndInstallUpdate.<no name provided>  
getInstallApk Xcom.sr.ghostencryptedchat.util.UpdateManager.downloadAndInstallUpdate.<no name provided>  getREMOVEUpdateOverlay Xcom.sr.ghostencryptedchat.util.UpdateManager.downloadAndInstallUpdate.<no name provided>  getRemoveUpdateOverlay Xcom.sr.ghostencryptedchat.util.UpdateManager.downloadAndInstallUpdate.<no name provided>  getUPDATEOverlayMessage Xcom.sr.ghostencryptedchat.util.UpdateManager.downloadAndInstallUpdate.<no name provided>  getUpdateOverlayMessage Xcom.sr.ghostencryptedchat.util.UpdateManager.downloadAndInstallUpdate.<no name provided>  Char com.sr.ghostencryptedchat.view  Color com.sr.ghostencryptedchat.view  Float com.sr.ghostencryptedchat.view  
FloatArray com.sr.ghostencryptedchat.view  Int com.sr.ghostencryptedchat.view  JvmOverloads com.sr.ghostencryptedchat.view  LAYER_TYPE_HARDWARE com.sr.ghostencryptedchat.view  LinearGradient com.sr.ghostencryptedchat.view  Log com.sr.ghostencryptedchat.view  Math com.sr.ghostencryptedchat.view  MatrixBackgroundView com.sr.ghostencryptedchat.view  MutableList com.sr.ghostencryptedchat.view  Paint com.sr.ghostencryptedchat.view  Pair com.sr.ghostencryptedchat.view  Path com.sr.ghostencryptedchat.view  RadialGradient com.sr.ghostencryptedchat.view  Random com.sr.ghostencryptedchat.view  Shader com.sr.ghostencryptedchat.view  System com.sr.ghostencryptedchat.view  ThreeDBackgroundView com.sr.ghostencryptedchat.view  Typeface com.sr.ghostencryptedchat.view  apply com.sr.ghostencryptedchat.view  
coerceAtLeast com.sr.ghostencryptedchat.view  cos com.sr.ghostencryptedchat.view  floatArrayOf com.sr.ghostencryptedchat.view  
intArrayOf com.sr.ghostencryptedchat.view  
isNotEmpty com.sr.ghostencryptedchat.view  min com.sr.ghostencryptedchat.view  minusAssign com.sr.ghostencryptedchat.view  
mutableListOf com.sr.ghostencryptedchat.view  
plusAssign com.sr.ghostencryptedchat.view  random com.sr.ghostencryptedchat.view  sin com.sr.ghostencryptedchat.view  until com.sr.ghostencryptedchat.view  AttributeSet 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  Canvas 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  Char 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  Color 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  Column 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  Context 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  Float 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  Int 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  JvmOverloads 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  Log 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  MutableList 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  Paint 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  Random 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  System 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  Typeface 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  animationSpeed 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  apply 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  chars 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  
coerceAtLeast 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  columnCount 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  columns 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  getAPPLY 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  getApply 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  getCOERCEAtLeast 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  getCoerceAtLeast 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  	getHEIGHT 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  	getHeight 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  
getISNotEmpty 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  
getIsNotEmpty 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  getMIN 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  getMUTABLEListOf 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  getMin 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  getMutableListOf 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  
getPLUSAssign 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  
getPlusAssign 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  getUNTIL 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  getUntil 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  height 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  
invalidate 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  
isNotEmpty 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  lastUpdateTime 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  min 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  
mutableListOf 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  paint 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  
plusAssign 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  random 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  setBackgroundColor 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  	setHeight 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  until 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  
updateColumns 3com.sr.ghostencryptedchat.view.MatrixBackgroundView  Char :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  Float :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  Int :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  MutableList :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  chars :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  	getRANDOM :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  	getRandom :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  getUNTIL :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  getUntil :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  length :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  
mutableListOf :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  random :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  speed :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  until :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  x :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  y :com.sr.ghostencryptedchat.view.MatrixBackgroundView.Column  AttributeSet 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  Canvas 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  Color 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  Context 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  Float 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
FloatArray 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  Int 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  JvmOverloads 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  LAYER_TYPE_HARDWARE 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  LinearGradient 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  Math 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  Paint 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  Pair 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  Path 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  Polygon 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  RadialGradient 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  Random 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  Shader 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  System 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  accentColor 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  animationSpeed 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
animationTime 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  apply 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  cos 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  	cosValues 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  	darkColor 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  drawPolygon 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  floatArrayOf 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getAPPLY 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getApply 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getCOS 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getCos 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getFLOATArrayOf 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getFloatArrayOf 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  	getHEIGHT 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  	getHeight 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
getINTArrayOf 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
getIntArrayOf 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getMINUSAssign 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getMUTABLEListOf 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getMinusAssign 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getMutableListOf 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
getPLUSAssign 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
getPlusAssign 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getSIN 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getSin 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getUNTIL 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getUntil 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
getVISIBILITY 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
getVisibility 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getWIDTH 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  getWidth 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  height 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
intArrayOf 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
invalidate 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
lastFrameTime 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  midColor 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  minusAssign 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
mutableListOf 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  paint 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
plusAssign 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  polygons 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  preventExcessiveOverlap 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  random 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  setBackgroundColor 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  	setHeight 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  setLayerType 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
setVisibility 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  setWidth 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  shadowColor 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  sin 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  	sinValues 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  until 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  updatePolygons 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  
visibility 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  width 3com.sr.ghostencryptedchat.view.ThreeDBackgroundView  Float ;com.sr.ghostencryptedchat.view.ThreeDBackgroundView.Polygon  Int ;com.sr.ghostencryptedchat.view.ThreeDBackgroundView.Polygon  alpha ;com.sr.ghostencryptedchat.view.ThreeDBackgroundView.Polygon  centerX ;com.sr.ghostencryptedchat.view.ThreeDBackgroundView.Polygon  centerY ;com.sr.ghostencryptedchat.view.ThreeDBackgroundView.Polygon  depth ;com.sr.ghostencryptedchat.view.ThreeDBackgroundView.Polygon  driftSpeedX ;com.sr.ghostencryptedchat.view.ThreeDBackgroundView.Polygon  driftSpeedY ;com.sr.ghostencryptedchat.view.ThreeDBackgroundView.Polygon  	originalX ;com.sr.ghostencryptedchat.view.ThreeDBackgroundView.Polygon  	originalY ;com.sr.ghostencryptedchat.view.ThreeDBackgroundView.Polygon  rotation ;com.sr.ghostencryptedchat.view.ThreeDBackgroundView.Polygon  
rotationSpeed ;com.sr.ghostencryptedchat.view.ThreeDBackgroundView.Polygon  size ;com.sr.ghostencryptedchat.view.ThreeDBackgroundView.Polygon  BufferedReader java.io  File java.io  InputStream java.io  getREADText java.io.BufferedReader  getReadText java.io.BufferedReader  getUSE java.io.BufferedReader  getUse java.io.BufferedReader  readText java.io.BufferedReader  use java.io.BufferedReader  delete java.io.File  exists java.io.File  bufferedReader java.io.InputStream  close java.io.InputStream  getBUFFEREDReader java.io.InputStream  getBufferedReader java.io.InputStream  readText java.io.Reader  use java.io.Reader  ActivityDmChatBinding 	java.lang  ActivityDmRequestBinding 	java.lang  ActivityEncryptedChatBinding 	java.lang  AlertDialog 	java.lang  
BackupUtil 	java.lang  Base64 	java.lang  Build 	java.lang  ChatListAdapter 	java.lang  ChatMessage 	java.lang  ChatViewHolder 	java.lang  
ChatsFragment 	java.lang  Cipher 	java.lang  Class 	java.lang  Color 	java.lang  ContactsFragment 	java.lang  Context 	java.lang  
ContextCompat 	java.lang  	DMAdapter 	java.lang  DMChatActivity 	java.lang  	DMRequest 	java.lang  DMRequestAdapter 	java.lang  Date 	java.lang  Dispatchers 	java.lang  DownloadManager 	java.lang  EditText 	java.lang  EncryptedChatActivity 	java.lang  EncryptionUtil 	java.lang  Environment 	java.lang  	Exception 	java.lang  File 	java.lang  FileProvider 	java.lang  FirebaseApp 	java.lang  FirebaseFirestore 	java.lang  FirebaseOptions 	java.lang  
FloatArray 	java.lang  FragmentChatsBinding 	java.lang  FragmentContactsBinding 	java.lang  FragmentGlobalChatBinding 	java.lang  FragmentWalletBinding 	java.lang  FrameLayout 	java.lang  GlobalChatFragment 	java.lang  Gravity 	java.lang  Gson 	java.lang  Handler 	java.lang  Intent 	java.lang  IntentFilter 	java.lang  ItemChatListBinding 	java.lang  ItemContactBinding 	java.lang  ItemDirectMessageBinding 	java.lang  ItemDmMessageBinding 	java.lang  ItemDmRequestBinding 	java.lang  ItemMessageBinding 	java.lang  ItemMessageMeBinding 	java.lang  ItemMessageOtherBinding 	java.lang  KeyGenParameterSpec 	java.lang  KeyPair 	java.lang  KeyPairGenerator 	java.lang  
KeyProperties 	java.lang  KeyStore 	java.lang  KeyStoreUtil 	java.lang  LAYER_TYPE_HARDWARE 	java.lang  LayoutInflater 	java.lang  LinearGradient 	java.lang  LinearLayoutManager 	java.lang  Locale 	java.lang  Log 	java.lang  Looper 	java.lang  MainActivity 	java.lang  Math 	java.lang  
MessageDigest 	java.lang  PackageManager 	java.lang  Paint 	java.lang  Pair 	java.lang  Path 	java.lang  PreferenceManager 	java.lang  PublicChatAdapter 	java.lang  R 	java.lang  RadialGradient 	java.lang  Random 	java.lang  RecyclerView 	java.lang  
SecretKeySpec 	java.lang  
SetOptions 	java.lang  SettingsFragment 	java.lang  Shader 	java.lang  SimpleContactsAdapter 	java.lang  SimpleDateFormat 	java.lang  String 	java.lang  System 	java.lang  TAG 	java.lang  TextView 	java.lang  Toast 	java.lang  Typeface 	java.lang  
UPDATE_URL 	java.lang  URL 	java.lang  UUID 	java.lang  
UpdateInfo 	java.lang  
UpdateManager 	java.lang  Uri 	java.lang  UsernameSetupActivity 	java.lang  View 	java.lang  	ViewGroup 	java.lang  Void 	java.lang  WalletFragment 	java.lang  WebSettings 	java.lang  adapter 	java.lang  android 	java.lang  androidx 	java.lang  any 	java.lang  apply 	java.lang  await 	java.lang  binding 	java.lang  bufferedReader 	java.lang  
coerceAtLeast 	java.lang  
component1 	java.lang  
component2 	java.lang  
component3 	java.lang  contains 	java.lang  context 	java.lang  cos 	java.lang  db 	java.lang  
downloadId 	java.lang  	emptyList 	java.lang  fetchContactsAndIncomingDMs 	java.lang  filterIsInstance 	java.lang  finish 	java.lang  firstOrNull 	java.lang  floatArrayOf 	java.lang  get 	java.lang  getItem 	java.lang  getValue 	java.lang  	hashMapOf 	java.lang  
installApk 	java.lang  
intArrayOf 	java.lang  invoke 	java.lang  
isInitialized 	java.lang  
isNotBlank 	java.lang  
isNotEmpty 	java.lang  
isNullOrBlank 	java.lang  java 	java.lang  joinToString 	java.lang  launch 	java.lang  lazy 	java.lang  let 	java.lang  listOf 	java.lang  	lowercase 	java.lang  map 	java.lang  
mapNotNull 	java.lang  mapOf 	java.lang  min 	java.lang  minusAssign 	java.lang  
mutableListOf 	java.lang  mutableMapOf 	java.lang  onClick 	java.lang  onItemClick 	java.lang  
plusAssign 	java.lang  progressBar 	java.lang  provideDelegate 	java.lang  random 	java.lang  readText 	java.lang  removeUpdateOverlay 	java.lang  requireContext 	java.lang  requireView 	java.lang  
searchUser 	java.lang  searchUserByUsername 	java.lang  set 	java.lang  showSeedPhraseDialog 	java.lang  sin 	java.lang  sorted 	java.lang  split 	java.lang  
startActivity 	java.lang  startMainActivity 	java.lang  	substring 	java.lang  take 	java.lang  to 	java.lang  toByteArray 	java.lang  
toMutableList 	java.lang  toString 	java.lang  trim 	java.lang  until 	java.lang  updateDisplayedUsername 	java.lang  updateOverlayMessage 	java.lang  use 	java.lang  users 	java.lang  withContext 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  sqrt java.lang.Math  	toRadians java.lang.Math  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  HttpURLConnection java.net  URL java.net  
URLConnection java.net  connectTimeout java.net.HttpURLConnection  getCONNECTTimeout java.net.HttpURLConnection  getConnectTimeout java.net.HttpURLConnection  getINPUTStream java.net.HttpURLConnection  getInputStream java.net.HttpURLConnection  getREADTimeout java.net.HttpURLConnection  getReadTimeout java.net.HttpURLConnection  inputStream java.net.HttpURLConnection  readTimeout java.net.HttpURLConnection  setConnectTimeout java.net.HttpURLConnection  setInputStream java.net.HttpURLConnection  setReadTimeout java.net.HttpURLConnection  openConnection java.net.URL  Key 
java.security  
KeyFactory 
java.security  KeyPair 
java.security  KeyPairGenerator 
java.security  KeyStore 
java.security  
MessageDigest 
java.security  
PrivateKey 
java.security  	PublicKey 
java.security  
getPRIVATE java.security.KeyPair  	getPUBLIC java.security.KeyPair  
getPrivate java.security.KeyPair  	getPublic java.security.KeyPair  private java.security.KeyPair  public java.security.KeyPair  
setPrivate java.security.KeyPair  	setPublic java.security.KeyPair  generateKeyPair java.security.KeyPairGenerator  getInstance java.security.KeyPairGenerator  
initialize java.security.KeyPairGenerator  generateKeyPair !java.security.KeyPairGeneratorSpi  
initialize !java.security.KeyPairGeneratorSpi  
containsAlias java.security.KeyStore  getCertificate java.security.KeyStore  getInstance java.security.KeyStore  getKey java.security.KeyStore  load java.security.KeyStore  digest java.security.MessageDigest  getInstance java.security.MessageDigest  digest java.security.MessageDigestSpi  getPUBLICKey java.security.cert.Certificate  getPublicKey java.security.cert.Certificate  	publicKey java.security.cert.Certificate  setPublicKey java.security.cert.Certificate  PKCS8EncodedKeySpec java.security.spec  X509EncodedKeySpec java.security.spec  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  AlertDialog 	java.util  Base64 	java.util  Cipher 	java.util  Context 	java.util  Date 	java.util  EditText 	java.util  	Exception 	java.util  FirebaseFirestore 	java.util  HashMap 	java.util  KeyGenParameterSpec 	java.util  KeyPair 	java.util  KeyPairGenerator 	java.util  
KeyProperties 	java.util  KeyStore 	java.util  Locale 	java.util  
MessageDigest 	java.util  PreferenceManager 	java.util  Random 	java.util  
SecretKeySpec 	java.util  String 	java.util  System 	java.util  UUID 	java.util  await 	java.util  
component1 	java.util  
component2 	java.util  
component3 	java.util  	hashMapOf 	java.util  invoke 	java.util  
isNotBlank 	java.util  joinToString 	java.util  let 	java.util  listOf 	java.util  
mutableListOf 	java.util  split 	java.util  	substring 	java.util  to 	java.util  toByteArray 	java.util  trim 	java.util  
getDefault java.util.Locale  	nextFloat java.util.Random  nextInt java.util.Random  
randomUUID java.util.UUID  toString java.util.UUID  Cipher javax.crypto  	SecretKey javax.crypto  DECRYPT_MODE javax.crypto.Cipher  ENCRYPT_MODE javax.crypto.Cipher  doFinal javax.crypto.Cipher  getInstance javax.crypto.Cipher  init javax.crypto.Cipher  
SecretKeySpec javax.crypto.spec  ActivityDmChatBinding kotlin  ActivityDmRequestBinding kotlin  ActivityEncryptedChatBinding kotlin  AlertDialog kotlin  Any kotlin  
BackupUtil kotlin  Base64 kotlin  Boolean kotlin  Build kotlin  	ByteArray kotlin  Char kotlin  CharSequence kotlin  ChatListAdapter kotlin  ChatMessage kotlin  ChatViewHolder kotlin  
ChatsFragment kotlin  Cipher kotlin  Color kotlin  ContactsFragment kotlin  Context kotlin  
ContextCompat kotlin  	DMAdapter kotlin  DMChatActivity kotlin  	DMRequest kotlin  DMRequestAdapter kotlin  Date kotlin  Dispatchers kotlin  Double kotlin  DownloadManager kotlin  EditText kotlin  EncryptedChatActivity kotlin  EncryptionUtil kotlin  Environment kotlin  	Exception kotlin  File kotlin  FileProvider kotlin  FirebaseApp kotlin  FirebaseFirestore kotlin  FirebaseOptions kotlin  Float kotlin  
FloatArray kotlin  FragmentChatsBinding kotlin  FragmentContactsBinding kotlin  FragmentGlobalChatBinding kotlin  FragmentWalletBinding kotlin  FrameLayout kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  GlobalChatFragment kotlin  Gravity kotlin  Gson kotlin  Handler kotlin  Int kotlin  IntArray kotlin  Intent kotlin  IntentFilter kotlin  ItemChatListBinding kotlin  ItemContactBinding kotlin  ItemDirectMessageBinding kotlin  ItemDmMessageBinding kotlin  ItemDmRequestBinding kotlin  ItemMessageBinding kotlin  ItemMessageMeBinding kotlin  ItemMessageOtherBinding kotlin  JvmOverloads kotlin  KeyGenParameterSpec kotlin  KeyPair kotlin  KeyPairGenerator kotlin  
KeyProperties kotlin  KeyStore kotlin  KeyStoreUtil kotlin  LAYER_TYPE_HARDWARE kotlin  LayoutInflater kotlin  Lazy kotlin  LinearGradient kotlin  LinearLayoutManager kotlin  Locale kotlin  Log kotlin  Long kotlin  Looper kotlin  MainActivity kotlin  Math kotlin  
MessageDigest kotlin  Nothing kotlin  Number kotlin  PackageManager kotlin  Paint kotlin  Pair kotlin  Path kotlin  PreferenceManager kotlin  PublicChatAdapter kotlin  R kotlin  RadialGradient kotlin  Random kotlin  RecyclerView kotlin  
SecretKeySpec kotlin  
SetOptions kotlin  SettingsFragment kotlin  Shader kotlin  SimpleContactsAdapter kotlin  SimpleDateFormat kotlin  String kotlin  Suppress kotlin  System kotlin  TAG kotlin  TextView kotlin  Toast kotlin  Typeface kotlin  
UPDATE_URL kotlin  URL kotlin  UUID kotlin  Unit kotlin  
UpdateInfo kotlin  
UpdateManager kotlin  Uri kotlin  UsernameSetupActivity kotlin  View kotlin  	ViewGroup kotlin  WalletFragment kotlin  WebSettings kotlin  adapter kotlin  android kotlin  androidx kotlin  any kotlin  apply kotlin  await kotlin  binding kotlin  bufferedReader kotlin  
coerceAtLeast kotlin  
component1 kotlin  
component2 kotlin  
component3 kotlin  contains kotlin  context kotlin  cos kotlin  db kotlin  
downloadId kotlin  	emptyList kotlin  fetchContactsAndIncomingDMs kotlin  filterIsInstance kotlin  finish kotlin  firstOrNull kotlin  floatArrayOf kotlin  get kotlin  getItem kotlin  getValue kotlin  	hashMapOf kotlin  
installApk kotlin  
intArrayOf kotlin  invoke kotlin  
isInitialized kotlin  
isNotBlank kotlin  
isNotEmpty kotlin  
isNullOrBlank kotlin  java kotlin  joinToString kotlin  launch kotlin  lazy kotlin  let kotlin  listOf kotlin  	lowercase kotlin  map kotlin  
mapNotNull kotlin  mapOf kotlin  min kotlin  minusAssign kotlin  
mutableListOf kotlin  mutableMapOf kotlin  onClick kotlin  onItemClick kotlin  
plusAssign kotlin  progressBar kotlin  provideDelegate kotlin  random kotlin  readText kotlin  removeUpdateOverlay kotlin  requireContext kotlin  requireView kotlin  
searchUser kotlin  searchUserByUsername kotlin  set kotlin  showSeedPhraseDialog kotlin  sin kotlin  sorted kotlin  split kotlin  
startActivity kotlin  startMainActivity kotlin  	substring kotlin  take kotlin  to kotlin  toByteArray kotlin  
toMutableList kotlin  toString kotlin  trim kotlin  until kotlin  updateDisplayedUsername kotlin  updateOverlayMessage kotlin  use kotlin  users kotlin  withContext kotlin  getLET kotlin.ByteArray  getLet kotlin.ByteArray  getTOString kotlin.CharSequence  getToString kotlin.CharSequence  getMINUSAssign kotlin.Float  getMinusAssign kotlin.Float  
getPLUSAssign kotlin.Float  
getPlusAssign kotlin.Float  getCOERCEAtLeast 
kotlin.Int  getCoerceAtLeast 
kotlin.Int  
getPLUSAssign 
kotlin.Int  
getPlusAssign 
kotlin.Int  getUNTIL 
kotlin.Int  getUntil 
kotlin.Int  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  
component1 kotlin.Pair  
component2 kotlin.Pair  first kotlin.Pair  second kotlin.Pair  getCONTAINS 
kotlin.String  getContains 
kotlin.String  
getISNotBlank 
kotlin.String  
getISNotEmpty 
kotlin.String  getISNullOrBlank 
kotlin.String  
getIsNotBlank 
kotlin.String  
getIsNotEmpty 
kotlin.String  getIsNullOrBlank 
kotlin.String  getLET 
kotlin.String  getLOWERCASE 
kotlin.String  getLet 
kotlin.String  getLowercase 
kotlin.String  getSPLIT 
kotlin.String  getSUBSTRING 
kotlin.String  getSplit 
kotlin.String  getSubstring 
kotlin.String  getTAKE 
kotlin.String  getTO 
kotlin.String  getTOByteArray 
kotlin.String  getTRIM 
kotlin.String  getTake 
kotlin.String  getTo 
kotlin.String  getToByteArray 
kotlin.String  getTrim 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrBlank 
kotlin.String  ActivityDmChatBinding kotlin.annotation  ActivityDmRequestBinding kotlin.annotation  ActivityEncryptedChatBinding kotlin.annotation  AlertDialog kotlin.annotation  
BackupUtil kotlin.annotation  Base64 kotlin.annotation  Build kotlin.annotation  ChatListAdapter kotlin.annotation  ChatMessage kotlin.annotation  ChatViewHolder kotlin.annotation  
ChatsFragment kotlin.annotation  Cipher kotlin.annotation  Color kotlin.annotation  ContactsFragment kotlin.annotation  Context kotlin.annotation  
ContextCompat kotlin.annotation  	DMAdapter kotlin.annotation  DMChatActivity kotlin.annotation  	DMRequest kotlin.annotation  DMRequestAdapter kotlin.annotation  Date kotlin.annotation  Dispatchers kotlin.annotation  DownloadManager kotlin.annotation  EditText kotlin.annotation  EncryptedChatActivity kotlin.annotation  EncryptionUtil kotlin.annotation  Environment kotlin.annotation  	Exception kotlin.annotation  File kotlin.annotation  FileProvider kotlin.annotation  FirebaseApp kotlin.annotation  FirebaseFirestore kotlin.annotation  FirebaseOptions kotlin.annotation  
FloatArray kotlin.annotation  FragmentChatsBinding kotlin.annotation  FragmentContactsBinding kotlin.annotation  FragmentGlobalChatBinding kotlin.annotation  FragmentWalletBinding kotlin.annotation  FrameLayout kotlin.annotation  GlobalChatFragment kotlin.annotation  Gravity kotlin.annotation  Gson kotlin.annotation  Handler kotlin.annotation  Intent kotlin.annotation  IntentFilter kotlin.annotation  ItemChatListBinding kotlin.annotation  ItemContactBinding kotlin.annotation  ItemDirectMessageBinding kotlin.annotation  ItemDmMessageBinding kotlin.annotation  ItemDmRequestBinding kotlin.annotation  ItemMessageBinding kotlin.annotation  ItemMessageMeBinding kotlin.annotation  ItemMessageOtherBinding kotlin.annotation  JvmOverloads kotlin.annotation  KeyGenParameterSpec kotlin.annotation  KeyPair kotlin.annotation  KeyPairGenerator kotlin.annotation  
KeyProperties kotlin.annotation  KeyStore kotlin.annotation  KeyStoreUtil kotlin.annotation  LAYER_TYPE_HARDWARE kotlin.annotation  LayoutInflater kotlin.annotation  LinearGradient kotlin.annotation  LinearLayoutManager kotlin.annotation  Locale kotlin.annotation  Log kotlin.annotation  Looper kotlin.annotation  MainActivity kotlin.annotation  Math kotlin.annotation  
MessageDigest kotlin.annotation  PackageManager kotlin.annotation  Paint kotlin.annotation  Pair kotlin.annotation  Path kotlin.annotation  PreferenceManager kotlin.annotation  PublicChatAdapter kotlin.annotation  R kotlin.annotation  RadialGradient kotlin.annotation  Random kotlin.annotation  RecyclerView kotlin.annotation  
SecretKeySpec kotlin.annotation  
SetOptions kotlin.annotation  SettingsFragment kotlin.annotation  Shader kotlin.annotation  SimpleContactsAdapter kotlin.annotation  SimpleDateFormat kotlin.annotation  String kotlin.annotation  System kotlin.annotation  TAG kotlin.annotation  TextView kotlin.annotation  Toast kotlin.annotation  Typeface kotlin.annotation  
UPDATE_URL kotlin.annotation  URL kotlin.annotation  UUID kotlin.annotation  
UpdateInfo kotlin.annotation  
UpdateManager kotlin.annotation  Uri kotlin.annotation  UsernameSetupActivity kotlin.annotation  View kotlin.annotation  	ViewGroup kotlin.annotation  WalletFragment kotlin.annotation  WebSettings kotlin.annotation  adapter kotlin.annotation  android kotlin.annotation  androidx kotlin.annotation  any kotlin.annotation  apply kotlin.annotation  await kotlin.annotation  binding kotlin.annotation  bufferedReader kotlin.annotation  
coerceAtLeast kotlin.annotation  
component1 kotlin.annotation  
component2 kotlin.annotation  
component3 kotlin.annotation  contains kotlin.annotation  context kotlin.annotation  cos kotlin.annotation  db kotlin.annotation  
downloadId kotlin.annotation  	emptyList kotlin.annotation  fetchContactsAndIncomingDMs kotlin.annotation  filterIsInstance kotlin.annotation  finish kotlin.annotation  firstOrNull kotlin.annotation  floatArrayOf kotlin.annotation  get kotlin.annotation  getItem kotlin.annotation  getValue kotlin.annotation  	hashMapOf kotlin.annotation  
installApk kotlin.annotation  
intArrayOf kotlin.annotation  invoke kotlin.annotation  
isInitialized kotlin.annotation  
isNotBlank kotlin.annotation  
isNotEmpty kotlin.annotation  
isNullOrBlank kotlin.annotation  java kotlin.annotation  joinToString kotlin.annotation  launch kotlin.annotation  lazy kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  	lowercase kotlin.annotation  map kotlin.annotation  
mapNotNull kotlin.annotation  mapOf kotlin.annotation  min kotlin.annotation  minusAssign kotlin.annotation  
mutableListOf kotlin.annotation  mutableMapOf kotlin.annotation  onClick kotlin.annotation  onItemClick kotlin.annotation  
plusAssign kotlin.annotation  progressBar kotlin.annotation  provideDelegate kotlin.annotation  random kotlin.annotation  readText kotlin.annotation  removeUpdateOverlay kotlin.annotation  requireContext kotlin.annotation  requireView kotlin.annotation  
searchUser kotlin.annotation  searchUserByUsername kotlin.annotation  set kotlin.annotation  showSeedPhraseDialog kotlin.annotation  sin kotlin.annotation  sorted kotlin.annotation  split kotlin.annotation  
startActivity kotlin.annotation  startMainActivity kotlin.annotation  	substring kotlin.annotation  take kotlin.annotation  to kotlin.annotation  toByteArray kotlin.annotation  
toMutableList kotlin.annotation  toString kotlin.annotation  trim kotlin.annotation  until kotlin.annotation  updateDisplayedUsername kotlin.annotation  updateOverlayMessage kotlin.annotation  use kotlin.annotation  users kotlin.annotation  withContext kotlin.annotation  ActivityDmChatBinding kotlin.collections  ActivityDmRequestBinding kotlin.collections  ActivityEncryptedChatBinding kotlin.collections  AlertDialog kotlin.collections  
BackupUtil kotlin.collections  Base64 kotlin.collections  Build kotlin.collections  ChatListAdapter kotlin.collections  ChatMessage kotlin.collections  ChatViewHolder kotlin.collections  
ChatsFragment kotlin.collections  Cipher kotlin.collections  Color kotlin.collections  ContactsFragment kotlin.collections  Context kotlin.collections  
ContextCompat kotlin.collections  	DMAdapter kotlin.collections  DMChatActivity kotlin.collections  	DMRequest kotlin.collections  DMRequestAdapter kotlin.collections  Date kotlin.collections  Dispatchers kotlin.collections  DownloadManager kotlin.collections  EditText kotlin.collections  EncryptedChatActivity kotlin.collections  EncryptionUtil kotlin.collections  Environment kotlin.collections  	Exception kotlin.collections  File kotlin.collections  FileProvider kotlin.collections  FirebaseApp kotlin.collections  FirebaseFirestore kotlin.collections  FirebaseOptions kotlin.collections  
FloatArray kotlin.collections  FragmentChatsBinding kotlin.collections  FragmentContactsBinding kotlin.collections  FragmentGlobalChatBinding kotlin.collections  FragmentWalletBinding kotlin.collections  FrameLayout kotlin.collections  GlobalChatFragment kotlin.collections  Gravity kotlin.collections  Gson kotlin.collections  Handler kotlin.collections  Intent kotlin.collections  IntentFilter kotlin.collections  ItemChatListBinding kotlin.collections  ItemContactBinding kotlin.collections  ItemDirectMessageBinding kotlin.collections  ItemDmMessageBinding kotlin.collections  ItemDmRequestBinding kotlin.collections  ItemMessageBinding kotlin.collections  ItemMessageMeBinding kotlin.collections  ItemMessageOtherBinding kotlin.collections  JvmOverloads kotlin.collections  KeyGenParameterSpec kotlin.collections  KeyPair kotlin.collections  KeyPairGenerator kotlin.collections  
KeyProperties kotlin.collections  KeyStore kotlin.collections  KeyStoreUtil kotlin.collections  LAYER_TYPE_HARDWARE kotlin.collections  LayoutInflater kotlin.collections  LinearGradient kotlin.collections  LinearLayoutManager kotlin.collections  List kotlin.collections  Locale kotlin.collections  Log kotlin.collections  Looper kotlin.collections  MainActivity kotlin.collections  Map kotlin.collections  Math kotlin.collections  
MessageDigest kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  PackageManager kotlin.collections  Paint kotlin.collections  Pair kotlin.collections  Path kotlin.collections  PreferenceManager kotlin.collections  PublicChatAdapter kotlin.collections  R kotlin.collections  RadialGradient kotlin.collections  Random kotlin.collections  RecyclerView kotlin.collections  
SecretKeySpec kotlin.collections  
SetOptions kotlin.collections  SettingsFragment kotlin.collections  Shader kotlin.collections  SimpleContactsAdapter kotlin.collections  SimpleDateFormat kotlin.collections  String kotlin.collections  System kotlin.collections  TAG kotlin.collections  TextView kotlin.collections  Toast kotlin.collections  Typeface kotlin.collections  
UPDATE_URL kotlin.collections  URL kotlin.collections  UUID kotlin.collections  
UpdateInfo kotlin.collections  
UpdateManager kotlin.collections  Uri kotlin.collections  UsernameSetupActivity kotlin.collections  View kotlin.collections  	ViewGroup kotlin.collections  WalletFragment kotlin.collections  WebSettings kotlin.collections  adapter kotlin.collections  android kotlin.collections  androidx kotlin.collections  any kotlin.collections  apply kotlin.collections  await kotlin.collections  binding kotlin.collections  bufferedReader kotlin.collections  
coerceAtLeast kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  
component3 kotlin.collections  contains kotlin.collections  context kotlin.collections  cos kotlin.collections  db kotlin.collections  
downloadId kotlin.collections  	emptyList kotlin.collections  fetchContactsAndIncomingDMs kotlin.collections  filterIsInstance kotlin.collections  finish kotlin.collections  firstOrNull kotlin.collections  floatArrayOf kotlin.collections  get kotlin.collections  getItem kotlin.collections  getValue kotlin.collections  	hashMapOf kotlin.collections  
installApk kotlin.collections  
intArrayOf kotlin.collections  invoke kotlin.collections  
isInitialized kotlin.collections  
isNotBlank kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrBlank kotlin.collections  java kotlin.collections  joinToString kotlin.collections  launch kotlin.collections  lazy kotlin.collections  let kotlin.collections  listOf kotlin.collections  	lowercase kotlin.collections  map kotlin.collections  
mapNotNull kotlin.collections  mapOf kotlin.collections  min kotlin.collections  minusAssign kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  onClick kotlin.collections  onItemClick kotlin.collections  
plusAssign kotlin.collections  progressBar kotlin.collections  provideDelegate kotlin.collections  random kotlin.collections  readText kotlin.collections  removeUpdateOverlay kotlin.collections  requireContext kotlin.collections  requireView kotlin.collections  
searchUser kotlin.collections  searchUserByUsername kotlin.collections  set kotlin.collections  showSeedPhraseDialog kotlin.collections  sin kotlin.collections  sorted kotlin.collections  split kotlin.collections  
startActivity kotlin.collections  startMainActivity kotlin.collections  	substring kotlin.collections  take kotlin.collections  to kotlin.collections  toByteArray kotlin.collections  
toMutableList kotlin.collections  toString kotlin.collections  trim kotlin.collections  until kotlin.collections  updateDisplayedUsername kotlin.collections  updateOverlayMessage kotlin.collections  use kotlin.collections  users kotlin.collections  withContext kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  
getComponent1 kotlin.collections.List  
getComponent2 kotlin.collections.List  
getComponent3 kotlin.collections.List  getFILTERIsInstance kotlin.collections.List  getFIRSTOrNull kotlin.collections.List  getFilterIsInstance kotlin.collections.List  getFirstOrNull kotlin.collections.List  getJOINToString kotlin.collections.List  getJoinToString kotlin.collections.List  getMAP kotlin.collections.List  getMap kotlin.collections.List  	getSORTED kotlin.collections.List  	getSorted kotlin.collections.List  getTOMutableList kotlin.collections.List  getToMutableList kotlin.collections.List  getGET kotlin.collections.Map  getGet kotlin.collections.Map  getANY kotlin.collections.MutableList  getAny kotlin.collections.MutableList  getFIRSTOrNull kotlin.collections.MutableList  getFirstOrNull kotlin.collections.MutableList  
getISNotEmpty kotlin.collections.MutableList  
getIsNotEmpty kotlin.collections.MutableList  getJOINToString kotlin.collections.MutableList  getJoinToString kotlin.collections.MutableList  
getMAPNotNull kotlin.collections.MutableList  
getMapNotNull kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  ActivityDmChatBinding kotlin.comparisons  ActivityDmRequestBinding kotlin.comparisons  ActivityEncryptedChatBinding kotlin.comparisons  AlertDialog kotlin.comparisons  
BackupUtil kotlin.comparisons  Base64 kotlin.comparisons  Build kotlin.comparisons  ChatListAdapter kotlin.comparisons  ChatMessage kotlin.comparisons  ChatViewHolder kotlin.comparisons  
ChatsFragment kotlin.comparisons  Cipher kotlin.comparisons  Color kotlin.comparisons  ContactsFragment kotlin.comparisons  Context kotlin.comparisons  
ContextCompat kotlin.comparisons  	DMAdapter kotlin.comparisons  DMChatActivity kotlin.comparisons  	DMRequest kotlin.comparisons  DMRequestAdapter kotlin.comparisons  Date kotlin.comparisons  Dispatchers kotlin.comparisons  DownloadManager kotlin.comparisons  EditText kotlin.comparisons  EncryptedChatActivity kotlin.comparisons  EncryptionUtil kotlin.comparisons  Environment kotlin.comparisons  	Exception kotlin.comparisons  File kotlin.comparisons  FileProvider kotlin.comparisons  FirebaseApp kotlin.comparisons  FirebaseFirestore kotlin.comparisons  FirebaseOptions kotlin.comparisons  
FloatArray kotlin.comparisons  FragmentChatsBinding kotlin.comparisons  FragmentContactsBinding kotlin.comparisons  FragmentGlobalChatBinding kotlin.comparisons  FragmentWalletBinding kotlin.comparisons  FrameLayout kotlin.comparisons  GlobalChatFragment kotlin.comparisons  Gravity kotlin.comparisons  Gson kotlin.comparisons  Handler kotlin.comparisons  Intent kotlin.comparisons  IntentFilter kotlin.comparisons  ItemChatListBinding kotlin.comparisons  ItemContactBinding kotlin.comparisons  ItemDirectMessageBinding kotlin.comparisons  ItemDmMessageBinding kotlin.comparisons  ItemDmRequestBinding kotlin.comparisons  ItemMessageBinding kotlin.comparisons  ItemMessageMeBinding kotlin.comparisons  ItemMessageOtherBinding kotlin.comparisons  JvmOverloads kotlin.comparisons  KeyGenParameterSpec kotlin.comparisons  KeyPair kotlin.comparisons  KeyPairGenerator kotlin.comparisons  
KeyProperties kotlin.comparisons  KeyStore kotlin.comparisons  KeyStoreUtil kotlin.comparisons  LAYER_TYPE_HARDWARE kotlin.comparisons  LayoutInflater kotlin.comparisons  LinearGradient kotlin.comparisons  LinearLayoutManager kotlin.comparisons  Locale kotlin.comparisons  Log kotlin.comparisons  Looper kotlin.comparisons  MainActivity kotlin.comparisons  Math kotlin.comparisons  
MessageDigest kotlin.comparisons  PackageManager kotlin.comparisons  Paint kotlin.comparisons  Pair kotlin.comparisons  Path kotlin.comparisons  PreferenceManager kotlin.comparisons  PublicChatAdapter kotlin.comparisons  R kotlin.comparisons  RadialGradient kotlin.comparisons  Random kotlin.comparisons  RecyclerView kotlin.comparisons  
SecretKeySpec kotlin.comparisons  
SetOptions kotlin.comparisons  SettingsFragment kotlin.comparisons  Shader kotlin.comparisons  SimpleContactsAdapter kotlin.comparisons  SimpleDateFormat kotlin.comparisons  String kotlin.comparisons  System kotlin.comparisons  TAG kotlin.comparisons  TextView kotlin.comparisons  Toast kotlin.comparisons  Typeface kotlin.comparisons  
UPDATE_URL kotlin.comparisons  URL kotlin.comparisons  UUID kotlin.comparisons  
UpdateInfo kotlin.comparisons  
UpdateManager kotlin.comparisons  Uri kotlin.comparisons  UsernameSetupActivity kotlin.comparisons  View kotlin.comparisons  	ViewGroup kotlin.comparisons  WalletFragment kotlin.comparisons  WebSettings kotlin.comparisons  adapter kotlin.comparisons  android kotlin.comparisons  androidx kotlin.comparisons  any kotlin.comparisons  apply kotlin.comparisons  await kotlin.comparisons  binding kotlin.comparisons  bufferedReader kotlin.comparisons  
coerceAtLeast kotlin.comparisons  
component1 kotlin.comparisons  
component2 kotlin.comparisons  
component3 kotlin.comparisons  contains kotlin.comparisons  context kotlin.comparisons  cos kotlin.comparisons  db kotlin.comparisons  
downloadId kotlin.comparisons  	emptyList kotlin.comparisons  fetchContactsAndIncomingDMs kotlin.comparisons  filterIsInstance kotlin.comparisons  finish kotlin.comparisons  firstOrNull kotlin.comparisons  floatArrayOf kotlin.comparisons  get kotlin.comparisons  getItem kotlin.comparisons  getValue kotlin.comparisons  	hashMapOf kotlin.comparisons  
installApk kotlin.comparisons  
intArrayOf kotlin.comparisons  invoke kotlin.comparisons  
isInitialized kotlin.comparisons  
isNotBlank kotlin.comparisons  
isNotEmpty kotlin.comparisons  
isNullOrBlank kotlin.comparisons  java kotlin.comparisons  joinToString kotlin.comparisons  launch kotlin.comparisons  lazy kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  	lowercase kotlin.comparisons  map kotlin.comparisons  
mapNotNull kotlin.comparisons  mapOf kotlin.comparisons  min kotlin.comparisons  minusAssign kotlin.comparisons  
mutableListOf kotlin.comparisons  mutableMapOf kotlin.comparisons  onClick kotlin.comparisons  onItemClick kotlin.comparisons  
plusAssign kotlin.comparisons  progressBar kotlin.comparisons  provideDelegate kotlin.comparisons  random kotlin.comparisons  readText kotlin.comparisons  removeUpdateOverlay kotlin.comparisons  requireContext kotlin.comparisons  requireView kotlin.comparisons  
searchUser kotlin.comparisons  searchUserByUsername kotlin.comparisons  set kotlin.comparisons  showSeedPhraseDialog kotlin.comparisons  sin kotlin.comparisons  sorted kotlin.comparisons  split kotlin.comparisons  
startActivity kotlin.comparisons  startMainActivity kotlin.comparisons  	substring kotlin.comparisons  take kotlin.comparisons  to kotlin.comparisons  toByteArray kotlin.comparisons  
toMutableList kotlin.comparisons  toString kotlin.comparisons  trim kotlin.comparisons  until kotlin.comparisons  updateDisplayedUsername kotlin.comparisons  updateOverlayMessage kotlin.comparisons  use kotlin.comparisons  users kotlin.comparisons  withContext kotlin.comparisons  SuspendFunction1 kotlin.coroutines  ActivityDmChatBinding 	kotlin.io  ActivityDmRequestBinding 	kotlin.io  ActivityEncryptedChatBinding 	kotlin.io  AlertDialog 	kotlin.io  
BackupUtil 	kotlin.io  Base64 	kotlin.io  Build 	kotlin.io  ChatListAdapter 	kotlin.io  ChatMessage 	kotlin.io  ChatViewHolder 	kotlin.io  
ChatsFragment 	kotlin.io  Cipher 	kotlin.io  Color 	kotlin.io  ContactsFragment 	kotlin.io  Context 	kotlin.io  
ContextCompat 	kotlin.io  	DMAdapter 	kotlin.io  DMChatActivity 	kotlin.io  	DMRequest 	kotlin.io  DMRequestAdapter 	kotlin.io  Date 	kotlin.io  Dispatchers 	kotlin.io  DownloadManager 	kotlin.io  EditText 	kotlin.io  EncryptedChatActivity 	kotlin.io  EncryptionUtil 	kotlin.io  Environment 	kotlin.io  	Exception 	kotlin.io  File 	kotlin.io  FileProvider 	kotlin.io  FirebaseApp 	kotlin.io  FirebaseFirestore 	kotlin.io  FirebaseOptions 	kotlin.io  
FloatArray 	kotlin.io  FragmentChatsBinding 	kotlin.io  FragmentContactsBinding 	kotlin.io  FragmentGlobalChatBinding 	kotlin.io  FragmentWalletBinding 	kotlin.io  FrameLayout 	kotlin.io  GlobalChatFragment 	kotlin.io  Gravity 	kotlin.io  Gson 	kotlin.io  Handler 	kotlin.io  Intent 	kotlin.io  IntentFilter 	kotlin.io  ItemChatListBinding 	kotlin.io  ItemContactBinding 	kotlin.io  ItemDirectMessageBinding 	kotlin.io  ItemDmMessageBinding 	kotlin.io  ItemDmRequestBinding 	kotlin.io  ItemMessageBinding 	kotlin.io  ItemMessageMeBinding 	kotlin.io  ItemMessageOtherBinding 	kotlin.io  JvmOverloads 	kotlin.io  KeyGenParameterSpec 	kotlin.io  KeyPair 	kotlin.io  KeyPairGenerator 	kotlin.io  
KeyProperties 	kotlin.io  KeyStore 	kotlin.io  KeyStoreUtil 	kotlin.io  LAYER_TYPE_HARDWARE 	kotlin.io  LayoutInflater 	kotlin.io  LinearGradient 	kotlin.io  LinearLayoutManager 	kotlin.io  Locale 	kotlin.io  Log 	kotlin.io  Looper 	kotlin.io  MainActivity 	kotlin.io  Math 	kotlin.io  
MessageDigest 	kotlin.io  PackageManager 	kotlin.io  Paint 	kotlin.io  Pair 	kotlin.io  Path 	kotlin.io  PreferenceManager 	kotlin.io  PublicChatAdapter 	kotlin.io  R 	kotlin.io  RadialGradient 	kotlin.io  Random 	kotlin.io  RecyclerView 	kotlin.io  
SecretKeySpec 	kotlin.io  
SetOptions 	kotlin.io  SettingsFragment 	kotlin.io  Shader 	kotlin.io  SimpleContactsAdapter 	kotlin.io  SimpleDateFormat 	kotlin.io  String 	kotlin.io  System 	kotlin.io  TAG 	kotlin.io  TextView 	kotlin.io  Toast 	kotlin.io  Typeface 	kotlin.io  
UPDATE_URL 	kotlin.io  URL 	kotlin.io  UUID 	kotlin.io  
UpdateInfo 	kotlin.io  
UpdateManager 	kotlin.io  Uri 	kotlin.io  UsernameSetupActivity 	kotlin.io  View 	kotlin.io  	ViewGroup 	kotlin.io  WalletFragment 	kotlin.io  WebSettings 	kotlin.io  adapter 	kotlin.io  android 	kotlin.io  androidx 	kotlin.io  any 	kotlin.io  apply 	kotlin.io  await 	kotlin.io  binding 	kotlin.io  bufferedReader 	kotlin.io  
coerceAtLeast 	kotlin.io  
component1 	kotlin.io  
component2 	kotlin.io  
component3 	kotlin.io  contains 	kotlin.io  context 	kotlin.io  cos 	kotlin.io  db 	kotlin.io  
downloadId 	kotlin.io  	emptyList 	kotlin.io  fetchContactsAndIncomingDMs 	kotlin.io  filterIsInstance 	kotlin.io  finish 	kotlin.io  firstOrNull 	kotlin.io  floatArrayOf 	kotlin.io  get 	kotlin.io  getItem 	kotlin.io  getValue 	kotlin.io  	hashMapOf 	kotlin.io  
installApk 	kotlin.io  
intArrayOf 	kotlin.io  invoke 	kotlin.io  
isInitialized 	kotlin.io  
isNotBlank 	kotlin.io  
isNotEmpty 	kotlin.io  
isNullOrBlank 	kotlin.io  java 	kotlin.io  joinToString 	kotlin.io  launch 	kotlin.io  lazy 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  	lowercase 	kotlin.io  map 	kotlin.io  
mapNotNull 	kotlin.io  mapOf 	kotlin.io  min 	kotlin.io  minusAssign 	kotlin.io  
mutableListOf 	kotlin.io  mutableMapOf 	kotlin.io  onClick 	kotlin.io  onItemClick 	kotlin.io  
plusAssign 	kotlin.io  progressBar 	kotlin.io  provideDelegate 	kotlin.io  random 	kotlin.io  readText 	kotlin.io  removeUpdateOverlay 	kotlin.io  requireContext 	kotlin.io  requireView 	kotlin.io  
searchUser 	kotlin.io  searchUserByUsername 	kotlin.io  set 	kotlin.io  showSeedPhraseDialog 	kotlin.io  sin 	kotlin.io  sorted 	kotlin.io  split 	kotlin.io  
startActivity 	kotlin.io  startMainActivity 	kotlin.io  	substring 	kotlin.io  take 	kotlin.io  to 	kotlin.io  toByteArray 	kotlin.io  
toMutableList 	kotlin.io  toString 	kotlin.io  trim 	kotlin.io  until 	kotlin.io  updateDisplayedUsername 	kotlin.io  updateOverlayMessage 	kotlin.io  use 	kotlin.io  users 	kotlin.io  withContext 	kotlin.io  ActivityDmChatBinding 
kotlin.jvm  ActivityDmRequestBinding 
kotlin.jvm  ActivityEncryptedChatBinding 
kotlin.jvm  AlertDialog 
kotlin.jvm  
BackupUtil 
kotlin.jvm  Base64 
kotlin.jvm  Build 
kotlin.jvm  ChatListAdapter 
kotlin.jvm  ChatMessage 
kotlin.jvm  ChatViewHolder 
kotlin.jvm  
ChatsFragment 
kotlin.jvm  Cipher 
kotlin.jvm  Color 
kotlin.jvm  ContactsFragment 
kotlin.jvm  Context 
kotlin.jvm  
ContextCompat 
kotlin.jvm  	DMAdapter 
kotlin.jvm  DMChatActivity 
kotlin.jvm  	DMRequest 
kotlin.jvm  DMRequestAdapter 
kotlin.jvm  Date 
kotlin.jvm  Dispatchers 
kotlin.jvm  DownloadManager 
kotlin.jvm  EditText 
kotlin.jvm  EncryptedChatActivity 
kotlin.jvm  EncryptionUtil 
kotlin.jvm  Environment 
kotlin.jvm  	Exception 
kotlin.jvm  File 
kotlin.jvm  FileProvider 
kotlin.jvm  FirebaseApp 
kotlin.jvm  FirebaseFirestore 
kotlin.jvm  FirebaseOptions 
kotlin.jvm  
FloatArray 
kotlin.jvm  FragmentChatsBinding 
kotlin.jvm  FragmentContactsBinding 
kotlin.jvm  FragmentGlobalChatBinding 
kotlin.jvm  FragmentWalletBinding 
kotlin.jvm  FrameLayout 
kotlin.jvm  GlobalChatFragment 
kotlin.jvm  Gravity 
kotlin.jvm  Gson 
kotlin.jvm  Handler 
kotlin.jvm  Intent 
kotlin.jvm  IntentFilter 
kotlin.jvm  ItemChatListBinding 
kotlin.jvm  ItemContactBinding 
kotlin.jvm  ItemDirectMessageBinding 
kotlin.jvm  ItemDmMessageBinding 
kotlin.jvm  ItemDmRequestBinding 
kotlin.jvm  ItemMessageBinding 
kotlin.jvm  ItemMessageMeBinding 
kotlin.jvm  ItemMessageOtherBinding 
kotlin.jvm  JvmOverloads 
kotlin.jvm  KeyGenParameterSpec 
kotlin.jvm  KeyPair 
kotlin.jvm  KeyPairGenerator 
kotlin.jvm  
KeyProperties 
kotlin.jvm  KeyStore 
kotlin.jvm  KeyStoreUtil 
kotlin.jvm  LAYER_TYPE_HARDWARE 
kotlin.jvm  LayoutInflater 
kotlin.jvm  LinearGradient 
kotlin.jvm  LinearLayoutManager 
kotlin.jvm  Locale 
kotlin.jvm  Log 
kotlin.jvm  Looper 
kotlin.jvm  MainActivity 
kotlin.jvm  Math 
kotlin.jvm  
MessageDigest 
kotlin.jvm  PackageManager 
kotlin.jvm  Paint 
kotlin.jvm  Pair 
kotlin.jvm  Path 
kotlin.jvm  PreferenceManager 
kotlin.jvm  PublicChatAdapter 
kotlin.jvm  R 
kotlin.jvm  RadialGradient 
kotlin.jvm  Random 
kotlin.jvm  RecyclerView 
kotlin.jvm  
SecretKeySpec 
kotlin.jvm  
SetOptions 
kotlin.jvm  SettingsFragment 
kotlin.jvm  Shader 
kotlin.jvm  SimpleContactsAdapter 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  String 
kotlin.jvm  System 
kotlin.jvm  TAG 
kotlin.jvm  TextView 
kotlin.jvm  Toast 
kotlin.jvm  Typeface 
kotlin.jvm  
UPDATE_URL 
kotlin.jvm  URL 
kotlin.jvm  UUID 
kotlin.jvm  
UpdateInfo 
kotlin.jvm  
UpdateManager 
kotlin.jvm  Uri 
kotlin.jvm  UsernameSetupActivity 
kotlin.jvm  View 
kotlin.jvm  	ViewGroup 
kotlin.jvm  WalletFragment 
kotlin.jvm  WebSettings 
kotlin.jvm  adapter 
kotlin.jvm  android 
kotlin.jvm  androidx 
kotlin.jvm  any 
kotlin.jvm  apply 
kotlin.jvm  await 
kotlin.jvm  binding 
kotlin.jvm  bufferedReader 
kotlin.jvm  
coerceAtLeast 
kotlin.jvm  
component1 
kotlin.jvm  
component2 
kotlin.jvm  
component3 
kotlin.jvm  contains 
kotlin.jvm  context 
kotlin.jvm  cos 
kotlin.jvm  db 
kotlin.jvm  
downloadId 
kotlin.jvm  	emptyList 
kotlin.jvm  fetchContactsAndIncomingDMs 
kotlin.jvm  filterIsInstance 
kotlin.jvm  finish 
kotlin.jvm  firstOrNull 
kotlin.jvm  floatArrayOf 
kotlin.jvm  get 
kotlin.jvm  getItem 
kotlin.jvm  getValue 
kotlin.jvm  	hashMapOf 
kotlin.jvm  
installApk 
kotlin.jvm  
intArrayOf 
kotlin.jvm  invoke 
kotlin.jvm  
isInitialized 
kotlin.jvm  
isNotBlank 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  
isNullOrBlank 
kotlin.jvm  java 
kotlin.jvm  joinToString 
kotlin.jvm  launch 
kotlin.jvm  lazy 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  	lowercase 
kotlin.jvm  map 
kotlin.jvm  
mapNotNull 
kotlin.jvm  mapOf 
kotlin.jvm  min 
kotlin.jvm  minusAssign 
kotlin.jvm  
mutableListOf 
kotlin.jvm  mutableMapOf 
kotlin.jvm  onClick 
kotlin.jvm  onItemClick 
kotlin.jvm  
plusAssign 
kotlin.jvm  progressBar 
kotlin.jvm  provideDelegate 
kotlin.jvm  random 
kotlin.jvm  readText 
kotlin.jvm  removeUpdateOverlay 
kotlin.jvm  requireContext 
kotlin.jvm  requireView 
kotlin.jvm  
searchUser 
kotlin.jvm  searchUserByUsername 
kotlin.jvm  set 
kotlin.jvm  showSeedPhraseDialog 
kotlin.jvm  sin 
kotlin.jvm  sorted 
kotlin.jvm  split 
kotlin.jvm  
startActivity 
kotlin.jvm  startMainActivity 
kotlin.jvm  	substring 
kotlin.jvm  take 
kotlin.jvm  to 
kotlin.jvm  toByteArray 
kotlin.jvm  
toMutableList 
kotlin.jvm  toString 
kotlin.jvm  trim 
kotlin.jvm  until 
kotlin.jvm  updateDisplayedUsername 
kotlin.jvm  updateOverlayMessage 
kotlin.jvm  use 
kotlin.jvm  users 
kotlin.jvm  withContext 
kotlin.jvm  cos kotlin.math  min kotlin.math  sin kotlin.math  ActivityDmChatBinding 
kotlin.ranges  ActivityDmRequestBinding 
kotlin.ranges  ActivityEncryptedChatBinding 
kotlin.ranges  AlertDialog 
kotlin.ranges  
BackupUtil 
kotlin.ranges  Base64 
kotlin.ranges  Build 
kotlin.ranges  ChatListAdapter 
kotlin.ranges  ChatMessage 
kotlin.ranges  ChatViewHolder 
kotlin.ranges  
ChatsFragment 
kotlin.ranges  Cipher 
kotlin.ranges  Color 
kotlin.ranges  ContactsFragment 
kotlin.ranges  Context 
kotlin.ranges  
ContextCompat 
kotlin.ranges  	DMAdapter 
kotlin.ranges  DMChatActivity 
kotlin.ranges  	DMRequest 
kotlin.ranges  DMRequestAdapter 
kotlin.ranges  Date 
kotlin.ranges  Dispatchers 
kotlin.ranges  DownloadManager 
kotlin.ranges  EditText 
kotlin.ranges  EncryptedChatActivity 
kotlin.ranges  EncryptionUtil 
kotlin.ranges  Environment 
kotlin.ranges  	Exception 
kotlin.ranges  File 
kotlin.ranges  FileProvider 
kotlin.ranges  FirebaseApp 
kotlin.ranges  FirebaseFirestore 
kotlin.ranges  FirebaseOptions 
kotlin.ranges  
FloatArray 
kotlin.ranges  FragmentChatsBinding 
kotlin.ranges  FragmentContactsBinding 
kotlin.ranges  FragmentGlobalChatBinding 
kotlin.ranges  FragmentWalletBinding 
kotlin.ranges  FrameLayout 
kotlin.ranges  GlobalChatFragment 
kotlin.ranges  Gravity 
kotlin.ranges  Gson 
kotlin.ranges  Handler 
kotlin.ranges  IntRange 
kotlin.ranges  Intent 
kotlin.ranges  IntentFilter 
kotlin.ranges  ItemChatListBinding 
kotlin.ranges  ItemContactBinding 
kotlin.ranges  ItemDirectMessageBinding 
kotlin.ranges  ItemDmMessageBinding 
kotlin.ranges  ItemDmRequestBinding 
kotlin.ranges  ItemMessageBinding 
kotlin.ranges  ItemMessageMeBinding 
kotlin.ranges  ItemMessageOtherBinding 
kotlin.ranges  JvmOverloads 
kotlin.ranges  KeyGenParameterSpec 
kotlin.ranges  KeyPair 
kotlin.ranges  KeyPairGenerator 
kotlin.ranges  
KeyProperties 
kotlin.ranges  KeyStore 
kotlin.ranges  KeyStoreUtil 
kotlin.ranges  LAYER_TYPE_HARDWARE 
kotlin.ranges  LayoutInflater 
kotlin.ranges  LinearGradient 
kotlin.ranges  LinearLayoutManager 
kotlin.ranges  Locale 
kotlin.ranges  Log 
kotlin.ranges  Looper 
kotlin.ranges  MainActivity 
kotlin.ranges  Math 
kotlin.ranges  
MessageDigest 
kotlin.ranges  PackageManager 
kotlin.ranges  Paint 
kotlin.ranges  Pair 
kotlin.ranges  Path 
kotlin.ranges  PreferenceManager 
kotlin.ranges  PublicChatAdapter 
kotlin.ranges  R 
kotlin.ranges  RadialGradient 
kotlin.ranges  Random 
kotlin.ranges  RecyclerView 
kotlin.ranges  
SecretKeySpec 
kotlin.ranges  
SetOptions 
kotlin.ranges  SettingsFragment 
kotlin.ranges  Shader 
kotlin.ranges  SimpleContactsAdapter 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  String 
kotlin.ranges  System 
kotlin.ranges  TAG 
kotlin.ranges  TextView 
kotlin.ranges  Toast 
kotlin.ranges  Typeface 
kotlin.ranges  
UPDATE_URL 
kotlin.ranges  URL 
kotlin.ranges  UUID 
kotlin.ranges  
UpdateInfo 
kotlin.ranges  
UpdateManager 
kotlin.ranges  Uri 
kotlin.ranges  UsernameSetupActivity 
kotlin.ranges  View 
kotlin.ranges  	ViewGroup 
kotlin.ranges  WalletFragment 
kotlin.ranges  WebSettings 
kotlin.ranges  adapter 
kotlin.ranges  android 
kotlin.ranges  androidx 
kotlin.ranges  any 
kotlin.ranges  apply 
kotlin.ranges  await 
kotlin.ranges  binding 
kotlin.ranges  bufferedReader 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  
component1 
kotlin.ranges  
component2 
kotlin.ranges  
component3 
kotlin.ranges  contains 
kotlin.ranges  context 
kotlin.ranges  cos 
kotlin.ranges  db 
kotlin.ranges  
downloadId 
kotlin.ranges  	emptyList 
kotlin.ranges  fetchContactsAndIncomingDMs 
kotlin.ranges  filterIsInstance 
kotlin.ranges  finish 
kotlin.ranges  firstOrNull 
kotlin.ranges  floatArrayOf 
kotlin.ranges  get 
kotlin.ranges  getItem 
kotlin.ranges  getValue 
kotlin.ranges  	hashMapOf 
kotlin.ranges  
installApk 
kotlin.ranges  
intArrayOf 
kotlin.ranges  invoke 
kotlin.ranges  
isInitialized 
kotlin.ranges  
isNotBlank 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  
isNullOrBlank 
kotlin.ranges  java 
kotlin.ranges  joinToString 
kotlin.ranges  launch 
kotlin.ranges  lazy 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  	lowercase 
kotlin.ranges  map 
kotlin.ranges  
mapNotNull 
kotlin.ranges  mapOf 
kotlin.ranges  min 
kotlin.ranges  minusAssign 
kotlin.ranges  
mutableListOf 
kotlin.ranges  mutableMapOf 
kotlin.ranges  onClick 
kotlin.ranges  onItemClick 
kotlin.ranges  
plusAssign 
kotlin.ranges  progressBar 
kotlin.ranges  provideDelegate 
kotlin.ranges  random 
kotlin.ranges  readText 
kotlin.ranges  removeUpdateOverlay 
kotlin.ranges  requireContext 
kotlin.ranges  requireView 
kotlin.ranges  
searchUser 
kotlin.ranges  searchUserByUsername 
kotlin.ranges  set 
kotlin.ranges  showSeedPhraseDialog 
kotlin.ranges  sin 
kotlin.ranges  sorted 
kotlin.ranges  split 
kotlin.ranges  
startActivity 
kotlin.ranges  startMainActivity 
kotlin.ranges  	substring 
kotlin.ranges  take 
kotlin.ranges  to 
kotlin.ranges  toByteArray 
kotlin.ranges  
toMutableList 
kotlin.ranges  toString 
kotlin.ranges  trim 
kotlin.ranges  until 
kotlin.ranges  updateDisplayedUsername 
kotlin.ranges  updateOverlayMessage 
kotlin.ranges  use 
kotlin.ranges  users 
kotlin.ranges  withContext 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  getISInitialized  kotlin.reflect.KMutableProperty0  getIsInitialized  kotlin.reflect.KMutableProperty0  
isInitialized  kotlin.reflect.KMutableProperty0  ActivityDmChatBinding kotlin.sequences  ActivityDmRequestBinding kotlin.sequences  ActivityEncryptedChatBinding kotlin.sequences  AlertDialog kotlin.sequences  
BackupUtil kotlin.sequences  Base64 kotlin.sequences  Build kotlin.sequences  ChatListAdapter kotlin.sequences  ChatMessage kotlin.sequences  ChatViewHolder kotlin.sequences  
ChatsFragment kotlin.sequences  Cipher kotlin.sequences  Color kotlin.sequences  ContactsFragment kotlin.sequences  Context kotlin.sequences  
ContextCompat kotlin.sequences  	DMAdapter kotlin.sequences  DMChatActivity kotlin.sequences  	DMRequest kotlin.sequences  DMRequestAdapter kotlin.sequences  Date kotlin.sequences  Dispatchers kotlin.sequences  DownloadManager kotlin.sequences  EditText kotlin.sequences  EncryptedChatActivity kotlin.sequences  EncryptionUtil kotlin.sequences  Environment kotlin.sequences  	Exception kotlin.sequences  File kotlin.sequences  FileProvider kotlin.sequences  FirebaseApp kotlin.sequences  FirebaseFirestore kotlin.sequences  FirebaseOptions kotlin.sequences  
FloatArray kotlin.sequences  FragmentChatsBinding kotlin.sequences  FragmentContactsBinding kotlin.sequences  FragmentGlobalChatBinding kotlin.sequences  FragmentWalletBinding kotlin.sequences  FrameLayout kotlin.sequences  GlobalChatFragment kotlin.sequences  Gravity kotlin.sequences  Gson kotlin.sequences  Handler kotlin.sequences  Intent kotlin.sequences  IntentFilter kotlin.sequences  ItemChatListBinding kotlin.sequences  ItemContactBinding kotlin.sequences  ItemDirectMessageBinding kotlin.sequences  ItemDmMessageBinding kotlin.sequences  ItemDmRequestBinding kotlin.sequences  ItemMessageBinding kotlin.sequences  ItemMessageMeBinding kotlin.sequences  ItemMessageOtherBinding kotlin.sequences  JvmOverloads kotlin.sequences  KeyGenParameterSpec kotlin.sequences  KeyPair kotlin.sequences  KeyPairGenerator kotlin.sequences  
KeyProperties kotlin.sequences  KeyStore kotlin.sequences  KeyStoreUtil kotlin.sequences  LAYER_TYPE_HARDWARE kotlin.sequences  LayoutInflater kotlin.sequences  LinearGradient kotlin.sequences  LinearLayoutManager kotlin.sequences  Locale kotlin.sequences  Log kotlin.sequences  Looper kotlin.sequences  MainActivity kotlin.sequences  Math kotlin.sequences  
MessageDigest kotlin.sequences  PackageManager kotlin.sequences  Paint kotlin.sequences  Pair kotlin.sequences  Path kotlin.sequences  PreferenceManager kotlin.sequences  PublicChatAdapter kotlin.sequences  R kotlin.sequences  RadialGradient kotlin.sequences  Random kotlin.sequences  RecyclerView kotlin.sequences  
SecretKeySpec kotlin.sequences  
SetOptions kotlin.sequences  SettingsFragment kotlin.sequences  Shader kotlin.sequences  SimpleContactsAdapter kotlin.sequences  SimpleDateFormat kotlin.sequences  String kotlin.sequences  System kotlin.sequences  TAG kotlin.sequences  TextView kotlin.sequences  Toast kotlin.sequences  Typeface kotlin.sequences  
UPDATE_URL kotlin.sequences  URL kotlin.sequences  UUID kotlin.sequences  
UpdateInfo kotlin.sequences  
UpdateManager kotlin.sequences  Uri kotlin.sequences  UsernameSetupActivity kotlin.sequences  View kotlin.sequences  	ViewGroup kotlin.sequences  WalletFragment kotlin.sequences  WebSettings kotlin.sequences  adapter kotlin.sequences  android kotlin.sequences  androidx kotlin.sequences  any kotlin.sequences  apply kotlin.sequences  await kotlin.sequences  binding kotlin.sequences  bufferedReader kotlin.sequences  
coerceAtLeast kotlin.sequences  
component1 kotlin.sequences  
component2 kotlin.sequences  
component3 kotlin.sequences  contains kotlin.sequences  context kotlin.sequences  cos kotlin.sequences  db kotlin.sequences  
downloadId kotlin.sequences  	emptyList kotlin.sequences  fetchContactsAndIncomingDMs kotlin.sequences  filterIsInstance kotlin.sequences  finish kotlin.sequences  firstOrNull kotlin.sequences  floatArrayOf kotlin.sequences  get kotlin.sequences  getItem kotlin.sequences  getValue kotlin.sequences  	hashMapOf kotlin.sequences  
installApk kotlin.sequences  
intArrayOf kotlin.sequences  invoke kotlin.sequences  
isInitialized kotlin.sequences  
isNotBlank kotlin.sequences  
isNotEmpty kotlin.sequences  
isNullOrBlank kotlin.sequences  java kotlin.sequences  joinToString kotlin.sequences  launch kotlin.sequences  lazy kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  	lowercase kotlin.sequences  map kotlin.sequences  
mapNotNull kotlin.sequences  mapOf kotlin.sequences  min kotlin.sequences  minusAssign kotlin.sequences  
mutableListOf kotlin.sequences  mutableMapOf kotlin.sequences  onClick kotlin.sequences  onItemClick kotlin.sequences  
plusAssign kotlin.sequences  progressBar kotlin.sequences  provideDelegate kotlin.sequences  random kotlin.sequences  readText kotlin.sequences  removeUpdateOverlay kotlin.sequences  requireContext kotlin.sequences  requireView kotlin.sequences  
searchUser kotlin.sequences  searchUserByUsername kotlin.sequences  set kotlin.sequences  showSeedPhraseDialog kotlin.sequences  sin kotlin.sequences  sorted kotlin.sequences  split kotlin.sequences  
startActivity kotlin.sequences  startMainActivity kotlin.sequences  	substring kotlin.sequences  take kotlin.sequences  to kotlin.sequences  toByteArray kotlin.sequences  
toMutableList kotlin.sequences  toString kotlin.sequences  trim kotlin.sequences  until kotlin.sequences  updateDisplayedUsername kotlin.sequences  updateOverlayMessage kotlin.sequences  use kotlin.sequences  users kotlin.sequences  withContext kotlin.sequences  ActivityDmChatBinding kotlin.text  ActivityDmRequestBinding kotlin.text  ActivityEncryptedChatBinding kotlin.text  AlertDialog kotlin.text  
BackupUtil kotlin.text  Base64 kotlin.text  Build kotlin.text  ChatListAdapter kotlin.text  ChatMessage kotlin.text  ChatViewHolder kotlin.text  
ChatsFragment kotlin.text  Cipher kotlin.text  Color kotlin.text  ContactsFragment kotlin.text  Context kotlin.text  
ContextCompat kotlin.text  	DMAdapter kotlin.text  DMChatActivity kotlin.text  	DMRequest kotlin.text  DMRequestAdapter kotlin.text  Date kotlin.text  Dispatchers kotlin.text  DownloadManager kotlin.text  EditText kotlin.text  EncryptedChatActivity kotlin.text  EncryptionUtil kotlin.text  Environment kotlin.text  	Exception kotlin.text  File kotlin.text  FileProvider kotlin.text  FirebaseApp kotlin.text  FirebaseFirestore kotlin.text  FirebaseOptions kotlin.text  
FloatArray kotlin.text  FragmentChatsBinding kotlin.text  FragmentContactsBinding kotlin.text  FragmentGlobalChatBinding kotlin.text  FragmentWalletBinding kotlin.text  FrameLayout kotlin.text  GlobalChatFragment kotlin.text  Gravity kotlin.text  Gson kotlin.text  Handler kotlin.text  Intent kotlin.text  IntentFilter kotlin.text  ItemChatListBinding kotlin.text  ItemContactBinding kotlin.text  ItemDirectMessageBinding kotlin.text  ItemDmMessageBinding kotlin.text  ItemDmRequestBinding kotlin.text  ItemMessageBinding kotlin.text  ItemMessageMeBinding kotlin.text  ItemMessageOtherBinding kotlin.text  JvmOverloads kotlin.text  KeyGenParameterSpec kotlin.text  KeyPair kotlin.text  KeyPairGenerator kotlin.text  
KeyProperties kotlin.text  KeyStore kotlin.text  KeyStoreUtil kotlin.text  LAYER_TYPE_HARDWARE kotlin.text  LayoutInflater kotlin.text  LinearGradient kotlin.text  LinearLayoutManager kotlin.text  Locale kotlin.text  Log kotlin.text  Looper kotlin.text  MainActivity kotlin.text  Math kotlin.text  
MessageDigest kotlin.text  PackageManager kotlin.text  Paint kotlin.text  Pair kotlin.text  Path kotlin.text  PreferenceManager kotlin.text  PublicChatAdapter kotlin.text  R kotlin.text  RadialGradient kotlin.text  Random kotlin.text  RecyclerView kotlin.text  
SecretKeySpec kotlin.text  
SetOptions kotlin.text  SettingsFragment kotlin.text  Shader kotlin.text  SimpleContactsAdapter kotlin.text  SimpleDateFormat kotlin.text  String kotlin.text  System kotlin.text  TAG kotlin.text  TextView kotlin.text  Toast kotlin.text  Typeface kotlin.text  
UPDATE_URL kotlin.text  URL kotlin.text  UUID kotlin.text  
UpdateInfo kotlin.text  
UpdateManager kotlin.text  Uri kotlin.text  UsernameSetupActivity kotlin.text  View kotlin.text  	ViewGroup kotlin.text  WalletFragment kotlin.text  WebSettings kotlin.text  adapter kotlin.text  android kotlin.text  androidx kotlin.text  any kotlin.text  apply kotlin.text  await kotlin.text  binding kotlin.text  bufferedReader kotlin.text  
coerceAtLeast kotlin.text  
component1 kotlin.text  
component2 kotlin.text  
component3 kotlin.text  contains kotlin.text  context kotlin.text  cos kotlin.text  db kotlin.text  
downloadId kotlin.text  	emptyList kotlin.text  fetchContactsAndIncomingDMs kotlin.text  filterIsInstance kotlin.text  finish kotlin.text  firstOrNull kotlin.text  floatArrayOf kotlin.text  get kotlin.text  getItem kotlin.text  getValue kotlin.text  	hashMapOf kotlin.text  
installApk kotlin.text  
intArrayOf kotlin.text  invoke kotlin.text  
isInitialized kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrBlank kotlin.text  java kotlin.text  joinToString kotlin.text  launch kotlin.text  lazy kotlin.text  let kotlin.text  listOf kotlin.text  	lowercase kotlin.text  map kotlin.text  
mapNotNull kotlin.text  mapOf kotlin.text  min kotlin.text  minusAssign kotlin.text  
mutableListOf kotlin.text  mutableMapOf kotlin.text  onClick kotlin.text  onItemClick kotlin.text  
plusAssign kotlin.text  progressBar kotlin.text  provideDelegate kotlin.text  random kotlin.text  readText kotlin.text  removeUpdateOverlay kotlin.text  requireContext kotlin.text  requireView kotlin.text  
searchUser kotlin.text  searchUserByUsername kotlin.text  set kotlin.text  showSeedPhraseDialog kotlin.text  sin kotlin.text  sorted kotlin.text  split kotlin.text  
startActivity kotlin.text  startMainActivity kotlin.text  	substring kotlin.text  take kotlin.text  to kotlin.text  toByteArray kotlin.text  
toMutableList kotlin.text  toString kotlin.text  trim kotlin.text  until kotlin.text  updateDisplayedUsername kotlin.text  updateOverlayMessage kotlin.text  use kotlin.text  users kotlin.text  withContext kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  
BackupUtil !kotlinx.coroutines.CoroutineScope  Gson !kotlinx.coroutines.CoroutineScope  Intent !kotlinx.coroutines.CoroutineScope  KeyStoreUtil !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  MainActivity !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  
UPDATE_URL !kotlinx.coroutines.CoroutineScope  URL !kotlinx.coroutines.CoroutineScope  
UpdateInfo !kotlinx.coroutines.CoroutineScope  
UpdateManager !kotlinx.coroutines.CoroutineScope  await !kotlinx.coroutines.CoroutineScope  bufferedReader !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope  db !kotlinx.coroutines.CoroutineScope  finish !kotlinx.coroutines.CoroutineScope  getAWAIT !kotlinx.coroutines.CoroutineScope  getAwait !kotlinx.coroutines.CoroutineScope  getBUFFEREDReader !kotlinx.coroutines.CoroutineScope  getBufferedReader !kotlinx.coroutines.CoroutineScope  
getCONTEXT !kotlinx.coroutines.CoroutineScope  
getContext !kotlinx.coroutines.CoroutineScope  getDB !kotlinx.coroutines.CoroutineScope  getDb !kotlinx.coroutines.CoroutineScope  	getFINISH !kotlinx.coroutines.CoroutineScope  	getFinish !kotlinx.coroutines.CoroutineScope  getHASHMapOf !kotlinx.coroutines.CoroutineScope  getHashMapOf !kotlinx.coroutines.CoroutineScope  getREADText !kotlinx.coroutines.CoroutineScope  getREQUIREContext !kotlinx.coroutines.CoroutineScope  getREQUIREView !kotlinx.coroutines.CoroutineScope  getReadText !kotlinx.coroutines.CoroutineScope  getRequireContext !kotlinx.coroutines.CoroutineScope  getRequireView !kotlinx.coroutines.CoroutineScope  getSHOWSeedPhraseDialog !kotlinx.coroutines.CoroutineScope  getSTARTActivity !kotlinx.coroutines.CoroutineScope  getSTARTMainActivity !kotlinx.coroutines.CoroutineScope  getShowSeedPhraseDialog !kotlinx.coroutines.CoroutineScope  getStartActivity !kotlinx.coroutines.CoroutineScope  getStartMainActivity !kotlinx.coroutines.CoroutineScope  getTO !kotlinx.coroutines.CoroutineScope  getTo !kotlinx.coroutines.CoroutineScope  getUPDATEDisplayedUsername !kotlinx.coroutines.CoroutineScope  getUSE !kotlinx.coroutines.CoroutineScope  getUpdateDisplayedUsername !kotlinx.coroutines.CoroutineScope  getUse !kotlinx.coroutines.CoroutineScope  	hashMapOf !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  readText !kotlinx.coroutines.CoroutineScope  requireContext !kotlinx.coroutines.CoroutineScope  requireView !kotlinx.coroutines.CoroutineScope  showSeedPhraseDialog !kotlinx.coroutines.CoroutineScope  
startActivity !kotlinx.coroutines.CoroutineScope  startMainActivity !kotlinx.coroutines.CoroutineScope  to !kotlinx.coroutines.CoroutineScope  updateDisplayedUsername !kotlinx.coroutines.CoroutineScope  use !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  await kotlinx.coroutines.tasks                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      