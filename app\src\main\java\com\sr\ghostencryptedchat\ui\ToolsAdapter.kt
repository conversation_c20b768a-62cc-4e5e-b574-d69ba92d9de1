package com.sr.ghostencryptedchat.ui

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.sr.ghostencryptedchat.R
import com.sr.ghostencryptedchat.databinding.ItemToolBinding

class ToolsAdapter(
    private val tools: List<ToolItem>,
    private val onToolClick: (ToolItem) -> Unit
) : RecyclerView.Adapter<ToolsAdapter.ToolViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ToolViewHolder {
        val binding = ItemToolBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ToolViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ToolViewHolder, position: Int) {
        holder.bind(tools[position])
    }

    override fun getItemCount(): Int = tools.size

    inner class ToolViewHolder(private val binding: ItemToolBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(tool: ToolItem) {
            binding.apply {
                toolIcon.text = tool.icon
                toolTitle.text = tool.title
                toolDescription.text = tool.description
                
                // Set availability styling
                if (tool.isAvailable) {
                    toolTitle.setTextColor(
                        ContextCompat.getColor(itemView.context, R.color.text_primary)
                    )
                    toolDescription.setTextColor(
                        ContextCompat.getColor(itemView.context, R.color.text_secondary)
                    )
                    toolIcon.alpha = 1.0f
                    comingSoonLabel.visibility = android.view.View.GONE
                } else {
                    toolTitle.setTextColor(
                        ContextCompat.getColor(itemView.context, R.color.text_disabled)
                    )
                    toolDescription.setTextColor(
                        ContextCompat.getColor(itemView.context, R.color.text_disabled)
                    )
                    toolIcon.alpha = 0.5f
                    comingSoonLabel.visibility = android.view.View.VISIBLE
                }
                
                root.setOnClickListener {
                    onToolClick(tool)
                }
            }
        }
    }
}
