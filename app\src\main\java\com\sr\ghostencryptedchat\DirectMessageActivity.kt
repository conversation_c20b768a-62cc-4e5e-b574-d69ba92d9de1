package com.sr.ghostencryptedchat

import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.ListView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.firebase.firestore.FirebaseFirestore
import com.sr.ghostencryptedchat.util.EncryptionUtil
import com.sr.ghostencryptedchat.util.KeyStoreUtil

class DirectMessageActivity : AppCompatActivity() {

    private lateinit var db: FirebaseFirestore
    private lateinit var chatId: String
    private lateinit var currentUser: String
    private lateinit var recipient: String

    private lateinit var dmList: ListView
    private lateinit var input: EditText
    private lateinit var sendButton: Button

    private val messages = mutableListOf<String>()
    private lateinit var adapter: android.widget.ArrayAdapter<String>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_direct_message)

        db = FirebaseFirestore.getInstance()
        currentUser = KeyStoreUtil.getOrCreateUsername(this)
        chatId = intent.getStringExtra("chatId") ?: return
        recipient = intent.getStringExtra("recipient") ?: "Unknown"

        dmList = findViewById(R.id.dmList)
        input = findViewById(R.id.dmInput)
        sendButton = findViewById(R.id.dmSend)

        adapter = android.widget.ArrayAdapter(this, android.R.layout.simple_list_item_1, messages)
        dmList.adapter = adapter

        listenToMessages()

        sendButton.setOnClickListener {
            val plain = input.text.toString()
            if (plain.isNotBlank()) {
                val encrypted = EncryptionUtil.encrypt(plain)
                val msg = hashMapOf(
                    "sender" to currentUser,
                    "content" to encrypted,
                    "timestamp" to System.currentTimeMillis()
                )
                db.collection("directMessages").document(chatId)
                    .collection("messages").add(msg)

                input.setText("")
            } else {
                Toast.makeText(this, "Message can't be empty", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun listenToMessages() {
        db.collection("directMessages").document(chatId)
            .collection("messages")
            .orderBy("timestamp")
            .addSnapshotListener { snapshot, _ ->
                if (snapshot != null) {
                    messages.clear()
                    for (doc in snapshot.documents) {
                        val encrypted = doc.getString("content") ?: continue
                        val sender = doc.getString("sender") ?: "?"
                        val decrypted = EncryptionUtil.decrypt(encrypted)
                        messages.add("$sender: $decrypted")
                    }
                    adapter.notifyDataSetChanged()
                    dmList.smoothScrollToPosition(messages.size - 1)
                }
            }
    }
}
