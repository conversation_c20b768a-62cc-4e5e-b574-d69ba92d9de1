// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemChatBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView avatar;

  @NonNull
  public final TextView chatMessagePreview;

  @NonNull
  public final TextView chatName;

  @NonNull
  public final TextView chatTimestamp;

  @NonNull
  public final TextView unreadBadge;

  private ItemChatBinding(@NonNull CardView rootView, @NonNull ImageView avatar,
      @NonNull TextView chatMessagePreview, @NonNull TextView chatName,
      @NonNull TextView chatTimestamp, @NonNull TextView unreadBadge) {
    this.rootView = rootView;
    this.avatar = avatar;
    this.chatMessagePreview = chatMessagePreview;
    this.chatName = chatName;
    this.chatTimestamp = chatTimestamp;
    this.unreadBadge = unreadBadge;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemChatBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemChatBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_chat, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemChatBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.avatar;
      ImageView avatar = ViewBindings.findChildViewById(rootView, id);
      if (avatar == null) {
        break missingId;
      }

      id = R.id.chatMessagePreview;
      TextView chatMessagePreview = ViewBindings.findChildViewById(rootView, id);
      if (chatMessagePreview == null) {
        break missingId;
      }

      id = R.id.chatName;
      TextView chatName = ViewBindings.findChildViewById(rootView, id);
      if (chatName == null) {
        break missingId;
      }

      id = R.id.chatTimestamp;
      TextView chatTimestamp = ViewBindings.findChildViewById(rootView, id);
      if (chatTimestamp == null) {
        break missingId;
      }

      id = R.id.unreadBadge;
      TextView unreadBadge = ViewBindings.findChildViewById(rootView, id);
      if (unreadBadge == null) {
        break missingId;
      }

      return new ItemChatBinding((CardView) rootView, avatar, chatMessagePreview, chatName,
          chatTimestamp, unreadBadge);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
