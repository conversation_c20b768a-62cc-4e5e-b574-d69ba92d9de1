package com.sr.ghostencryptedchat.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.sr.ghostencryptedchat.databinding.FragmentPortScannerBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.net.InetSocketAddress
import java.net.Socket

class PortScannerFragment : Fragment() {

    private var _binding: FragmentPortScannerBinding? = null
    private val binding get() = _binding!!

    private lateinit var portAdapter: PortScanResultAdapter
    private val scanResults = mutableListOf<PortScanResult>()
    private var isScanning = false

    // Common ports to scan
    private val commonPorts = mapOf(
        21 to "FTP",
        22 to "SSH",
        23 to "Telnet",
        25 to "SMTP",
        53 to "DNS",
        80 to "HTTP",
        110 to "POP3",
        143 to "IMAP",
        443 to "HTTPS",
        993 to "IMAPS",
        995 to "POP3S",
        1433 to "SQL Server",
        3306 to "MySQL",
        3389 to "RDP",
        5432 to "PostgreSQL",
        5900 to "VNC",
        8080 to "HTTP Alt",
        8443 to "HTTPS Alt"
    )

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPortScannerBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupUI()
    }

    private fun setupUI() {
        portAdapter = PortScanResultAdapter(scanResults)
        
        binding.resultsRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = portAdapter
        }
        
        // Set default target
        binding.targetInput.setText("127.0.0.1")
        
        binding.scanCommonButton.setOnClickListener {
            if (!isScanning) {
                startPortScan(commonPorts.keys.toList())
            }
        }
        
        binding.scanCustomButton.setOnClickListener {
            if (!isScanning) {
                val customPorts = parseCustomPorts()
                if (customPorts.isNotEmpty()) {
                    startPortScan(customPorts)
                } else {
                    Toast.makeText(requireContext(), "Please enter valid port numbers", Toast.LENGTH_SHORT).show()
                }
            }
        }
        
        binding.backButton.setOnClickListener {
            parentFragmentManager.popBackStack()
        }
    }

    private fun parseCustomPorts(): List<Int> {
        val input = binding.customPortsInput.text.toString().trim()
        if (input.isEmpty()) return emptyList()
        
        return try {
            input.split(",")
                .map { it.trim() }
                .filter { it.isNotEmpty() }
                .map { portStr ->
                    if (portStr.contains("-")) {
                        // Handle range like "80-90"
                        val range = portStr.split("-")
                        val start = range[0].toInt()
                        val end = range[1].toInt()
                        (start..end).toList()
                    } else {
                        // Single port
                        listOf(portStr.toInt())
                    }
                }
                .flatten()
                .filter { it in 1..65535 }
        } catch (e: Exception) {
            emptyList()
        }
    }

    private fun startPortScan(ports: List<Int>) {
        val target = binding.targetInput.text.toString().trim()
        if (target.isEmpty()) {
            Toast.makeText(requireContext(), "Please enter a target host", Toast.LENGTH_SHORT).show()
            return
        }
        
        isScanning = true
        binding.scanCommonButton.isEnabled = false
        binding.scanCustomButton.isEnabled = false
        binding.progressBar.visibility = View.VISIBLE
        binding.scanStatus.text = "Scanning ${ports.size} ports..."
        
        scanResults.clear()
        portAdapter.notifyDataSetChanged()
        
        lifecycleScope.launch {
            try {
                scanPorts(target, ports)
            } finally {
                withContext(Dispatchers.Main) {
                    isScanning = false
                    binding.scanCommonButton.isEnabled = true
                    binding.scanCustomButton.isEnabled = true
                    binding.progressBar.visibility = View.GONE
                    binding.scanStatus.text = "Scan completed. Found ${scanResults.count { it.isOpen }} open ports."
                }
            }
        }
    }

    private suspend fun scanPorts(target: String, ports: List<Int>) {
        withContext(Dispatchers.IO) {
            ports.forEachIndexed { index, port ->
                try {
                    val socket = Socket()
                    val startTime = System.currentTimeMillis()
                    
                    socket.connect(InetSocketAddress(target, port), 3000) // 3 second timeout
                    val responseTime = System.currentTimeMillis() - startTime
                    
                    socket.close()
                    
                    // Port is open
                    val result = PortScanResult(
                        port = port,
                        service = commonPorts[port] ?: "Unknown",
                        isOpen = true,
                        responseTime = responseTime
                    )
                    
                    withContext(Dispatchers.Main) {
                        scanResults.add(result)
                        portAdapter.notifyItemInserted(scanResults.size - 1)
                        binding.scanStatus.text = "Scanning... ${index + 1}/${ports.size} (Found ${scanResults.count { it.isOpen }} open)"
                    }
                    
                } catch (e: Exception) {
                    // Port is closed or filtered
                    val result = PortScanResult(
                        port = port,
                        service = commonPorts[port] ?: "Unknown",
                        isOpen = false,
                        responseTime = 0
                    )
                    
                    withContext(Dispatchers.Main) {
                        scanResults.add(result)
                        portAdapter.notifyItemInserted(scanResults.size - 1)
                        binding.scanStatus.text = "Scanning... ${index + 1}/${ports.size} (Found ${scanResults.count { it.isOpen }} open)"
                    }
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

data class PortScanResult(
    val port: Int,
    val service: String,
    val isOpen: Boolean,
    val responseTime: Long
)
