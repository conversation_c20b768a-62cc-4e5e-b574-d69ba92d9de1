// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityUsernameSetupBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button newAccountButton;

  @NonNull
  public final LinearLayout newAccountLayout;

  @NonNull
  public final Button recoverAccountButton;

  @NonNull
  public final LinearLayout recoverAccountLayout;

  @NonNull
  public final Button recoverButton;

  @NonNull
  public final Button registerButton;

  @NonNull
  public final EditText seedPhraseInput;

  @NonNull
  public final EditText usernameInput;

  @NonNull
  public final TextView warningText;

  private ActivityUsernameSetupBinding(@NonNull LinearLayout rootView,
      @NonNull Button newAccountButton, @NonNull LinearLayout newAccountLayout,
      @NonNull Button recoverAccountButton, @NonNull LinearLayout recoverAccountLayout,
      @NonNull Button recoverButton, @NonNull Button registerButton,
      @NonNull EditText seedPhraseInput, @NonNull EditText usernameInput,
      @NonNull TextView warningText) {
    this.rootView = rootView;
    this.newAccountButton = newAccountButton;
    this.newAccountLayout = newAccountLayout;
    this.recoverAccountButton = recoverAccountButton;
    this.recoverAccountLayout = recoverAccountLayout;
    this.recoverButton = recoverButton;
    this.registerButton = registerButton;
    this.seedPhraseInput = seedPhraseInput;
    this.usernameInput = usernameInput;
    this.warningText = warningText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityUsernameSetupBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityUsernameSetupBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_username_setup, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityUsernameSetupBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.newAccountButton;
      Button newAccountButton = ViewBindings.findChildViewById(rootView, id);
      if (newAccountButton == null) {
        break missingId;
      }

      id = R.id.newAccountLayout;
      LinearLayout newAccountLayout = ViewBindings.findChildViewById(rootView, id);
      if (newAccountLayout == null) {
        break missingId;
      }

      id = R.id.recoverAccountButton;
      Button recoverAccountButton = ViewBindings.findChildViewById(rootView, id);
      if (recoverAccountButton == null) {
        break missingId;
      }

      id = R.id.recoverAccountLayout;
      LinearLayout recoverAccountLayout = ViewBindings.findChildViewById(rootView, id);
      if (recoverAccountLayout == null) {
        break missingId;
      }

      id = R.id.recover_button;
      Button recoverButton = ViewBindings.findChildViewById(rootView, id);
      if (recoverButton == null) {
        break missingId;
      }

      id = R.id.register_button;
      Button registerButton = ViewBindings.findChildViewById(rootView, id);
      if (registerButton == null) {
        break missingId;
      }

      id = R.id.seed_phrase_input;
      EditText seedPhraseInput = ViewBindings.findChildViewById(rootView, id);
      if (seedPhraseInput == null) {
        break missingId;
      }

      id = R.id.username_input;
      EditText usernameInput = ViewBindings.findChildViewById(rootView, id);
      if (usernameInput == null) {
        break missingId;
      }

      id = R.id.warning_text;
      TextView warningText = ViewBindings.findChildViewById(rootView, id);
      if (warningText == null) {
        break missingId;
      }

      return new ActivityUsernameSetupBinding((LinearLayout) rootView, newAccountButton,
          newAccountLayout, recoverAccountButton, recoverAccountLayout, recoverButton,
          registerButton, seedPhraseInput, usernameInput, warningText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
