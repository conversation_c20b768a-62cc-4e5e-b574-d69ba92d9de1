package com.sr.ghostencryptedchat.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.sr.ghostencryptedchat.databinding.ItemDmMessageBinding
import com.sr.ghostencryptedchat.model.DMMessage
import com.sr.ghostencryptedchat.util.EncryptionUtil

class DMChatAdapter(
    private val messages: List<DMMessage>,
    private val username: String
) : RecyclerView.Adapter<DMChatAdapter.DMViewHolder>() {

    inner class DMViewHolder(val binding: ItemDmMessageBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DMViewHolder {
        val binding = ItemDmMessageBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return DMViewHolder(binding)
    }

    override fun onBindViewHolder(holder: DMViewHolder, position: Int) {
        val message = messages[position]
        val isOwn = message.from == username
        val decrypted = EncryptionUtil.decrypt(message.content)
        holder.binding.messageText.text = decrypted
        holder.binding.messageText.textAlignment =
            if (isOwn) android.view.View.TEXT_ALIGNMENT_VIEW_END else android.view.View.TEXT_ALIGNMENT_VIEW_START
    }

    override fun getItemCount() = messages.size
}
