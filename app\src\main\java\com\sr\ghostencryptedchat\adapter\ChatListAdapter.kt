package com.sr.ghostencryptedchat.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.sr.ghostencryptedchat.R
import com.sr.ghostencryptedchat.databinding.ItemChatListBinding

class ChatListAdapter(
    private val items: List<Pair<String, Boolean>>,
    private val onClick: (String) -> Unit,
    private val onLongClick: (String) -> Unit
) : RecyclerView.Adapter<ChatListAdapter.ChatViewHolder>() {

    inner class ChatViewHolder(val binding: ItemChatListBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatViewHolder {
        val binding = ItemChatListBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ChatViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ChatViewHolder, position: Int) {
        val (name, isNew) = items[position]
        holder.binding.chatName.text = name

        if (name == "World Chat") {
            holder.binding.chatIcon.setImageResource(R.drawable.ic_globe)
        } else {
            holder.binding.chatIcon.setImageResource(R.drawable.ic_person)
        }

        // NEW label setup
        if (isNew) {
            holder.binding.chatNewLabel.apply {
                visibility = View.VISIBLE
                text = "NEW"
                setTextColor(Color.parseColor("#aeff00")) // Neon green
                setTypeface(typeface, android.graphics.Typeface.BOLD)
            }
        } else {
            holder.binding.chatNewLabel.visibility = View.GONE
        }

        holder.binding.root.setOnClickListener {
            onClick(name)
        }

        if (name != "World Chat") {
            holder.binding.root.setOnLongClickListener {
                onLongClick(name)
                true
            }
        }
    }

    override fun getItemCount(): Int = items.size
}
