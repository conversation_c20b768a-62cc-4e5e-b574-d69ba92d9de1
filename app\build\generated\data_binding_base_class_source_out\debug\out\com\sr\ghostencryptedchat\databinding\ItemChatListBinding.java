// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemChatListBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView chatIcon;

  @NonNull
  public final TextView chatName;

  @NonNull
  public final TextView chatNewLabel;

  private ItemChatListBinding(@NonNull CardView rootView, @NonNull ImageView chatIcon,
      @NonNull TextView chatName, @NonNull TextView chatNewLabel) {
    this.rootView = rootView;
    this.chatIcon = chatIcon;
    this.chatName = chatName;
    this.chatNewLabel = chatNewLabel;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemChatListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemChatListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_chat_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemChatListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.chatIcon;
      ImageView chatIcon = ViewBindings.findChildViewById(rootView, id);
      if (chatIcon == null) {
        break missingId;
      }

      id = R.id.chatName;
      TextView chatName = ViewBindings.findChildViewById(rootView, id);
      if (chatName == null) {
        break missingId;
      }

      id = R.id.chatNewLabel;
      TextView chatNewLabel = ViewBindings.findChildViewById(rootView, id);
      if (chatNewLabel == null) {
        break missingId;
      }

      return new ItemChatListBinding((CardView) rootView, chatIcon, chatName, chatNewLabel);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
