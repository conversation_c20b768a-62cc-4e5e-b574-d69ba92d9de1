package com.sr.ghostencryptedchat

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.firebase.FirebaseApp
import com.google.firebase.FirebaseOptions
import com.google.firebase.firestore.FirebaseFirestore
import com.sr.ghostencryptedchat.util.KeyStoreUtil
import com.sr.ghostencryptedchat.util.UpdateManager
import kotlinx.coroutines.launch

class SplashActivity : AppCompatActivity() {

    private val TAG = "SplashActivity"
    private val SPLASH_DELAY = 2000L // 2 seconds

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // ✅ Manually initialize FirebaseApp only if not already initialized
        if (FirebaseApp.getApps(this).isEmpty()) {
            val options = FirebaseOptions.Builder()
                .setProjectId("ghostencryptedchat")
                .setApplicationId("1:530964170349:android:d3b193427e6a469261e164")
                .setApiKey("AIzaSyAHTxLQJXkpYEhQWHvKVBkqGNdj-CfSJ3Q")
                .build()
            FirebaseApp.initializeApp(this, options)
        }

        setContentView(R.layout.activity_splash)
        checkForUpdates()

        Handler(Looper.getMainLooper()).postDelayed({
            checkUsernameAndNavigate()
        }, SPLASH_DELAY)
    }

    private fun checkForUpdates() {
        lifecycleScope.launch {
            try {
                val updateManager = UpdateManager(this@SplashActivity)
                updateManager.checkForUpdate()
            } catch (e: Exception) {
                Log.e(TAG, "Update check failed: ${e.message}")
            }
        }
    }

    private fun checkUsernameAndNavigate() {
        val username = KeyStoreUtil.getOrCreateUsername(this)
        val db = FirebaseFirestore.getInstance()

        db.collection("users").document(username).get()
            .addOnSuccessListener { doc ->
                if (doc.exists() && doc.getString("username") != null) {
                    startActivity(Intent(this, EncryptedChatActivity::class.java))
                } else {
                    startActivity(Intent(this, UsernameSetupActivity::class.java))
                }
                finish()
            }
            .addOnFailureListener {
                startActivity(Intent(this, EncryptedChatActivity::class.java))
                finish()
            }
    }
}
