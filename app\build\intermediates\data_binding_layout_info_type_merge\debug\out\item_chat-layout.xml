<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_chat" modulePackage="com.sr.ghostencryptedchat" filePath="app\src\main\res\layout\item_chat.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_chat_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="81" endOffset="35"/></Target><Target id="@+id/avatar" view="ImageView"><Expressions/><location startLine="16" startOffset="8" endLine="25" endOffset="35"/></Target><Target id="@+id/chatName" view="TextView"><Expressions/><location startLine="28" startOffset="8" endLine="37" endOffset="38"/></Target><Target id="@+id/chatTimestamp" view="TextView"><Expressions/><location startLine="40" startOffset="8" endLine="48" endOffset="37"/></Target><Target id="@+id/chatMessagePreview" view="TextView"><Expressions/><location startLine="51" startOffset="8" endLine="64" endOffset="37"/></Target><Target id="@+id/unreadBadge" view="TextView"><Expressions/><location startLine="67" startOffset="8" endLine="79" endOffset="42"/></Target></Targets></Layout>