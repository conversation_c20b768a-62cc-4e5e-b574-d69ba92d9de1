// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.sr.ghostencryptedchat.R;
import com.sr.ghostencryptedchat.view.ThreeDBackgroundView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDmChatBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final ImageButton addToContactsButton;

  @NonNull
  public final ThreeDBackgroundView backgroundView;

  @NonNull
  public final BottomNavigationView bottomNav;

  @NonNull
  public final TextView chatTitle;

  @NonNull
  public final LinearLayout dmInputContainer;

  @NonNull
  public final EditText dmMessageInput;

  @NonNull
  public final RecyclerView dmRecyclerView;

  @NonNull
  public final LinearLayout dmTopBar;

  @NonNull
  public final ImageButton sendDMButton;

  private ActivityDmChatBinding(@NonNull CoordinatorLayout rootView,
      @NonNull ImageButton addToContactsButton, @NonNull ThreeDBackgroundView backgroundView,
      @NonNull BottomNavigationView bottomNav, @NonNull TextView chatTitle,
      @NonNull LinearLayout dmInputContainer, @NonNull EditText dmMessageInput,
      @NonNull RecyclerView dmRecyclerView, @NonNull LinearLayout dmTopBar,
      @NonNull ImageButton sendDMButton) {
    this.rootView = rootView;
    this.addToContactsButton = addToContactsButton;
    this.backgroundView = backgroundView;
    this.bottomNav = bottomNav;
    this.chatTitle = chatTitle;
    this.dmInputContainer = dmInputContainer;
    this.dmMessageInput = dmMessageInput;
    this.dmRecyclerView = dmRecyclerView;
    this.dmTopBar = dmTopBar;
    this.sendDMButton = sendDMButton;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDmChatBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDmChatBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_dm_chat, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDmChatBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addToContactsButton;
      ImageButton addToContactsButton = ViewBindings.findChildViewById(rootView, id);
      if (addToContactsButton == null) {
        break missingId;
      }

      id = R.id.backgroundView;
      ThreeDBackgroundView backgroundView = ViewBindings.findChildViewById(rootView, id);
      if (backgroundView == null) {
        break missingId;
      }

      id = R.id.bottomNav;
      BottomNavigationView bottomNav = ViewBindings.findChildViewById(rootView, id);
      if (bottomNav == null) {
        break missingId;
      }

      id = R.id.chatTitle;
      TextView chatTitle = ViewBindings.findChildViewById(rootView, id);
      if (chatTitle == null) {
        break missingId;
      }

      id = R.id.dmInputContainer;
      LinearLayout dmInputContainer = ViewBindings.findChildViewById(rootView, id);
      if (dmInputContainer == null) {
        break missingId;
      }

      id = R.id.dmMessageInput;
      EditText dmMessageInput = ViewBindings.findChildViewById(rootView, id);
      if (dmMessageInput == null) {
        break missingId;
      }

      id = R.id.dmRecyclerView;
      RecyclerView dmRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (dmRecyclerView == null) {
        break missingId;
      }

      id = R.id.dmTopBar;
      LinearLayout dmTopBar = ViewBindings.findChildViewById(rootView, id);
      if (dmTopBar == null) {
        break missingId;
      }

      id = R.id.sendDMButton;
      ImageButton sendDMButton = ViewBindings.findChildViewById(rootView, id);
      if (sendDMButton == null) {
        break missingId;
      }

      return new ActivityDmChatBinding((CoordinatorLayout) rootView, addToContactsButton,
          backgroundView, bottomNav, chatTitle, dmInputContainer, dmMessageInput, dmRecyclerView,
          dmTopBar, sendDMButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
