<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.GhostEncryptedChat"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="31">

        <!-- FileProvider for APK installation -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- ✅ Splash launches the app -->
        <activity
            android:name=".SplashActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- ✅ Your other activities (each listed once) -->
        <activity android:name=".EncryptedChatActivity" />
        <activity android:name=".DirectMessageActivity" android:exported="true" />
        <activity android:name=".GroupChatActivity" />
        <activity android:name=".DMRequestActivity" android:exported="true" />
        <activity android:name=".MainActivity" />
        <activity android:name=".ContactsActivity" android:exported="true" />
        <activity android:name=".SettingsActivity" />
        <activity android:name=".UsernameSetupActivity" />
        <activity
            android:name=".DMChatActivity"
            android:exported="false" />

    </application>
</manifest>
