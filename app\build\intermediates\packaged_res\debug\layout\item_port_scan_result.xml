<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="16dp"
    android:background="?android:attr/selectableItemBackground">

    <!-- Service Icon -->
    <TextView
        android:id="@+id/serviceIcon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/circle_background"
        android:backgroundTint="@color/accent_color"
        android:gravity="center"
        android:text="🔌"
        android:textSize="20sp" />

    <!-- Port Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/portNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="80"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:fontFamily="monospace"
                android:minWidth="60dp" />

            <TextView
                android:id="@+id/serviceName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="12dp"
                android:text="HTTP"
                android:textSize="16sp"
                android:textColor="@color/text_primary" />

            <TextView
                android:id="@+id/portStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="OPEN"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/accent_color" />

        </LinearLayout>

        <TextView
            android:id="@+id/responseTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="25ms"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:fontFamily="monospace"
            android:visibility="visible" />

    </LinearLayout>

</LinearLayout>
