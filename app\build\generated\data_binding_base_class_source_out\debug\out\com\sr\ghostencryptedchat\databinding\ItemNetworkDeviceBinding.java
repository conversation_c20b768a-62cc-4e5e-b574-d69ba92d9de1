// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemNetworkDeviceBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView deviceHostname;

  @NonNull
  public final TextView deviceIcon;

  @NonNull
  public final TextView deviceIp;

  @NonNull
  public final TextView deviceStatus;

  @NonNull
  public final TextView responseTime;

  private ItemNetworkDeviceBinding(@NonNull LinearLayout rootView, @NonNull TextView deviceHostname,
      @NonNull TextView deviceIcon, @NonNull TextView deviceIp, @NonNull TextView deviceStatus,
      @NonNull TextView responseTime) {
    this.rootView = rootView;
    this.deviceHostname = deviceHostname;
    this.deviceIcon = deviceIcon;
    this.deviceIp = deviceIp;
    this.deviceStatus = deviceStatus;
    this.responseTime = responseTime;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemNetworkDeviceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemNetworkDeviceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_network_device, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemNetworkDeviceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.deviceHostname;
      TextView deviceHostname = ViewBindings.findChildViewById(rootView, id);
      if (deviceHostname == null) {
        break missingId;
      }

      id = R.id.deviceIcon;
      TextView deviceIcon = ViewBindings.findChildViewById(rootView, id);
      if (deviceIcon == null) {
        break missingId;
      }

      id = R.id.deviceIp;
      TextView deviceIp = ViewBindings.findChildViewById(rootView, id);
      if (deviceIp == null) {
        break missingId;
      }

      id = R.id.deviceStatus;
      TextView deviceStatus = ViewBindings.findChildViewById(rootView, id);
      if (deviceStatus == null) {
        break missingId;
      }

      id = R.id.responseTime;
      TextView responseTime = ViewBindings.findChildViewById(rootView, id);
      if (responseTime == null) {
        break missingId;
      }

      return new ItemNetworkDeviceBinding((LinearLayout) rootView, deviceHostname, deviceIcon,
          deviceIp, deviceStatus, responseTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
