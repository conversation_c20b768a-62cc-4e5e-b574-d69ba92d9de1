<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/contactsLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#121212"
    android:padding="16dp">

    <TextView
        android:id="@+id/contactsHeader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Contacts"
        android:textColor="#00FF00"
        android:textSize="20sp"
        android:textStyle="bold"
        android:paddingBottom="12dp" />

    <!-- 🔍 Search Input -->
    <EditText
        android:id="@+id/contactSearchInput"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/search_box_background"
        android:hint="Search contacts..."
        android:padding="12dp"
        android:textColor="#FFFFFF"
        android:textColorHint="#888888"
        android:textSize="14sp"
        android:layout_marginBottom="12dp"
        />

    <!-- 📋 Recycler for contacts and requests -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/contactsRecycler"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:overScrollMode="ifContentScrolls"
        android:paddingBottom="8dp" />

</LinearLayout>
