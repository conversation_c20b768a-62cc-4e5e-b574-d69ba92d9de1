package com.sr.ghostencryptedchat.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.sr.ghostencryptedchat.R

class NetworkScannerFragment : Fragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = TextView(requireContext())
        view.text = "Network Scanner - Coming Soon!"
        view.textSize = 24f
        view.setTextColor(requireContext().getColor(R.color.text_primary))
        view.setPadding(48, 48, 48, 48)
        return view
    }
}
