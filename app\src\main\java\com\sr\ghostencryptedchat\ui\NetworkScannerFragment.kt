package com.sr.ghostencryptedchat.ui

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.sr.ghostencryptedchat.databinding.FragmentNetworkScannerBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.net.InetAddress
import java.net.NetworkInterface
import java.util.Collections

class NetworkScannerFragment : Fragment() {

    private var _binding: FragmentNetworkScannerBinding? = null
    private val binding get() = _binding!!

    private lateinit var deviceAdapter: NetworkDeviceAdapter
    private val deviceList = mutableListOf<NetworkDevice>()
    private var isScanning = false

    companion object {
        private const val PERMISSION_REQUEST_CODE = 1001
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentNetworkScannerBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupUI()
        checkPermissions()
    }

    private fun setupUI() {
        deviceAdapter = NetworkDeviceAdapter(deviceList)

        binding.devicesRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = deviceAdapter
        }

        binding.scanButton.setOnClickListener {
            if (!isScanning) {
                startNetworkScan()
            }
        }

        binding.backButton.setOnClickListener {
            parentFragmentManager.popBackStack()
        }

        // Show current network info
        displayNetworkInfo()
    }

    private fun checkPermissions() {
        val permissions = arrayOf(
            Manifest.permission.ACCESS_WIFI_STATE,
            Manifest.permission.ACCESS_NETWORK_STATE
        )

        val missingPermissions = permissions.filter {
            ContextCompat.checkSelfPermission(requireContext(), it) != PackageManager.PERMISSION_GRANTED
        }

        if (missingPermissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(
                requireActivity(),
                missingPermissions.toTypedArray(),
                PERMISSION_REQUEST_CODE
            )
        }
    }

    private fun displayNetworkInfo() {
        try {
            val wifiManager = requireContext().applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val connectivityManager = requireContext().getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

            val network = connectivityManager.activeNetwork
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network)

            val networkInfo = StringBuilder()

            // Get local IP
            val localIP = getLocalIPAddress()
            if (localIP != null) {
                networkInfo.append("Local IP: $localIP\n")
            }

            // Network type
            networkCapabilities?.let { caps ->
                when {
                    caps.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
                        networkInfo.append("Connection: WiFi\n")

                        // Get WiFi info if available
                        if (ActivityCompat.checkSelfPermission(
                                requireContext(),
                                Manifest.permission.ACCESS_WIFI_STATE
                            ) == PackageManager.PERMISSION_GRANTED
                        ) {
                            val wifiInfo = wifiManager.connectionInfo
                            networkInfo.append("SSID: ${wifiInfo.ssid}\n")
                        } else {
                            networkInfo.append("SSID: Permission required\n")
                        }
                    }
                    caps.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                        networkInfo.append("Connection: Mobile Data\n")
                    }
                    caps.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> {
                        networkInfo.append("Connection: Ethernet\n")
                    }
                    else -> {
                        networkInfo.append("Connection: Unknown\n")
                    }
                }
            }

            binding.networkInfo.text = networkInfo.toString().trim()

        } catch (e: Exception) {
            binding.networkInfo.text = "Unable to get network information"
        }
    }

    private fun getLocalIPAddress(): String? {
        try {
            val interfaces = Collections.list(NetworkInterface.getNetworkInterfaces())
            for (networkInterface in interfaces) {
                val addresses = Collections.list(networkInterface.inetAddresses)
                for (address in addresses) {
                    if (!address.isLoopbackAddress && address is java.net.Inet4Address) {
                        return address.hostAddress
                    }
                }
            }
        } catch (e: Exception) {
            // Ignore
        }
        return null
    }

    private fun startNetworkScan() {
        if (isScanning) return

        val localIP = getLocalIPAddress()
        if (localIP == null) {
            Toast.makeText(requireContext(), "Unable to determine local IP address", Toast.LENGTH_SHORT).show()
            return
        }

        isScanning = true
        binding.scanButton.text = "Scanning..."
        binding.scanButton.isEnabled = false
        binding.progressBar.visibility = View.VISIBLE

        deviceList.clear()
        deviceAdapter.notifyDataSetChanged()

        lifecycleScope.launch {
            try {
                scanNetwork(localIP)
            } finally {
                withContext(Dispatchers.Main) {
                    isScanning = false
                    binding.scanButton.text = "Scan Network"
                    binding.scanButton.isEnabled = true
                    binding.progressBar.visibility = View.GONE
                }
            }
        }
    }

    private suspend fun scanNetwork(localIP: String) {
        val subnet = localIP.substring(0, localIP.lastIndexOf('.'))

        withContext(Dispatchers.IO) {
            // Add local device first
            withContext(Dispatchers.Main) {
                deviceList.add(
                    NetworkDevice(
                        ip = localIP,
                        hostname = "This Device",
                        isReachable = true,
                        responseTime = 0
                    )
                )
                deviceAdapter.notifyItemInserted(deviceList.size - 1)
            }

            // Scan common IP ranges
            for (i in 1..254) {
                val targetIP = "$subnet.$i"
                if (targetIP == localIP) continue

                try {
                    val address = InetAddress.getByName(targetIP)
                    val startTime = System.currentTimeMillis()
                    val isReachable = address.isReachable(1000) // 1 second timeout
                    val responseTime = System.currentTimeMillis() - startTime

                    if (isReachable) {
                        val hostname = try {
                            address.canonicalHostName
                        } catch (e: Exception) {
                            "Unknown Device"
                        }

                        withContext(Dispatchers.Main) {
                            deviceList.add(
                                NetworkDevice(
                                    ip = targetIP,
                                    hostname = if (hostname != targetIP) hostname else "Unknown Device",
                                    isReachable = true,
                                    responseTime = responseTime
                                )
                            )
                            deviceAdapter.notifyItemInserted(deviceList.size - 1)
                        }
                    }
                } catch (e: Exception) {
                    // Device not reachable or error occurred
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

data class NetworkDevice(
    val ip: String,
    val hostname: String,
    val isReachable: Boolean,
    val responseTime: Long
)
