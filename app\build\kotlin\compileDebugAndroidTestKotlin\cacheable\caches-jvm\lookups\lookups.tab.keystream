  getTARGETContext android.app.Instrumentation  getTargetContext android.app.Instrumentation  setTargetContext android.app.Instrumentation  
targetContext android.app.Instrumentation  Context android.content  getPACKAGEName android.content.Context  getPackageName android.content.Context  packageName android.content.Context  setPackageName android.content.Context  
AndroidJUnit4 androidx.test.ext.junit.runners  InstrumentationRegistry androidx.test.platform.app  getInstrumentation 2androidx.test.platform.app.InstrumentationRegistry  
AndroidJUnit4 com.sr.ghostencryptedchat  ExampleInstrumentedTest com.sr.ghostencryptedchat  InstrumentationRegistry com.sr.ghostencryptedchat  assertEquals com.sr.ghostencryptedchat  InstrumentationRegistry 1com.sr.ghostencryptedchat.ExampleInstrumentedTest  Test 1com.sr.ghostencryptedchat.ExampleInstrumentedTest  assertEquals 1com.sr.ghostencryptedchat.ExampleInstrumentedTest  getASSERTEquals 1com.sr.ghostencryptedchat.ExampleInstrumentedTest  getAssertEquals 1com.sr.ghostencryptedchat.ExampleInstrumentedTest  
AndroidJUnit4 	java.lang  InstrumentationRegistry 	java.lang  assertEquals 	java.lang  
AndroidJUnit4 kotlin  InstrumentationRegistry kotlin  String kotlin  assertEquals kotlin  
AndroidJUnit4 kotlin.annotation  InstrumentationRegistry kotlin.annotation  assertEquals kotlin.annotation  
AndroidJUnit4 kotlin.collections  InstrumentationRegistry kotlin.collections  assertEquals kotlin.collections  
AndroidJUnit4 kotlin.comparisons  InstrumentationRegistry kotlin.comparisons  assertEquals kotlin.comparisons  
AndroidJUnit4 	kotlin.io  InstrumentationRegistry 	kotlin.io  assertEquals 	kotlin.io  
AndroidJUnit4 
kotlin.jvm  InstrumentationRegistry 
kotlin.jvm  assertEquals 
kotlin.jvm  
AndroidJUnit4 
kotlin.ranges  InstrumentationRegistry 
kotlin.ranges  assertEquals 
kotlin.ranges  KClass kotlin.reflect  
AndroidJUnit4 kotlin.sequences  InstrumentationRegistry kotlin.sequences  assertEquals kotlin.sequences  
AndroidJUnit4 kotlin.text  InstrumentationRegistry kotlin.text  assertEquals kotlin.text  Assert 	org.junit  Test 	org.junit  
AndroidJUnit4 org.junit.Assert  InstrumentationRegistry org.junit.Assert  assertEquals org.junit.Assert  RunWith org.junit.runner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   