package com.sr.ghostencryptedchat

import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.firebase.firestore.FirebaseFirestore
import com.sr.ghostencryptedchat.databinding.ActivityDmRequestBinding
import com.sr.ghostencryptedchat.model.DMRequest
import com.sr.ghostencryptedchat.util.KeyStoreUtil
import com.sr.ghostencryptedchat.adapter.DMRequestAdapter

class DMRequestActivity : AppCompatActivity() {

    private lateinit var binding: ActivityDmRequestBinding
    private lateinit var db: FirebaseFirestore
    private lateinit var username: String
    private val requests = mutableListOf<DMRequest>()
    private lateinit var adapter: DMRequestAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDmRequestBinding.inflate(layoutInflater)
        setContentView(binding.root)

        db = FirebaseFirestore.getInstance()
        username = KeyStoreUtil.getOrCreateUsername(this)

        adapter = DMRequestAdapter(requests,
            onAccept = { request -> handleAccept(request) },
            onReject = { request -> handleReject(request) }
        )

        binding.dmRequestList.layoutManager = LinearLayoutManager(this)
        binding.dmRequestList.adapter = adapter

        loadDMRequests()
    }

    private fun loadDMRequests() {
        db.collection("dmRequests")
            .whereEqualTo("to", username)
            .addSnapshotListener { snapshot, _ ->
                if (snapshot != null) {
                    requests.clear()
                    requests.addAll(snapshot.documents.mapNotNull {
                        it.toObject(DMRequest::class.java)?.apply { id = it.id }
                    })
                    adapter.notifyDataSetChanged()
                }
            }
    }

    private fun handleAccept(request: DMRequest) {
        val dmId = listOf(request.from, request.to).sorted().joinToString("_")
        val dmEntry = mapOf(
            "users" to listOf(request.from, request.to),
            "createdAt" to System.currentTimeMillis()
        )

        db.collection("dmThreads").document(dmId).set(dmEntry)
        db.collection("dmRequests").document(request.id).delete()
        Toast.makeText(this, "Accepted DM with ${request.from}", Toast.LENGTH_SHORT).show()
    }

    private fun handleReject(request: DMRequest) {
        db.collection("dmRequests").document(request.id).delete()
        Toast.makeText(this, "Rejected DM request", Toast.LENGTH_SHORT).show()
    }
}
