package com.sr.ghostencryptedchat.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.sr.ghostencryptedchat.R
import com.sr.ghostencryptedchat.databinding.ItemMessageBinding
import com.sr.ghostencryptedchat.model.ChatMessage
import com.sr.ghostencryptedchat.util.EncryptionUtil
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class PublicChatAdapter(
    private val messages: List<ChatMessage>,
    private val currentUsername: String
) : RecyclerView.Adapter<PublicChatAdapter.ChatViewHolder>() {

    class ChatViewHolder(val binding: ItemMessageBinding) :
        RecyclerView.ViewHolder(binding.root)

    // Map to store consistent colors for users
    private val userColors = mutableMapOf<String, Int>()
    
    // List of predefined vibrant colors for users
    private val colorList = listOf(
        "#FF5722", // Deep Orange
        "#E91E63", // Pink
        "#9C27B0", // Purple
        "#673AB7", // Deep Purple
        "#3F51B5", // Indigo
        "#2196F3", // Blue
        "#03A9F4", // Light Blue
        "#00BCD4", // Cyan
        "#009688", // Teal
        "#4CAF50", // Green
        "#8BC34A", // Light Green
        "#CDDC39", // Lime
        "#FFC107", // Amber
        "#FF9800"  // Orange
    )

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatViewHolder {
        val binding = ItemMessageBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ChatViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ChatViewHolder, position: Int) {
        val message = messages[position]
        val decrypted = EncryptionUtil.decrypt(message.message)
        val isCurrentUser = message.sender == currentUsername
        
        // Get or assign color for this user
        val userColor = getUserColor(message.sender)
        
        // Format timestamp
        val formattedTime = formatTimestamp(message.timestamp)
        
        // Set message text with sender name in their color
        if (isCurrentUser) {
            // Current user's messages
            holder.binding.messageText.text = decrypted
            holder.binding.senderName.text = "You"
            holder.binding.senderName.setTextColor(Color.parseColor("#aeff00")) // Neon green for current user
            holder.binding.messageContainer.setBackgroundResource(R.drawable.bg_message_me)
            holder.binding.messageContainer.layoutParams = (holder.binding.messageContainer.layoutParams as ViewGroup.MarginLayoutParams).apply {
                marginStart = 80
                marginEnd = 16
            }
            holder.binding.messageText.setTextColor(Color.WHITE)
        } else {
            // Other users' messages
            holder.binding.messageText.text = decrypted
            holder.binding.senderName.text = message.sender
            holder.binding.senderName.setTextColor(userColor)
            holder.binding.messageContainer.setBackgroundResource(R.drawable.bg_message_other)
            holder.binding.messageContainer.layoutParams = (holder.binding.messageContainer.layoutParams as ViewGroup.MarginLayoutParams).apply {
                marginStart = 16
                marginEnd = 80
            }
            holder.binding.messageText.setTextColor(Color.BLACK)
        }
        
        // Set timestamp
        holder.binding.timestamp.text = formattedTime
        
        // Make sure all views are visible
        holder.binding.senderName.visibility = View.VISIBLE
        holder.binding.timestamp.visibility = View.VISIBLE
    }

    private fun getUserColor(username: String): Int {
        // Return existing color or create a new one
        if (!userColors.containsKey(username)) {
            val colorIndex = userColors.size % colorList.size
            userColors[username] = Color.parseColor(colorList[colorIndex])
        }
        return userColors[username]!!
    }
    
    private fun formatTimestamp(timestamp: Long): String {
        val sdf = SimpleDateFormat("h:mm a", Locale.getDefault())
        return sdf.format(Date(timestamp))
    }

    override fun getItemCount() = messages.size
}
