<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#121212">

    <!-- 3D Background -->
    <com.sr.ghostencryptedchat.view.ThreeDBackgroundView
        android:id="@+id/backgroundView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- Main content area with increased bottom margin -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="64dp"
        android:background="#88121212">

        <!-- DM Top Bar -->
        <LinearLayout
            android:id="@+id/dmTopBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            android:gravity="center_vertical"
            android:background="#1F1F1F"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/chatTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="DM with ..."
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold" />

            <ImageButton
                android:id="@+id/addToContactsButton"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_add_contact"
                android:background="@android:color/transparent"
                android:contentDescription="Add to Contacts"
                android:tint="#00FF00"
                android:padding="6dp" />
        </LinearLayout>

        <!-- RecyclerView for messages with bottom margin to avoid overlap -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/dmRecyclerView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipToPadding="false"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            app:layout_constraintTop_toBottomOf="@id/dmTopBar"
            app:layout_constraintBottom_toTopOf="@id/dmInputContainer"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginBottom="8dp" />

        <!-- Input box with bottom margin to ensure visibility -->
        <LinearLayout
            android:id="@+id/dmInputContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp"
            android:gravity="center_vertical"
            android:background="#121212"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginBottom="8dp">

            <EditText
                android:id="@+id/dmMessageInput"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="Type here..."
                android:background="@drawable/bg_input_border"
                android:textColor="#FFFFFF"
                android:textColorHint="#AAAAAA"
                android:padding="12dp"
                android:minHeight="48dp"
                android:maxLines="5"
                android:inputType="textMultiLine"
                android:scrollHorizontally="false" />

            <ImageButton
                android:id="@+id/sendDMButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_send"
                android:contentDescription="Send"
                android:background="@android:color/transparent"
                android:tint="#00FF00"
                android:padding="12dp"
                android:layout_marginStart="8dp" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Bottom Navigation View fixed at bottom -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottomNav"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="#1C1C1C"
        app:itemIconTint="@color/nav_icon_selector"
        app:itemTextColor="@color/nav_text_selector"
        app:menu="@menu/bottom_nav_menu" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>



