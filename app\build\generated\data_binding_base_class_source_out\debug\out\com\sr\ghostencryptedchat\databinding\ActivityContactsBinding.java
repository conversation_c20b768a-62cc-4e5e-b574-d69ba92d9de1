// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityContactsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ListView contactList;

  @NonNull
  public final LinearLayout contactsLayout;

  @NonNull
  public final TextView contactsTitle;

  @NonNull
  public final EditText searchInput;

  private ActivityContactsBinding(@NonNull LinearLayout rootView, @NonNull ListView contactList,
      @NonNull LinearLayout contactsLayout, @NonNull TextView contactsTitle,
      @NonNull EditText searchInput) {
    this.rootView = rootView;
    this.contactList = contactList;
    this.contactsLayout = contactsLayout;
    this.contactsTitle = contactsTitle;
    this.searchInput = searchInput;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityContactsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityContactsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_contacts, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityContactsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.contactList;
      ListView contactList = ViewBindings.findChildViewById(rootView, id);
      if (contactList == null) {
        break missingId;
      }

      LinearLayout contactsLayout = (LinearLayout) rootView;

      id = R.id.contactsTitle;
      TextView contactsTitle = ViewBindings.findChildViewById(rootView, id);
      if (contactsTitle == null) {
        break missingId;
      }

      id = R.id.search_input;
      EditText searchInput = ViewBindings.findChildViewById(rootView, id);
      if (searchInput == null) {
        break missingId;
      }

      return new ActivityContactsBinding((LinearLayout) rootView, contactList, contactsLayout,
          contactsTitle, searchInput);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
