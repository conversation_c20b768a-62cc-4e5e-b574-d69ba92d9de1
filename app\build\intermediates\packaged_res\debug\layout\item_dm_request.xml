<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="10dp"
    app:cardBackgroundColor="#1E1E1E">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <TextView
            android:id="@+id/requestText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="DM request"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <Button
                android:id="@+id/acceptButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Accept"
                android:textColor="#FFFFFF"
                android:backgroundTint="#00C853" />

            <Button
                android:id="@+id/rejectButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Reject"
                android:textColor="#FFFFFF"
                android:backgroundTint="#D32F2F"
                android:layout_marginStart="8dp" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
