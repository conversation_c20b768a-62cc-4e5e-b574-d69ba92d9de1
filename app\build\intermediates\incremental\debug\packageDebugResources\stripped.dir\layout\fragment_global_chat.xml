<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/globalChatLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#121212">

    <TextView
        android:id="@+id/chatTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="World Chat"
        android:textColor="#FFFFFF"
        android:textSize="20sp"
        android:textStyle="bold"
        android:paddingBottom="8dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipToPadding="false"
        android:overScrollMode="ifContentScrolls"
        android:paddingBottom="8dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <EditText
            android:id="@+id/messageInput"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="Type here..."
            android:background="@drawable/bg_input_border"
            android:textColor="#FFFFFF"
            android:textColorHint="#AAAAAA"
            android:padding="12dp"
            android:minHeight="48dp"
            android:maxLines="5"
            android:inputType="textMultiLine"
            android:scrollHorizontally="false" />

        <ImageButton
            android:id="@+id/sendButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_send"
            android:contentDescription="Send"
            android:background="@android:color/transparent"
            android:tint="#aeff00"
            android:padding="12dp"
            android:layout_marginStart="8dp" />
    </LinearLayout>
</LinearLayout>

