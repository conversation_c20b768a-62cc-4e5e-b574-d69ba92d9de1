// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemToolBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView comingSoonLabel;

  @NonNull
  public final TextView toolDescription;

  @NonNull
  public final TextView toolIcon;

  @NonNull
  public final TextView toolTitle;

  private ItemToolBinding(@NonNull CardView rootView, @NonNull TextView comingSoonLabel,
      @NonNull TextView toolDescription, @NonNull TextView toolIcon, @NonNull TextView toolTitle) {
    this.rootView = rootView;
    this.comingSoonLabel = comingSoonLabel;
    this.toolDescription = toolDescription;
    this.toolIcon = toolIcon;
    this.toolTitle = toolTitle;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemToolBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemToolBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_tool, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemToolBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.comingSoonLabel;
      TextView comingSoonLabel = ViewBindings.findChildViewById(rootView, id);
      if (comingSoonLabel == null) {
        break missingId;
      }

      id = R.id.toolDescription;
      TextView toolDescription = ViewBindings.findChildViewById(rootView, id);
      if (toolDescription == null) {
        break missingId;
      }

      id = R.id.toolIcon;
      TextView toolIcon = ViewBindings.findChildViewById(rootView, id);
      if (toolIcon == null) {
        break missingId;
      }

      id = R.id.toolTitle;
      TextView toolTitle = ViewBindings.findChildViewById(rootView, id);
      if (toolTitle == null) {
        break missingId;
      }

      return new ItemToolBinding((CardView) rootView, comingSoonLabel, toolDescription, toolIcon,
          toolTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
