// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDmRequestBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final Button acceptButton;

  @NonNull
  public final Button rejectButton;

  @NonNull
  public final TextView requestText;

  private ItemDmRequestBinding(@NonNull CardView rootView, @NonNull Button acceptButton,
      @NonNull Button rejectButton, @NonNull TextView requestText) {
    this.rootView = rootView;
    this.acceptButton = acceptButton;
    this.rejectButton = rejectButton;
    this.requestText = requestText;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDmRequestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDmRequestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_dm_request, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDmRequestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.acceptButton;
      Button acceptButton = ViewBindings.findChildViewById(rootView, id);
      if (acceptButton == null) {
        break missingId;
      }

      id = R.id.rejectButton;
      Button rejectButton = ViewBindings.findChildViewById(rootView, id);
      if (rejectButton == null) {
        break missingId;
      }

      id = R.id.requestText;
      TextView requestText = ViewBindings.findChildViewById(rootView, id);
      if (requestText == null) {
        break missingId;
      }

      return new ItemDmRequestBinding((CardView) rootView, acceptButton, rejectButton, requestText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
