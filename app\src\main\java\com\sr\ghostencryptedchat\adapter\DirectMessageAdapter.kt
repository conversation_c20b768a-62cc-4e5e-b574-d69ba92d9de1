package com.sr.ghostencryptedchat.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.sr.ghostencryptedchat.databinding.ItemDirectMessageBinding
import com.sr.ghostencryptedchat.model.DirectMessage

class DirectMessageAdapter(
    private val onItemClick: (DirectMessage) -> Unit
) : ListAdapter<DirectMessage, DirectMessageAdapter.ViewHolder>(DirectMessageDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemDirectMessageBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ViewHolder(
        private val binding: ItemDirectMessageBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onItemClick(getItem(position))
                }
            }
        }

        fun bind(message: DirectMessage) {
            binding.apply {
                recipientName.text = message.recipientName
                lastMessage.text = message.lastMessage
                // Force layout update for newIndicator
                newIndicator.visibility = android.view.View.GONE
                newIndicator.visibility = if (message.isNew) android.view.View.VISIBLE else android.view.View.GONE
                // Request layout update
                newIndicator.requestLayout()
            }
        }
    }

    private class DirectMessageDiffCallback : DiffUtil.ItemCallback<DirectMessage>() {
        override fun areItemsTheSame(oldItem: DirectMessage, newItem: DirectMessage): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: DirectMessage, newItem: DirectMessage): Boolean {
            // Compare all fields including isNew
            return oldItem.id == newItem.id &&
                   oldItem.participants == newItem.participants &&
                   oldItem.lastMessage == newItem.lastMessage &&
                   oldItem.lastMessageTime == newItem.lastMessageTime &&
                   oldItem.recipientId == newItem.recipientId &&
                   oldItem.recipientName == newItem.recipientName &&
                   oldItem.isNew == newItem.isNew
        }
    }
} 