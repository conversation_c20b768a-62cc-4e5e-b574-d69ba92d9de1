// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPortScanResultBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView portNumber;

  @NonNull
  public final TextView portStatus;

  @NonNull
  public final TextView responseTime;

  @NonNull
  public final TextView serviceIcon;

  @NonNull
  public final TextView serviceName;

  private ItemPortScanResultBinding(@NonNull LinearLayout rootView, @NonNull TextView portNumber,
      @NonNull TextView portStatus, @NonNull TextView responseTime, @NonNull TextView serviceIcon,
      @NonNull TextView serviceName) {
    this.rootView = rootView;
    this.portNumber = portNumber;
    this.portStatus = portStatus;
    this.responseTime = responseTime;
    this.serviceIcon = serviceIcon;
    this.serviceName = serviceName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPortScanResultBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPortScanResultBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_port_scan_result, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPortScanResultBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.portNumber;
      TextView portNumber = ViewBindings.findChildViewById(rootView, id);
      if (portNumber == null) {
        break missingId;
      }

      id = R.id.portStatus;
      TextView portStatus = ViewBindings.findChildViewById(rootView, id);
      if (portStatus == null) {
        break missingId;
      }

      id = R.id.responseTime;
      TextView responseTime = ViewBindings.findChildViewById(rootView, id);
      if (responseTime == null) {
        break missingId;
      }

      id = R.id.serviceIcon;
      TextView serviceIcon = ViewBindings.findChildViewById(rootView, id);
      if (serviceIcon == null) {
        break missingId;
      }

      id = R.id.serviceName;
      TextView serviceName = ViewBindings.findChildViewById(rootView, id);
      if (serviceName == null) {
        break missingId;
      }

      return new ItemPortScanResultBinding((LinearLayout) rootView, portNumber, portStatus,
          responseTime, serviceIcon, serviceName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
