// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentContactsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final EditText contactSearchInput;

  @NonNull
  public final TextView contactsHeader;

  @NonNull
  public final LinearLayout contactsLayout;

  @NonNull
  public final RecyclerView contactsRecycler;

  private FragmentContactsBinding(@NonNull LinearLayout rootView,
      @NonNull EditText contactSearchInput, @NonNull TextView contactsHeader,
      @NonNull LinearLayout contactsLayout, @NonNull RecyclerView contactsRecycler) {
    this.rootView = rootView;
    this.contactSearchInput = contactSearchInput;
    this.contactsHeader = contactsHeader;
    this.contactsLayout = contactsLayout;
    this.contactsRecycler = contactsRecycler;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentContactsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentContactsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_contacts, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentContactsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.contactSearchInput;
      EditText contactSearchInput = ViewBindings.findChildViewById(rootView, id);
      if (contactSearchInput == null) {
        break missingId;
      }

      id = R.id.contactsHeader;
      TextView contactsHeader = ViewBindings.findChildViewById(rootView, id);
      if (contactsHeader == null) {
        break missingId;
      }

      LinearLayout contactsLayout = (LinearLayout) rootView;

      id = R.id.contactsRecycler;
      RecyclerView contactsRecycler = ViewBindings.findChildViewById(rootView, id);
      if (contactsRecycler == null) {
        break missingId;
      }

      return new FragmentContactsBinding((LinearLayout) rootView, contactSearchInput,
          contactsHeader, contactsLayout, contactsRecycler);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
