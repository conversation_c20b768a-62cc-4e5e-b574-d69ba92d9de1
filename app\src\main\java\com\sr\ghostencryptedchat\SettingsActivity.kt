package com.sr.ghostencryptedchat

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.sr.ghostencryptedchat.util.BackupUtil
import com.sr.ghostencryptedchat.util.KeyStoreUtil
import kotlinx.coroutines.launch

class SettingsActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings)

        // Set up backup button
        findViewById<android.widget.Button>(R.id.backupButton).setOnClickListener {
            lifecycleScope.launch {
                try {
                    val seedPhrase = BackupUtil.createBackup(this@SettingsActivity)
                    showSeedPhraseDialog(seedPhrase)
                } catch (e: Exception) {
                    Toast.makeText(this@SettingsActivity, "Failed to create backup: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }

        // Set up restore button
        findViewById<android.widget.Button>(R.id.restoreButton).setOnClickListener {
            showRestoreDialog()
        }
    }

    private fun showSeedPhraseDialog(seedPhrase: String) {
        AlertDialog.Builder(this)
            .setTitle("Backup Created")
            .setMessage("Your seed phrase is:\n\n$seedPhrase\n\nWrite this down and keep it safe! You'll need it to recover your account.")
            .setPositiveButton("OK", null)
            .show()
    }

    private fun showRestoreDialog() {
        val input = android.widget.EditText(this)
        input.hint = "Enter your seed phrase"
        
        AlertDialog.Builder(this)
            .setTitle("Restore Account")
            .setView(input)
            .setPositiveButton("Restore") { _, _ ->
                val seedPhrase = input.text.toString().trim()
                if (seedPhrase.isNotEmpty()) {
                    lifecycleScope.launch {
                        try {
                            if (BackupUtil.restoreFromSeedPhrase(this@SettingsActivity, seedPhrase)) {
                                Toast.makeText(this@SettingsActivity, "Account restored successfully!", Toast.LENGTH_SHORT).show()
                                // Restart the app to apply changes
                                val intent = Intent(this@SettingsActivity, MainActivity::class.java)
                                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                                startActivity(intent)
                                finish()
                            } else {
                                Toast.makeText(this@SettingsActivity, "Invalid seed phrase", Toast.LENGTH_SHORT).show()
                            }
                        } catch (e: Exception) {
                            Toast.makeText(this@SettingsActivity, "Failed to restore account: ${e.message}", Toast.LENGTH_LONG).show()
                        }
                    }
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
}
