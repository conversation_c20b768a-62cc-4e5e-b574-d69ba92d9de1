{"logs": [{"outputFile": "com.sr.ghostencryptedchat.app-mergeDebugResources-41:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\20819e21ff7c6943837ea2e5c7b675ed\\transformed\\media3-ui-1.2.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,496,672,758,847,931,1023,1114,1190,1255,1344,1437,1508,1576,1637,1705,1860,2018,2172,2239,2321,2392,2472,2563,2657,2723,2788,2841,2899,2947,3008,3070,3146,3208,3272,3333,3394,3458,3523,3589,3641,3705,3783,3861", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,154,157,153,66,81,70,79,90,93,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57", "endOffsets": "280,491,667,753,842,926,1018,1109,1185,1250,1339,1432,1503,1571,1632,1700,1855,2013,2167,2234,2316,2387,2467,2558,2652,2718,2783,2836,2894,2942,3003,3065,3141,3203,3267,3328,3389,3453,3518,3584,3636,3700,3778,3856,3914"}, "to": {"startLines": "2,11,15,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,591,7418,7504,7593,7677,7769,7860,7936,8001,8090,8183,8254,8322,8383,8451,8606,8764,8918,8985,9067,9138,9218,9309,9403,9469,10195,10248,10306,10354,10415,10477,10553,10615,10679,10740,10801,10865,10930,10996,11048,11112,11190,11268", "endLines": "10,14,18,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,154,157,153,66,81,70,79,90,93,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57", "endOffsets": "375,586,762,7499,7588,7672,7764,7855,7931,7996,8085,8178,8249,8317,8378,8446,8601,8759,8913,8980,9062,9133,9213,9304,9398,9464,9529,10243,10301,10349,10410,10472,10548,10610,10674,10735,10796,10860,10925,10991,11043,11107,11185,11263,11321"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\d858642a26bc843a8ca821fcaa92e1a0\\transformed\\preference-1.2.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,265,345,483,652,737", "endColumns": "69,89,79,137,168,84,81", "endOffsets": "170,260,340,478,647,732,814"}, "to": {"startLines": "83,136,195,197,200,201,202", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7283,11326,16040,16204,16524,16693,16778", "endColumns": "69,89,79,137,168,84,81", "endOffsets": "7348,11411,16115,16337,16688,16773,16855"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\1936637f2b78fb49128b3efd39ec3928\\transformed\\material-1.10.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,500,587,672,770,889,974,1039,1137,1218,1277,1370,1432,1495,1553,1624,1686,1740,1861,1918,1979,2033,2104,2237,2321,2404,2537,2619,2697,2829,2919,2999,3053,3104,3170,3241,3319,3405,3484,3559,3637,3717,3800,3905,3993,4072,4162,4255,4329,4399,4490,4544,4624,4691,4775,4860,4922,4986,5049,5120,5224,5339,5436,5550,5608,5663", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,131,89,79,53,50,65,70,77,85,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83", "endOffsets": "260,339,416,495,582,667,765,884,969,1034,1132,1213,1272,1365,1427,1490,1548,1619,1681,1735,1856,1913,1974,2028,2099,2232,2316,2399,2532,2614,2692,2824,2914,2994,3048,3099,3165,3236,3314,3400,3479,3554,3632,3712,3795,3900,3988,4067,4157,4250,4324,4394,4485,4539,4619,4686,4770,4855,4917,4981,5044,5115,5219,5334,5431,5545,5603,5658,5742"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,84,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "767,3624,3703,3780,3859,3946,4766,4864,4983,7353,11416,11514,11595,11654,11747,11809,11872,11930,12001,12063,12117,12238,12295,12356,12410,12481,12614,12698,12781,12914,12996,13074,13206,13296,13376,13430,13481,13547,13618,13696,13782,13861,13936,14014,14094,14177,14282,14370,14449,14539,14632,14706,14776,14867,14921,15001,15068,15152,15237,15299,15363,15426,15497,15601,15716,15813,15927,15985,16120", "endLines": "22,50,51,52,53,54,62,63,64,84,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,196", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,131,89,79,53,50,65,70,77,85,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83", "endOffsets": "927,3698,3775,3854,3941,4026,4859,4978,5063,7413,11509,11590,11649,11742,11804,11867,11925,11996,12058,12112,12233,12290,12351,12405,12476,12609,12693,12776,12909,12991,13069,13201,13291,13371,13425,13476,13542,13613,13691,13777,13856,13931,14009,14089,14172,14277,14365,14444,14534,14627,14701,14771,14862,14916,14996,15063,15147,15232,15294,15358,15421,15492,15596,15711,15808,15922,15980,16035,16199"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\573c237f13283bc1d5f145ebb8306e38\\transformed\\media3-exoplayer-1.2.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,244,308,382,456,547,635", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "115,174,239,303,377,451,542,630,711"}, "to": {"startLines": "109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9534,9599,9658,9723,9787,9861,9935,10026,10114", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "9594,9653,9718,9782,9856,9930,10021,10109,10190"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\1100a1eb0e13467cdf6e3e9fa04c3895\\transformed\\core-1.10.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "55,56,57,58,59,60,61,199", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4031,4129,4231,4332,4430,4535,4647,16423", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "4124,4226,4327,4425,4530,4642,4761,16519"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\6142c6f2f2c5da410d9cf340d166ee81\\transformed\\play-services-base-18.0.1\\res\\values-mn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,452,580,683,816,938,1063,1169,1309,1412,1576,1701,1838,2002,2059,2117", "endColumns": "105,152,127,102,132,121,124,105,139,102,163,124,136,163,56,57,72", "endOffsets": "298,451,579,682,815,937,1062,1168,1308,1411,1575,1700,1837,2001,2058,2116,2189"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5068,5178,5335,5467,5574,5711,5837,5966,6226,6370,6477,6645,6774,6915,7083,7144,7206", "endColumns": "109,156,131,106,136,125,128,109,143,106,167,128,140,167,60,61,76", "endOffsets": "5173,5330,5462,5569,5706,5832,5961,6071,6365,6472,6640,6769,6910,7078,7139,7201,7278"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\f2dc38ad7cc09bc05989a176e11ace02\\transformed\\appcompat-1.6.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "932,1046,1146,1255,1341,1447,1561,1644,1725,1816,1909,2004,2100,2197,2290,2384,2476,2567,2657,2737,2844,2947,3044,3151,3253,3366,3525,16342", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "1041,1141,1250,1336,1442,1556,1639,1720,1811,1904,1999,2095,2192,2285,2379,2471,2562,2652,2732,2839,2942,3039,3146,3248,3361,3520,3619,16418"}}, {"source": "C:\\Users\\<USER>\\.jdks\\ms-17.0.15\\caches\\transforms-3\\ce7eb9a863475fa63014d727748770e8\\transformed\\play-services-basement-18.1.0\\res\\values-mn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6076", "endColumns": "149", "endOffsets": "6221"}}]}]}