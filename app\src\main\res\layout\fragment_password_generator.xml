<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <ImageButton
                android:id="@+id/backButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_arrow_back"
                android:tint="@color/text_primary"
                android:contentDescription="Back" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="16dp"
                android:text="Password Generator"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

        </LinearLayout>

        <!-- Generated Password Display -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardBackgroundColor="@color/background_secondary"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Generated Password"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/generatedPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Generating..."
                    android:textSize="18sp"
                    android:textColor="@color/accent_color"
                    android:background="@drawable/rounded_input_background"
                    android:padding="16dp"
                    android:fontFamily="monospace"
                    android:textIsSelectable="true"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Strength: "
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/strengthIndicator"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Strong"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/accent_color" />

                    <Button
                        android:id="@+id/copyButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Copy"
                        android:textColor="@color/background_primary"
                        android:backgroundTint="@color/accent_color"
                        style="@style/Widget.MaterialComponents.Button" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Password Options -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardBackgroundColor="@color/background_secondary"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Password Options"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <!-- Length Slider -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Length: "
                        android:textSize="14sp"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/lengthValue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="16"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/accent_color"
                        android:minWidth="32dp" />

                    <com.google.android.material.slider.Slider
                        android:id="@+id/lengthSlider"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:valueFrom="4"
                        android:valueTo="64"
                        android:stepSize="1"
                        android:value="16"
                        app:thumbColor="@color/accent_color"
                        app:trackColorActive="@color/accent_color"
                        app:trackColorInactive="@color/text_disabled" />

                </LinearLayout>

                <!-- Character Type Options -->
                <CheckBox
                    android:id="@+id/includeUppercase"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Include Uppercase (A-Z)"
                    android:textColor="@color/text_primary"
                    android:buttonTint="@color/accent_color"
                    android:layout_marginBottom="8dp" />

                <CheckBox
                    android:id="@+id/includeLowercase"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Include Lowercase (a-z)"
                    android:textColor="@color/text_primary"
                    android:buttonTint="@color/accent_color"
                    android:layout_marginBottom="8dp" />

                <CheckBox
                    android:id="@+id/includeNumbers"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Include Numbers (0-9)"
                    android:textColor="@color/text_primary"
                    android:buttonTint="@color/accent_color"
                    android:layout_marginBottom="8dp" />

                <CheckBox
                    android:id="@+id/includeSymbols"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Include Symbols (!@#$%^&*)"
                    android:textColor="@color/text_primary"
                    android:buttonTint="@color/accent_color"
                    android:layout_marginBottom="8dp" />

                <CheckBox
                    android:id="@+id/excludeSimilar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Exclude Similar Characters (0, O, l, I, 1)"
                    android:textColor="@color/text_primary"
                    android:buttonTint="@color/accent_color"
                    android:layout_marginBottom="16dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Generate Button -->
        <Button
            android:id="@+id/generateButton"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="Generate New Password"
            android:textColor="@color/background_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:backgroundTint="@color/accent_color"
            style="@style/Widget.MaterialComponents.Button" />

    </LinearLayout>

</ScrollView>
