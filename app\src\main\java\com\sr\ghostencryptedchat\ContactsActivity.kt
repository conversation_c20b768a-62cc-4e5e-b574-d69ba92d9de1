package com.sr.ghostencryptedchat

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.widget.EditText
import android.widget.ListView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.firebase.firestore.FirebaseFirestore
import com.sr.ghostencryptedchat.util.KeyStoreUtil

class ContactsActivity : AppCompatActivity() {

    private lateinit var db: FirebaseFirestore
    private lateinit var username: String
    private lateinit var listView: ListView
    private lateinit var adapter: android.widget.ArrayAdapter<String>
    private lateinit var searchInput: EditText
    private val users = mutableListOf<String>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_contacts)

        db = FirebaseFirestore.getInstance()
        username = KeyStoreUtil.getOrCreateUsername(this)

        listView = findViewById(R.id.contactList)
        searchInput = findViewById(R.id.search_input)
        adapter = android.widget.ArrayAdapter(this, android.R.layout.simple_list_item_1, users)
        listView.adapter = adapter

        searchInput.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {}

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val query = s.toString().trim().lowercase()
                if (query.isNotEmpty()) {
                    searchUserByUsername(query)
                } else {
                    users.clear()
                    adapter.notifyDataSetChanged()
                }
            }
        })
    }

    private fun searchUserByUsername(username: String) {
        val lowercaseQuery = username.lowercase()
        db.collection("users")
            .get()
            .addOnSuccessListener { result ->
                users.clear()
                val match = result.documents.firstOrNull { doc ->
                    val storedUsername = doc.getString("username") ?: ""
                    storedUsername.lowercase() == lowercaseQuery
                }?.getString("username")
                if (match != null) {
                    users.add(match)
                } else {
                    Toast.makeText(this, "User not found", Toast.LENGTH_SHORT).show()
                }
                adapter.notifyDataSetChanged()
            }
            .addOnFailureListener {
                Toast.makeText(this, "Error searching user", Toast.LENGTH_SHORT).show()
            }
    }
}
