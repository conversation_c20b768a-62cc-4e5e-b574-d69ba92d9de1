package com.sr.ghostencryptedchat.util

import android.app.AlertDialog
import android.content.Context
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.preference.PreferenceManager
import java.security.KeyFactory
import java.security.KeyPair
import java.security.KeyPairGenerator
import java.security.KeyStore
import java.security.PrivateKey
import java.security.PublicKey
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import java.util.*

object KeyStoreUtil {
    private const val KEYSTORE_PROVIDER = "AndroidKeyStore"
    private const val KEY_ALIAS = "GhostChatKey"
    private const val USERNAME_KEY = "username"

    fun getOrCreateUsername(context: Context): String {
        val prefs = PreferenceManager.getDefaultSharedPreferences(context)
        var username = prefs.getString(USERNAME_KEY, null)
        
        if (username == null) {
            // Generate a random username if none exists
            username = "user_${UUID.randomUUID().toString().substring(0, 8)}"
            prefs.edit().putString(USERNAME_KEY, username).apply()
        }
        
        return username
    }

    fun setUsername(context: Context, username: String) {
        val prefs = PreferenceManager.getDefaultSharedPreferences(context)
        prefs.edit().putString(USERNAME_KEY, username).apply()
    }

    fun promptUsernameChange(context: Context, onComplete: (() -> Unit)? = null) {
        val prefs = PreferenceManager.getDefaultSharedPreferences(context)
        val input = EditText(context)
        input.setText(getOrCreateUsername(context))

        AlertDialog.Builder(context)
            .setTitle("Change Username")
            .setView(input)
            .setPositiveButton("Save") { dialog, _ ->
                val newName = input.text.toString().trim()
                if (newName.isNotBlank()) {
                    prefs.edit().putString(USERNAME_KEY, newName).apply()
                    onComplete?.invoke()

                    // ✅ Hide keyboard after saving
                    val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                    imm.hideSoftInputFromWindow(input.windowToken, 0)
                }
                dialog.dismiss()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    fun getOrCreateKeyPair(context: Context): KeyPair {
        val keyStore = KeyStore.getInstance(KEYSTORE_PROVIDER)
        keyStore.load(null)

        if (!keyStore.containsAlias(KEY_ALIAS)) {
            val keyPairGenerator = KeyPairGenerator.getInstance(
                KeyProperties.KEY_ALGORITHM_RSA,
                KEYSTORE_PROVIDER
            )

            val keyGenParameterSpec = KeyGenParameterSpec.Builder(
                KEY_ALIAS,
                KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
            )
                .setBlockModes(KeyProperties.BLOCK_MODE_ECB)
                .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_RSA_PKCS1)
                .setKeySize(2048)
                .build()

            keyPairGenerator.initialize(keyGenParameterSpec)
            keyPairGenerator.generateKeyPair()
        }

        val privateKey = keyStore.getKey(KEY_ALIAS, null) as PrivateKey
        val publicKey = keyStore.getCertificate(KEY_ALIAS).publicKey

        return KeyPair(publicKey, privateKey)
    }

    fun getPublicKey(context: Context): PublicKey {
        return getOrCreateKeyPair(context).public
    }

    fun getPrivateKey(context: Context): PrivateKey {
        return getOrCreateKeyPair(context).private
    }
}
