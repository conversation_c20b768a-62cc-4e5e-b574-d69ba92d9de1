1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.sr.ghostencryptedchat"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="33" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:4:5-67
11-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:5:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:5:22-76
13    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
13-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:6:5-83
13-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:6:22-80
14    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
14-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:7:5-88
14-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:7:22-85
15    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
15-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:8:5-76
15-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:8:22-73
16
17    <permission
17-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
18        android:name="com.sr.ghostencryptedchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.sr.ghostencryptedchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
22
23    <application
23-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:10:5-58:19
24        android:allowBackup="true"
24-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:11:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:12:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:13:9-54
30        android:icon="@mipmap/ic_launcher"
30-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:14:9-43
31        android:label="@string/app_name"
31-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:15:9-41
32        android:networkSecurityConfig="@xml/network_security_config"
32-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:19:9-69
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:16:9-54
34        android:supportsRtl="true"
34-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:17:9-35
35        android:testOnly="true"
36        android:theme="@style/Theme.GhostEncryptedChat" >
36-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:18:9-56
37
38        <!-- FileProvider for APK installation -->
39        <provider
40            android:name="androidx.core.content.FileProvider"
40-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:24:13-62
41            android:authorities="com.sr.ghostencryptedchat.provider"
41-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:25:13-60
42            android:exported="false"
42-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:26:13-37
43            android:grantUriPermissions="true" >
43-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:27:13-47
44            <meta-data
44-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:28:13-30:54
45                android:name="android.support.FILE_PROVIDER_PATHS"
45-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:29:17-67
46                android:resource="@xml/file_paths" />
46-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:30:17-51
47        </provider>
48
49        <!-- ✅ Splash launches the app -->
50        <activity
50-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:34:9-41:20
51            android:name="com.sr.ghostencryptedchat.SplashActivity"
51-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:35:13-43
52            android:exported="true" >
52-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:36:13-36
53            <intent-filter>
53-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:37:13-40:29
54                <action android:name="android.intent.action.MAIN" />
54-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:38:17-69
54-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:38:25-66
55
56                <category android:name="android.intent.category.LAUNCHER" />
56-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:39:17-77
56-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:39:27-74
57            </intent-filter>
58        </activity>
59
60        <!-- ✅ Your other activities (each listed once) -->
61        <activity
61-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:44:9-46:46
62            android:name="com.sr.ghostencryptedchat.EncryptedChatActivity"
62-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:45:13-50
63            android:launchMode="singleTop" />
63-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:46:13-43
64        <activity
64-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:47:9-83
65            android:name="com.sr.ghostencryptedchat.DirectMessageActivity"
65-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:47:19-56
66            android:exported="true" />
66-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:47:57-80
67        <activity android:name="com.sr.ghostencryptedchat.GroupChatActivity" />
67-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:48:9-55
67-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:48:19-52
68        <activity
68-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:49:9-79
69            android:name="com.sr.ghostencryptedchat.DMRequestActivity"
69-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:49:19-52
70            android:exported="true" />
70-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:49:53-76
71        <activity android:name="com.sr.ghostencryptedchat.MainActivity" />
71-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:50:9-50
71-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:50:19-47
72        <activity
72-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:51:9-78
73            android:name="com.sr.ghostencryptedchat.ContactsActivity"
73-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:51:19-51
74            android:exported="true" />
74-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:51:52-75
75        <activity android:name="com.sr.ghostencryptedchat.SettingsActivity" />
75-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:52:9-54
75-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:52:19-51
76        <activity android:name="com.sr.ghostencryptedchat.UsernameSetupActivity" />
76-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:53:9-59
76-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:53:19-56
77        <activity
77-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:54:9-56:40
78            android:name="com.sr.ghostencryptedchat.DMChatActivity"
78-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:55:13-43
79            android:exported="false" />
79-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:56:13-37
80
81        <service
81-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:9:9-15:19
82            android:name="com.google.firebase.components.ComponentDiscoveryService"
82-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:10:13-84
83            android:directBootAware="true"
83-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
84            android:exported="false" >
84-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:11:13-37
85            <meta-data
85-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:12:13-14:85
86                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
86-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:13:17-129
87                android:value="com.google.firebase.components.ComponentRegistrar" />
87-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:14:17-82
88            <meta-data
88-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:17:13-19:85
89                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
89-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:18:17-122
90                android:value="com.google.firebase.components.ComponentRegistrar" />
90-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:19:17-82
91            <meta-data
91-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:20:13-22:85
92                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
92-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:21:17-111
93                android:value="com.google.firebase.components.ComponentRegistrar" />
93-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:22:17-82
94            <meta-data
94-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
95                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
95-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
97            <meta-data
97-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
98                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
98-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
100        </service>
101
102        <provider
102-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
103            android:name="com.google.firebase.provider.FirebaseInitProvider"
103-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
104            android:authorities="com.sr.ghostencryptedchat.firebaseinitprovider"
104-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
105            android:directBootAware="true"
105-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
106            android:exported="false"
106-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
107            android:initOrder="100" />
107-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
108
109        <uses-library
109-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
110            android:name="androidx.window.extensions"
110-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
111            android:required="false" />
111-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
112        <uses-library
112-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
113            android:name="androidx.window.sidecar"
113-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
114            android:required="false" />
114-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
115
116        <activity
116-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
117            android:name="com.google.android.gms.common.api.GoogleApiActivity"
117-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
118            android:exported="false"
118-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
119            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
119-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
120
121        <meta-data
121-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
122            android:name="com.google.android.gms.version"
122-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
123            android:value="@integer/google_play_services_version" />
123-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
124
125        <provider
125-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
126            android:name="androidx.startup.InitializationProvider"
126-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
127            android:authorities="com.sr.ghostencryptedchat.androidx-startup"
127-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
128            android:exported="false" >
128-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
129            <meta-data
129-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.emoji2.text.EmojiCompatInitializer"
130-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
131                android:value="androidx.startup" />
131-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
132            <meta-data
132-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
133                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
133-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
134                android:value="androidx.startup" />
134-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
135        </provider>
136    </application>
137
138</manifest>
