1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.sr.ghostencryptedchat"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="33" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:4:5-67
11-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:5:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:5:22-76
13    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
13-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:6:5-83
13-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:6:22-80
14    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
14-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:7:5-88
14-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:7:22-85
15    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
15-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:8:5-76
15-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:8:22-73
16
17    <permission
17-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
18        android:name="com.sr.ghostencryptedchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.sr.ghostencryptedchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
22
23    <application
23-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:10:5-58:19
24        android:allowBackup="true"
24-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:11:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:12:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:13:9-54
30        android:icon="@mipmap/ic_launcher"
30-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:14:9-43
31        android:label="@string/app_name"
31-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:15:9-41
32        android:networkSecurityConfig="@xml/network_security_config"
32-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:19:9-69
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:16:9-54
34        android:supportsRtl="true"
34-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:17:9-35
35        android:theme="@style/Theme.GhostEncryptedChat" >
35-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:18:9-56
36
37        <!-- FileProvider for APK installation -->
38        <provider
39            android:name="androidx.core.content.FileProvider"
39-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:24:13-62
40            android:authorities="com.sr.ghostencryptedchat.provider"
40-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:25:13-60
41            android:exported="false"
41-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:26:13-37
42            android:grantUriPermissions="true" >
42-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:27:13-47
43            <meta-data
43-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:28:13-30:54
44                android:name="android.support.FILE_PROVIDER_PATHS"
44-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:29:17-67
45                android:resource="@xml/file_paths" />
45-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:30:17-51
46        </provider>
47
48        <!-- ✅ Splash launches the app -->
49        <activity
49-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:34:9-41:20
50            android:name="com.sr.ghostencryptedchat.SplashActivity"
50-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:35:13-43
51            android:exported="true" >
51-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:36:13-36
52            <intent-filter>
52-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:37:13-40:29
53                <action android:name="android.intent.action.MAIN" />
53-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:38:17-69
53-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:38:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:39:17-77
55-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:39:27-74
56            </intent-filter>
57        </activity>
58
59        <!-- ✅ Your other activities (each listed once) -->
60        <activity
60-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:44:9-46:46
61            android:name="com.sr.ghostencryptedchat.EncryptedChatActivity"
61-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:45:13-50
62            android:launchMode="singleTop" />
62-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:46:13-43
63        <activity
63-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:47:9-83
64            android:name="com.sr.ghostencryptedchat.DirectMessageActivity"
64-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:47:19-56
65            android:exported="true" />
65-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:47:57-80
66        <activity android:name="com.sr.ghostencryptedchat.GroupChatActivity" />
66-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:48:9-55
66-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:48:19-52
67        <activity
67-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:49:9-79
68            android:name="com.sr.ghostencryptedchat.DMRequestActivity"
68-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:49:19-52
69            android:exported="true" />
69-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:49:53-76
70        <activity android:name="com.sr.ghostencryptedchat.MainActivity" />
70-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:50:9-50
70-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:50:19-47
71        <activity
71-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:51:9-78
72            android:name="com.sr.ghostencryptedchat.ContactsActivity"
72-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:51:19-51
73            android:exported="true" />
73-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:51:52-75
74        <activity android:name="com.sr.ghostencryptedchat.SettingsActivity" />
74-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:52:9-54
74-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:52:19-51
75        <activity android:name="com.sr.ghostencryptedchat.UsernameSetupActivity" />
75-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:53:9-59
75-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:53:19-56
76        <activity
76-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:54:9-56:40
77            android:name="com.sr.ghostencryptedchat.DMChatActivity"
77-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:55:13-43
78            android:exported="false" />
78-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:56:13-37
79
80        <service
80-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:9:9-15:19
81            android:name="com.google.firebase.components.ComponentDiscoveryService"
81-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:10:13-84
82            android:directBootAware="true"
82-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
83            android:exported="false" >
83-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:11:13-37
84            <meta-data
84-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:12:13-14:85
85                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
85-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:13:17-129
86                android:value="com.google.firebase.components.ComponentRegistrar" />
86-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:14:17-82
87            <meta-data
87-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:17:13-19:85
88                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
88-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:18:17-122
89                android:value="com.google.firebase.components.ComponentRegistrar" />
89-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:19:17-82
90            <meta-data
90-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:20:13-22:85
91                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
91-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:21:17-111
92                android:value="com.google.firebase.components.ComponentRegistrar" />
92-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:22:17-82
93            <meta-data
93-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
94                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
94-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
95                android:value="com.google.firebase.components.ComponentRegistrar" />
95-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
96            <meta-data
96-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
97                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
97-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
98                android:value="com.google.firebase.components.ComponentRegistrar" />
98-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
99        </service>
100
101        <provider
101-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
102            android:name="com.google.firebase.provider.FirebaseInitProvider"
102-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
103            android:authorities="com.sr.ghostencryptedchat.firebaseinitprovider"
103-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
104            android:directBootAware="true"
104-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
105            android:exported="false"
105-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
106            android:initOrder="100" />
106-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
107
108        <activity
108-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
109            android:name="com.google.android.gms.common.api.GoogleApiActivity"
109-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
110            android:exported="false"
110-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
111            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
111-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
112
113        <uses-library
113-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
114            android:name="androidx.window.extensions"
114-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
115            android:required="false" />
115-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
116        <uses-library
116-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
117            android:name="androidx.window.sidecar"
117-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
118            android:required="false" />
118-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
119
120        <meta-data
120-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
121            android:name="com.google.android.gms.version"
121-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
122            android:value="@integer/google_play_services_version" />
122-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
123
124        <provider
124-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
125            android:name="androidx.startup.InitializationProvider"
125-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
126            android:authorities="com.sr.ghostencryptedchat.androidx-startup"
126-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
127            android:exported="false" >
127-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
128            <meta-data
128-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
129                android:name="androidx.emoji2.text.EmojiCompatInitializer"
129-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
130                android:value="androidx.startup" />
130-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
131            <meta-data
131-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
132                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
132-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
133                android:value="androidx.startup" />
133-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
134        </provider>
135    </application>
136
137</manifest>
