1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.sr.ghostencryptedchat"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="33" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:4:5-67
11-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:5:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:5:22-76
13    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
13-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:6:5-83
13-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:6:22-80
14    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
14-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:7:5-88
14-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:7:22-85
15    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
15-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:8:5-76
15-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:8:22-73
16
17    <permission
17-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
18        android:name="com.sr.ghostencryptedchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.sr.ghostencryptedchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
22
23    <application
23-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:10:5-56:19
24        android:allowBackup="true"
24-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:11:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.10.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\1100a1eb0e13467cdf6e3e9fa04c3895\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:12:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:13:9-54
30        android:icon="@mipmap/ic_launcher"
30-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:14:9-43
31        android:label="@string/app_name"
31-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:15:9-41
32        android:networkSecurityConfig="@xml/network_security_config"
32-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:19:9-69
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:16:9-54
34        android:supportsRtl="true"
34-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:17:9-35
35        android:theme="@style/Theme.GhostEncryptedChat" >
35-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:18:9-56
36
37        <!-- FileProvider for APK installation -->
38        <provider
39            android:name="androidx.core.content.FileProvider"
39-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:24:13-62
40            android:authorities="com.sr.ghostencryptedchat.provider"
40-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:25:13-60
41            android:exported="false"
41-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:26:13-37
42            android:grantUriPermissions="true" >
42-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:27:13-47
43            <meta-data
43-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:28:13-30:54
44                android:name="android.support.FILE_PROVIDER_PATHS"
44-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:29:17-67
45                android:resource="@xml/file_paths" />
45-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:30:17-51
46        </provider>
47
48        <!-- ✅ Splash launches the app -->
49        <activity
49-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:34:9-41:20
50            android:name="com.sr.ghostencryptedchat.SplashActivity"
50-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:35:13-43
51            android:exported="true" >
51-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:36:13-36
52            <intent-filter>
52-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:37:13-40:29
53                <action android:name="android.intent.action.MAIN" />
53-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:38:17-69
53-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:38:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:39:17-77
55-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:39:27-74
56            </intent-filter>
57        </activity>
58
59        <!-- ✅ Your other activities (each listed once) -->
60        <activity android:name="com.sr.ghostencryptedchat.EncryptedChatActivity" />
60-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:44:9-59
60-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:44:19-56
61        <activity
61-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:45:9-83
62            android:name="com.sr.ghostencryptedchat.DirectMessageActivity"
62-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:45:19-56
63            android:exported="true" />
63-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:45:57-80
64        <activity android:name="com.sr.ghostencryptedchat.GroupChatActivity" />
64-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:46:9-55
64-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:46:19-52
65        <activity
65-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:47:9-79
66            android:name="com.sr.ghostencryptedchat.DMRequestActivity"
66-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:47:19-52
67            android:exported="true" />
67-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:47:53-76
68        <activity android:name="com.sr.ghostencryptedchat.MainActivity" />
68-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:48:9-50
68-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:48:19-47
69        <activity
69-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:49:9-78
70            android:name="com.sr.ghostencryptedchat.ContactsActivity"
70-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:49:19-51
71            android:exported="true" />
71-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:49:52-75
72        <activity android:name="com.sr.ghostencryptedchat.SettingsActivity" />
72-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:50:9-54
72-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:50:19-51
73        <activity android:name="com.sr.ghostencryptedchat.UsernameSetupActivity" />
73-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:51:9-59
73-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:51:19-56
74        <activity
74-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:52:9-54:40
75            android:name="com.sr.ghostencryptedchat.DMChatActivity"
75-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:53:13-43
76            android:exported="false" />
76-->C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\AndroidManifest.xml:54:13-37
77
78        <service
78-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:9:9-15:19
79            android:name="com.google.firebase.components.ComponentDiscoveryService"
79-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:10:13-84
80            android:directBootAware="true"
80-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
81            android:exported="false" >
81-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:11:13-37
82            <meta-data
82-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:12:13-14:85
83                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
83-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:13:17-129
84                android:value="com.google.firebase.components.ComponentRegistrar" />
84-->[com.google.firebase:firebase-firestore-ktx:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\36c90d3ff7118533a8ba8177e5cc9a89\transformed\firebase-firestore-ktx-24.9.0\AndroidManifest.xml:14:17-82
85            <meta-data
85-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:17:13-19:85
86                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
86-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:18:17-122
87                android:value="com.google.firebase.components.ComponentRegistrar" />
87-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:19:17-82
88            <meta-data
88-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:20:13-22:85
89                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
89-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:21:17-111
90                android:value="com.google.firebase.components.ComponentRegistrar" />
90-->[com.google.firebase:firebase-firestore:24.9.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\44255e7534b580c18be2e6b3116f7d59\transformed\firebase-firestore-24.9.0\AndroidManifest.xml:22:17-82
91            <meta-data
91-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
92                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
92-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
93                android:value="com.google.firebase.components.ComponentRegistrar" />
93-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\778990bbfa63fdc26284c304e44e6611\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
94            <meta-data
94-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
95                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
95-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
97        </service>
98
99        <provider
99-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
100            android:name="com.google.firebase.provider.FirebaseInitProvider"
100-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
101            android:authorities="com.sr.ghostencryptedchat.firebaseinitprovider"
101-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
102            android:directBootAware="true"
102-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
103            android:exported="false"
103-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
104            android:initOrder="100" />
104-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\f9d2a17d89f4262a8369c7ab29485334\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
105
106        <uses-library
106-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
107            android:name="androidx.window.extensions"
107-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
108            android:required="false" />
108-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
109        <uses-library
109-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
110            android:name="androidx.window.sidecar"
110-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
111            android:required="false" />
111-->[androidx.window:window:1.0.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\b574604460ae775e7b12ca55a8e35bd8\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
112
113        <activity
113-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
114            android:name="com.google.android.gms.common.api.GoogleApiActivity"
114-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
115            android:exported="false"
115-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
116            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
116-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\6142c6f2f2c5da410d9cf340d166ee81\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
117
118        <meta-data
118-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
119            android:name="com.google.android.gms.version"
119-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
120            android:value="@integer/google_play_services_version" />
120-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\ce7eb9a863475fa63014d727748770e8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
121
122        <provider
122-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
123            android:name="androidx.startup.InitializationProvider"
123-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
124            android:authorities="com.sr.ghostencryptedchat.androidx-startup"
124-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
125            android:exported="false" >
125-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
126            <meta-data
126-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.emoji2.text.EmojiCompatInitializer"
127-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
128                android:value="androidx.startup" />
128-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\a8a8ad82d9e7c48a5383529c2412c397\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
130                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
130-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
131                android:value="androidx.startup" />
131-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.jdks\ms-17.0.15\caches\transforms-3\7e2d5fb703a26032f3a7fb1772d5659d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
132        </provider>
133    </application>
134
135</manifest>
