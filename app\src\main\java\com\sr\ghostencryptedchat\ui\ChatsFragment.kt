package com.sr.ghostencryptedchat.ui

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.firebase.firestore.FirebaseFirestore
import com.sr.ghostencryptedchat.DMChatActivity
import com.sr.ghostencryptedchat.R
import com.sr.ghostencryptedchat.adapter.ChatListAdapter
import com.sr.ghostencryptedchat.databinding.FragmentChatsBinding
import com.sr.ghostencryptedchat.util.KeyStoreUtil

class ChatsFragment : Fragment() {

    private var _binding: FragmentChatsBinding? = null
    private val binding get() = _binding!!

    private lateinit var db: FirebaseFirestore
    private lateinit var username: String
    private lateinit var adapter: ChatListAdapter
    private val chatList = mutableListOf<Pair<String, Boolean>>() // (chat partner, isNew)

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentChatsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        db = FirebaseFirestore.getInstance()
        username = KeyStoreUtil.getOrCreateUsername(requireContext())

        // Make sure 3D background is visible by default
        binding.backgroundView.visibility = View.VISIBLE
        
        // Use a more transparent background for the content layout
        binding.chatLayout.setBackgroundColor(Color.parseColor("#88121212"))

        // Add a long press listener to the title to toggle the 3D background
        binding.chatTitle.setOnLongClickListener {
            toggleMatrixBackground()
            true
        }

        adapter = ChatListAdapter(chatList,
            onClick = { selected ->
                if (selected == "World Chat") {
                    parentFragmentManager.beginTransaction()
                        .hide(this@ChatsFragment)
                        .add(R.id.fragmentContainer, GlobalChatFragment())
                        .addToBackStack("global_chat")
                        .commit()
                } else {
                    val intent = Intent(requireContext(), DMChatActivity::class.java)
                    intent.putExtra("recipient", selected)
                    startActivity(intent)
                }
            },
            onLongClick = { selected ->
                if (selected != "World Chat") {
                    showDeleteDialog(selected)
                }
            }
        )

        binding.chatListRecycler.layoutManager = LinearLayoutManager(requireContext())
        binding.chatListRecycler.adapter = adapter

        fetchChatList()
    }

    private fun fetchChatList() {
        chatList.clear()
        chatList.add(Pair("World Chat", false))

        db.collection("dm_chats")
            .whereArrayContains("participants", username)
            .get()
            .addOnSuccessListener { result ->
                for (doc in result.documents) {
                    val participants = doc.get("participants") as? List<*> ?: continue
                    val otherUser = participants.firstOrNull { it != username }?.toString() ?: continue
                    val lastSender = doc.getString("lastSender") ?: ""
                    val lastMessageTime = doc.getLong("lastMessageTime") ?: 0L
                    val lastReadMap = doc.get("lastRead") as? Map<*, *>
                    val lastRead = (lastReadMap?.get(username) as? Number)?.toLong() ?: 0L
                    // We'll use status if needed later
                    // val status = doc.getString("status") ?: "accepted"

                    // ✅ Show "NEW" if message is from other user AND not yet read
                    val isNew = (lastSender != username && lastMessageTime > lastRead)

                    // ✅ Show only accepted or pending if current user is initiator
                    if (!chatList.any { it.first == otherUser }) {
                        chatList.add(Pair(otherUser, isNew))
                    }
                }
                adapter.notifyDataSetChanged()
            }
            .addOnFailureListener {
                Toast.makeText(requireContext(), "Failed to load chats", Toast.LENGTH_SHORT).show()
            }
    }

    private fun showDeleteDialog(user: String) {
        AlertDialog.Builder(requireContext())
            .setTitle("Delete Chat")
            .setMessage("Are you sure you want to delete the chat with $user?")
            .setPositiveButton("Delete") { _, _ -> deleteChat(user) }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun deleteChat(otherUser: String) {
        val chatId = listOf(username, otherUser).sorted().joinToString("_")
        db.collection("dm_chats").document(chatId)
            .delete()
            .addOnSuccessListener {
                Toast.makeText(requireContext(), "Chat deleted", Toast.LENGTH_SHORT).show()
                fetchChatList()
            }
            .addOnFailureListener {
                Toast.makeText(requireContext(), "Failed to delete chat", Toast.LENGTH_SHORT).show()
            }
    }

    override fun onResume() {
        super.onResume()
        fetchChatList()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun toggleMatrixBackground() {
        val backgroundView = binding.backgroundView
        if (backgroundView.visibility == View.VISIBLE) {
            backgroundView.visibility = View.GONE
            binding.chatLayout.setBackgroundColor(Color.parseColor("#121212"))
            Toast.makeText(requireContext(), "3D background disabled", Toast.LENGTH_SHORT).show()
        } else {
            backgroundView.visibility = View.VISIBLE
            binding.chatLayout.setBackgroundColor(Color.parseColor("#88121212"))
            Toast.makeText(requireContext(), "3D background enabled", Toast.LENGTH_SHORT).show()
        }
    }
}
