<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_tool" modulePackage="com.sr.ghostencryptedchat" filePath="app\src\main\res\layout\item_tool.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_tool_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="95" endOffset="35"/></Target><Target id="@+id/toolIcon" view="TextView"><Expressions/><location startLine="20" startOffset="8" endLine="29" endOffset="37"/></Target><Target id="@+id/toolTitle" view="TextView"><Expressions/><location startLine="44" startOffset="16" endLine="53" endOffset="59"/></Target><Target id="@+id/comingSoonLabel" view="TextView"><Expressions/><location startLine="55" startOffset="16" endLine="69" endOffset="47"/></Target><Target id="@+id/toolDescription" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="81" endOffset="58"/></Target></Targets></Layout>