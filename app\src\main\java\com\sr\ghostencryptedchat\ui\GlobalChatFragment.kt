package com.sr.ghostencryptedchat.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ListenerRegistration
import com.sr.ghostencryptedchat.adapter.PublicChatAdapter
import com.sr.ghostencryptedchat.databinding.FragmentGlobalChatBinding
import com.sr.ghostencryptedchat.model.ChatMessage
import com.sr.ghostencryptedchat.util.EncryptionUtil
import com.sr.ghostencryptedchat.util.KeyStoreUtil

class GlobalChatFragment : Fragment() {

    private var _binding: FragmentGlobalChatBinding? = null
    private val binding get() = _binding!!

    private lateinit var db: FirebaseFirestore
    private lateinit var username: String
    private lateinit var adapter: PublicChatAdapter
    private val messages = mutableListOf<ChatMessage>()
    private var listener: ListenerRegistration? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentGlobalChatBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        db = FirebaseFirestore.getInstance()
        username = KeyStoreUtil.getOrCreateUsername(requireContext())

        adapter = PublicChatAdapter(messages, username)
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.recyclerView.adapter = adapter

        binding.sendButton.setOnClickListener {
            val plaintext = binding.messageInput.text.toString()
            if (plaintext.isNotBlank()) {
                val encrypted = EncryptionUtil.encrypt(plaintext)
                val message = ChatMessage(username, encrypted, System.currentTimeMillis())
                db.collection("publicChat").add(message)
                binding.messageInput.setText("")
            }
        }

        listener = db.collection("publicChat")
            .orderBy("timestamp")
            .addSnapshotListener { snapshot, _ ->
                if (snapshot != null && _binding != null) {
                    messages.clear()
                    messages.addAll(snapshot.documents.mapNotNull {
                        it.toObject(ChatMessage::class.java)
                    })
                    adapter.notifyDataSetChanged()
                    binding.recyclerView.scrollToPosition(messages.size - 1)
                }
            }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        listener?.remove()
        _binding = null
    }
}
