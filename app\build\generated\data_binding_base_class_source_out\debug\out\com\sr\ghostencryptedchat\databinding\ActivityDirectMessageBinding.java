// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ListView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDirectMessageBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout directMessageLayout;

  @NonNull
  public final EditText dmInput;

  @NonNull
  public final ListView dmList;

  @NonNull
  public final Button dmSend;

  private ActivityDirectMessageBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout directMessageLayout, @NonNull EditText dmInput,
      @NonNull ListView dmList, @NonNull Button dmSend) {
    this.rootView = rootView;
    this.directMessageLayout = directMessageLayout;
    this.dmInput = dmInput;
    this.dmList = dmList;
    this.dmSend = dmSend;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDirectMessageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDirectMessageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_direct_message, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDirectMessageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      LinearLayout directMessageLayout = (LinearLayout) rootView;

      id = R.id.dmInput;
      EditText dmInput = ViewBindings.findChildViewById(rootView, id);
      if (dmInput == null) {
        break missingId;
      }

      id = R.id.dmList;
      ListView dmList = ViewBindings.findChildViewById(rootView, id);
      if (dmList == null) {
        break missingId;
      }

      id = R.id.dmSend;
      Button dmSend = ViewBindings.findChildViewById(rootView, id);
      if (dmSend == null) {
        break missingId;
      }

      return new ActivityDirectMessageBinding((LinearLayout) rootView, directMessageLayout, dmInput,
          dmList, dmSend);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
