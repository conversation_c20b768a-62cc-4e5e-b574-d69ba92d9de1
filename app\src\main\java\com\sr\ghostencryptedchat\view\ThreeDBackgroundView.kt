package com.sr.ghostencryptedchat.view

import android.content.Context
import android.net.Uri
import android.util.AttributeSet
import android.util.Log
import android.view.ViewGroup
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.ui.PlayerView

class ThreeDBackgroundView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : PlayerView(context, attrs, defStyleAttr) {

    private var exoPlayer: ExoPlayer? = null
    private var isVideoReady = false

    init {
        setupFlawlessExoPlayer()
        Log.d("VideoBackground", "Flawless ExoPlayer background initialized")
    }

    private fun setupFlawlessExoPlayer() {
        try {
            // Create ExoPlayer instance
            exoPlayer = ExoPlayer.Builder(context).build().apply {

                // Configure for flawless looping
                repeatMode = Player.REPEAT_MODE_ONE // Perfect seamless looping
                volume = 0f // Mute for background

                // Set up listener for when player is ready
                addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        when (playbackState) {
                            Player.STATE_READY -> {
                                if (!isVideoReady) {
                                    isVideoReady = true
                                    Log.d("VideoBackground", "ExoPlayer ready - starting playback")
                                    play()
                                }
                            }
                            Player.STATE_ENDED -> {
                                Log.d("VideoBackground", "Video ended - should auto-repeat")
                            }
                            Player.STATE_BUFFERING -> {
                                Log.d("VideoBackground", "Buffering...")
                            }
                            Player.STATE_IDLE -> {
                                Log.d("VideoBackground", "Player idle")
                            }
                        }
                    }

                    override fun onPlayerError(error: com.google.android.exoplayer2.PlaybackException) {
                        Log.e("VideoBackground", "ExoPlayer error: ${error.message}")
                        // Try to restart
                        postDelayed({ setupFlawlessExoPlayer() }, 1000)
                    }
                })

                // Create media item from raw resource
                val uri = Uri.parse("android.resource://${context.packageName}/${com.sr.ghostencryptedchat.R.raw.background}")
                val mediaItem = MediaItem.fromUri(uri)

                // Set media and prepare
                setMediaItem(mediaItem)
                prepare()
            }

            // Attach player to view
            player = exoPlayer

            // Configure PlayerView for background video
            useController = false // Hide controls
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )

            // Ensure full screen coverage
            resizeMode = com.google.android.exoplayer2.ui.AspectRatioFrameLayout.RESIZE_MODE_ZOOM

            Log.d("VideoBackground", "ExoPlayer setup completed")

        } catch (e: Exception) {
            Log.e("VideoBackground", "Failed to setup ExoPlayer: ${e.message}")
            // Try fallback
            setupFallback()
        }
    }





    private fun setupFallback() {
        try {
            Log.d("VideoBackground", "Setting up fallback...")
            // Could implement a simple color background or image as fallback
            setBackgroundColor(android.graphics.Color.parseColor("#121212"))
        } catch (e: Exception) {
            Log.e("VideoBackground", "Fallback setup failed: ${e.message}")
        }
    }

    fun pauseVideo() {
        try {
            exoPlayer?.pause()
            Log.d("VideoBackground", "Video paused")
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error pausing video: ${e.message}")
        }
    }

    fun resumeVideo() {
        try {
            exoPlayer?.play()
            Log.d("VideoBackground", "Video resumed")
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error resuming video: ${e.message}")
        }
    }

    private fun releaseExoPlayer() {
        try {
            exoPlayer?.release()
            exoPlayer = null
            player = null
            isVideoReady = false
            Log.d("VideoBackground", "ExoPlayer released")
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error releasing ExoPlayer: ${e.message}")
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        releaseExoPlayer()
    }










}











