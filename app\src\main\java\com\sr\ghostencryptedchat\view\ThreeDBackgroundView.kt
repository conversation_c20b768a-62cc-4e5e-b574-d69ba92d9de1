package com.sr.ghostencryptedchat.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PointF
import android.graphics.RadialGradient
import android.graphics.Shader
import android.util.AttributeSet
import android.util.Log
import android.view.View
import java.io.BufferedReader
import java.io.InputStreamReader
import java.util.Random
import kotlin.math.cos
import kotlin.math.sin

class ThreeDBackgroundView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val random = Random()
    private val paint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.FILL
    }
    
    private val models3D = mutableListOf<Model3D>()

    // Colors for the 3D effect - deeper greens based on the image
    private val darkColor = Color.parseColor("#121212")
    private val accentColor = Color.parseColor("#aeff00") // Bright green
    private val midColor = Color.parseColor("#0A3B15") // Deeper green
    private val shadowColor = Color.parseColor("#0A0A0A")
    
    // Pre-calculated trigonometric values
    private val cosValues = FloatArray(360)
    private val sinValues = FloatArray(360)

    // Animation variables - smoother and faster
    private var animationTime = 0f
    private var lastFrameTime = System.currentTimeMillis()
    private val animationSpeed = 1.0f // Smooth, fluid animation
    
    init {
        // Set background color
        setBackgroundColor(darkColor)
        
        // Enable hardware acceleration
        setLayerType(LAYER_TYPE_HARDWARE, null)
        
        // Pre-calculate sin and cos values
        for (i in 0 until 360) {
            val radians = Math.toRadians(i.toDouble())
            cosValues[i] = cos(radians).toFloat()
            sinValues[i] = sin(radians).toFloat()
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // Create 3D models
        create3DModels(w, h)

        // Force a redraw with the new pattern
        invalidate()
    }


    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // Update animation
        val currentTime = System.currentTimeMillis()
        val deltaTime = (currentTime - lastFrameTime) / 16.67f // Normalize to 60fps
        lastFrameTime = currentTime
        animationTime += deltaTime * animationSpeed

        // Update 3D models
        update3DModels(deltaTime)

        // Draw 3D models
        for (model in models3D) {
            draw3DModel(canvas, model)
        }

        // Request next frame for smooth animation
        invalidate()
    }


    


    private fun create3DModels(w: Int, h: Int) {
        models3D.clear()

        // Create more 3D models for richer background (6-10)
        val modelCount = 6 + random.nextInt(5)

        // Add 1-2 custom OBJ models
        val objModelCount = 1 + random.nextInt(2)

        for (i in 0 until modelCount) {
            // Random position
            val centerX = random.nextInt(w).toFloat()
            val centerY = random.nextInt(h).toFloat()
            val centerZ = random.nextFloat() * 300f - 150f // -150 to 150 for more depth variation

            // Varied sizes for more visual interest
            val size = 25f + random.nextFloat() * 60f // 25-85px

            // Random rotation
            val rotationX = random.nextFloat() * 360f
            val rotationY = random.nextFloat() * 360f
            val rotationZ = random.nextFloat() * 360f

            // Smoother rotation speeds - faster but still smooth
            val rotationSpeedX = (random.nextFloat() - 0.5f) * 1.2f
            val rotationSpeedY = (random.nextFloat() - 0.5f) * 1.2f
            val rotationSpeedZ = (random.nextFloat() - 0.5f) * 1.2f

            // More dynamic drift
            val driftSpeedX = (random.nextFloat() - 0.5f) * 0.4f
            val driftSpeedY = (random.nextFloat() - 0.5f) * 0.4f
            val driftSpeedZ = (random.nextFloat() - 0.5f) * 0.2f

            // Random model type (exclude CUSTOM_OBJ for now)
            val availableTypes = arrayOf(ModelType.CUBE, ModelType.CRYSTAL, ModelType.PYRAMID)
            val modelType = availableTypes[random.nextInt(availableTypes.size)]

            // Varied alpha for depth perception
            val alpha = 100 + random.nextInt(80) // 100-180 for more variation

            models3D.add(Model3D(
                centerX = centerX,
                centerY = centerY,
                centerZ = centerZ,
                size = size,
                rotationX = rotationX,
                rotationY = rotationY,
                rotationZ = rotationZ,
                rotationSpeedX = rotationSpeedX,
                rotationSpeedY = rotationSpeedY,
                rotationSpeedZ = rotationSpeedZ,
                driftSpeedX = driftSpeedX,
                driftSpeedY = driftSpeedY,
                driftSpeedZ = driftSpeedZ,
                originalX = centerX,
                originalY = centerY,
                originalZ = centerZ,
                modelType = modelType,
                alpha = alpha
            ))
        }

        // Add custom OBJ models
        for (i in 0 until objModelCount) {
            createCustomOBJModel(w, h)
        }
    }

    private fun createCustomOBJModel(w: Int, h: Int) {
        // List of OBJ files to try loading (put your .obj file name here)
        val objFiles = listOf(
            "ghost.obj",            // Your ghost model - perfect for the app theme!
            "test_spaceship.obj",   // Test model fallback
            "model.obj",            // Additional fallbacks
            "spaceship.obj",
            "skull.obj",
            "robot.obj"
        )

        // Try to load one of the OBJ files
        val objLoader = OBJLoader()
        var loadedModel: Pair<List<Vertex3D>, List<Edge3D>>? = null

        for (fileName in objFiles) {
            val result = objLoader.loadModel(fileName, 1.0f) // Base size multiplier
            if (result.first.isNotEmpty()) {
                loadedModel = result
                Log.d("ThreeDBackground", "Successfully loaded $fileName")
                break
            }
        }

        // If we successfully loaded a model, create it
        loadedModel?.let { (vertices, edges) ->
            // Random position
            val centerX = random.nextInt(w).toFloat()
            val centerY = random.nextInt(h).toFloat()
            val centerZ = random.nextFloat() * 300f - 150f

            // Size for ghost models (ghostly and prominent)
            val size = 50f + random.nextFloat() * 60f // 50-110px (larger for ghost presence)

            // Random rotation
            val rotationX = random.nextFloat() * 360f
            val rotationY = random.nextFloat() * 360f
            val rotationZ = random.nextFloat() * 360f

            // Slower, more ethereal rotation speeds for ghostly effect
            val rotationSpeedX = (random.nextFloat() - 0.5f) * 0.8f
            val rotationSpeedY = (random.nextFloat() - 0.5f) * 0.8f
            val rotationSpeedZ = (random.nextFloat() - 0.5f) * 0.8f

            // Gentle, floating drift speeds (ghostly movement)
            val driftSpeedX = (random.nextFloat() - 0.5f) * 0.25f
            val driftSpeedY = (random.nextFloat() - 0.5f) * 0.25f
            val driftSpeedZ = (random.nextFloat() - 0.5f) * 0.12f

            // Higher alpha for ghost visibility (more prominent)
            val alpha = 140 + random.nextInt(50) // 140-190 (more visible ghost)

            // Create the model
            val customModel = Model3D(
                centerX = centerX,
                centerY = centerY,
                centerZ = centerZ,
                size = size,
                rotationX = rotationX,
                rotationY = rotationY,
                rotationZ = rotationZ,
                rotationSpeedX = rotationSpeedX,
                rotationSpeedY = rotationSpeedY,
                rotationSpeedZ = rotationSpeedZ,
                driftSpeedX = driftSpeedX,
                driftSpeedY = driftSpeedY,
                driftSpeedZ = driftSpeedZ,
                originalX = centerX,
                originalY = centerY,
                originalZ = centerZ,
                modelType = ModelType.CUSTOM_OBJ,
                alpha = alpha
            )

            // Manually set the vertices and edges from the OBJ file
            customModel.vertices.clear()
            customModel.edges.clear()
            customModel.vertices.addAll(vertices)
            customModel.edges.addAll(edges)

            models3D.add(customModel)
        }
    }

    private fun update3DModels(deltaTime: Float) {
        for (model in models3D) {
            // Smooth rotation updates
            model.rotationX += model.rotationSpeedX * deltaTime
            model.rotationY += model.rotationSpeedY * deltaTime
            model.rotationZ += model.rotationSpeedZ * deltaTime

            // Keep rotations in bounds
            model.rotationX = model.rotationX % 360f
            model.rotationY = model.rotationY % 360f
            model.rotationZ = model.rotationZ % 360f

            // Enhanced floating with multiple wave frequencies for organic movement
            val floatOffsetX = sin(animationTime * 0.008f + model.originalX * 0.001f) * 25f +
                             cos(animationTime * 0.003f + model.originalX * 0.0005f) * 10f
            val floatOffsetY = cos(animationTime * 0.006f + model.originalY * 0.001f) * 20f +
                             sin(animationTime * 0.004f + model.originalY * 0.0008f) * 8f
            val floatOffsetZ = sin(animationTime * 0.005f + model.originalZ * 0.001f) * 30f +
                             cos(animationTime * 0.007f + model.originalZ * 0.0006f) * 15f

            model.centerX = model.originalX + model.driftSpeedX * animationTime + floatOffsetX
            model.centerY = model.originalY + model.driftSpeedY * animationTime + floatOffsetY
            model.centerZ = model.originalZ + model.driftSpeedZ * animationTime + floatOffsetZ

            // Smooth screen wrapping
            if (model.centerX < -model.size * 3) {
                model.centerX = width.toFloat() + model.size * 3
            } else if (model.centerX > width + model.size * 3) {
                model.centerX = -model.size * 3
            }

            if (model.centerY < -model.size * 3) {
                model.centerY = height.toFloat() + model.size * 3
            } else if (model.centerY > height + model.size * 3) {
                model.centerY = -model.size * 3
            }
        }
    }

    private fun draw3DModel(canvas: Canvas, model: Model3D) {
        // Transform and project vertices
        val projectedVertices = mutableListOf<PointF>()

        for (vertex in model.vertices) {
            // Apply rotations
            val rotatedVertex = rotateVertex(vertex, model.rotationX, model.rotationY, model.rotationZ)

            // Project to 2D
            val projected = project3DTo2D(
                rotatedVertex.x + model.centerX,
                rotatedVertex.y + model.centerY,
                rotatedVertex.z + model.centerZ
            )
            projectedVertices.add(projected)
        }

        // Calculate distance-based alpha for depth effect
        val avgZ = model.centerZ
        val depthAlpha = (model.alpha * (1.0f - (avgZ + 150f) / 300f)).toInt().coerceIn(50, 255)

        // Enhanced wireframe rendering with multiple glow layers
        paint.style = Paint.Style.STROKE
        paint.color = accentColor
        paint.shader = null

        // Draw edges with layered glow effect
        for (edge in model.edges) {
            val start = projectedVertices[edge.start]
            val end = projectedVertices[edge.end]

            // Outer glow (thicker, more transparent)
            paint.strokeWidth = 6f
            paint.alpha = (depthAlpha * 0.3f).toInt()
            paint.setShadowLayer(12f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)

            // Middle glow
            paint.strokeWidth = 3f
            paint.alpha = (depthAlpha * 0.6f).toInt()
            paint.setShadowLayer(6f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)

            // Core line (sharp and bright)
            paint.strokeWidth = 1.5f
            paint.alpha = depthAlpha
            paint.setShadowLayer(3f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)
        }

        // Reset paint
        paint.clearShadowLayer()
        paint.style = Paint.Style.FILL
    }

    private fun rotateVertex(vertex: Vertex3D, rotX: Float, rotY: Float, rotZ: Float): Vertex3D {
        var x = vertex.x
        var y = vertex.y
        var z = vertex.z

        // Rotate around X axis
        val cosX = cos(Math.toRadians(rotX.toDouble())).toFloat()
        val sinX = sin(Math.toRadians(rotX.toDouble())).toFloat()
        val newY = y * cosX - z * sinX
        val newZ = y * sinX + z * cosX
        y = newY
        z = newZ

        // Rotate around Y axis
        val cosY = cos(Math.toRadians(rotY.toDouble())).toFloat()
        val sinY = sin(Math.toRadians(rotY.toDouble())).toFloat()
        val newX = x * cosY + z * sinY
        val newZ2 = -x * sinY + z * cosY
        x = newX
        z = newZ2

        // Rotate around Z axis
        val cosZ = cos(Math.toRadians(rotZ.toDouble())).toFloat()
        val sinZ = sin(Math.toRadians(rotZ.toDouble())).toFloat()
        val newX2 = x * cosZ - y * sinZ
        val newY2 = x * sinZ + y * cosZ
        x = newX2
        y = newY2

        return Vertex3D(x, y, z)
    }

    private fun project3DTo2D(x: Float, y: Float, z: Float): PointF {
        // Simple perspective projection
        val distance = 400f
        val scale = distance / (distance + z)
        return PointF(x * scale, y * scale)
    }



    // 3D Model classes and methods
    private data class Vertex3D(var x: Float, var y: Float, var z: Float)
    private data class Edge3D(val start: Int, val end: Int)

    private inner class Model3D(
        var centerX: Float,
        var centerY: Float,
        var centerZ: Float,
        val size: Float,
        var rotationX: Float,
        var rotationY: Float,
        var rotationZ: Float,
        val rotationSpeedX: Float,
        val rotationSpeedY: Float,
        val rotationSpeedZ: Float,
        val driftSpeedX: Float,
        val driftSpeedY: Float,
        val driftSpeedZ: Float,
        val originalX: Float,
        val originalY: Float,
        val originalZ: Float,
        val modelType: ModelType,
        val alpha: Int
    ) {
        val vertices = mutableListOf<Vertex3D>()
        val edges = mutableListOf<Edge3D>()

        init {
            createModel()
        }

        private fun createModel() {
            when (modelType) {
                ModelType.CUBE -> createCube()
                ModelType.CRYSTAL -> createCrystal()
                ModelType.PYRAMID -> createPyramid()
                ModelType.CUSTOM_OBJ -> {
                    // Custom OBJ models will be loaded separately
                    // This case is handled in the create3DModels function
                }
            }
        }

        private fun createCube() {
            // Define cube vertices
            vertices.addAll(listOf(
                Vertex3D(-size, -size, -size), // 0
                Vertex3D(size, -size, -size),  // 1
                Vertex3D(size, size, -size),   // 2
                Vertex3D(-size, size, -size),  // 3
                Vertex3D(-size, -size, size),  // 4
                Vertex3D(size, -size, size),   // 5
                Vertex3D(size, size, size),    // 6
                Vertex3D(-size, size, size)    // 7
            ))

            // Define cube edges
            edges.addAll(listOf(
                // Front face
                Edge3D(0, 1), Edge3D(1, 2), Edge3D(2, 3), Edge3D(3, 0),
                // Back face
                Edge3D(4, 5), Edge3D(5, 6), Edge3D(6, 7), Edge3D(7, 4),
                // Connecting edges
                Edge3D(0, 4), Edge3D(1, 5), Edge3D(2, 6), Edge3D(3, 7)
            ))
        }

        private fun createCrystal() {
            // Define crystal vertices (octahedron-like)
            vertices.addAll(listOf(
                Vertex3D(0f, -size * 1.5f, 0f),    // Top point
                Vertex3D(-size, 0f, -size),        // Front left
                Vertex3D(size, 0f, -size),         // Front right
                Vertex3D(size, 0f, size),          // Back right
                Vertex3D(-size, 0f, size),         // Back left
                Vertex3D(0f, size * 1.5f, 0f)     // Bottom point
            ))

            // Define crystal edges
            edges.addAll(listOf(
                // Top pyramid
                Edge3D(0, 1), Edge3D(0, 2), Edge3D(0, 3), Edge3D(0, 4),
                // Middle square
                Edge3D(1, 2), Edge3D(2, 3), Edge3D(3, 4), Edge3D(4, 1),
                // Bottom pyramid
                Edge3D(5, 1), Edge3D(5, 2), Edge3D(5, 3), Edge3D(5, 4)
            ))
        }

        private fun createPyramid() {
            // Define pyramid vertices
            vertices.addAll(listOf(
                Vertex3D(0f, -size * 1.2f, 0f),   // Top point
                Vertex3D(-size, size, -size),      // Base front left
                Vertex3D(size, size, -size),       // Base front right
                Vertex3D(size, size, size),        // Base back right
                Vertex3D(-size, size, size)        // Base back left
            ))

            // Define pyramid edges
            edges.addAll(listOf(
                // Base
                Edge3D(1, 2), Edge3D(2, 3), Edge3D(3, 4), Edge3D(4, 1),
                // Sides to top
                Edge3D(0, 1), Edge3D(0, 2), Edge3D(0, 3), Edge3D(0, 4)
            ))
        }
    }

    private enum class ModelType {
        CUBE, CRYSTAL, PYRAMID, CUSTOM_OBJ
    }

    // OBJ Model Loader
    private inner class OBJLoader {
        fun loadModel(fileName: String, size: Float): Pair<List<Vertex3D>, List<Edge3D>> {
            val vertices = mutableListOf<Vertex3D>()
            val faces = mutableListOf<List<Int>>()

            try {
                val inputStream = context.assets.open("models/$fileName")
                val reader = BufferedReader(InputStreamReader(inputStream))

                reader.useLines { lines ->
                    lines.forEach { line ->
                        val trimmedLine = line.trim()
                        when {
                            trimmedLine.startsWith("v ") -> {
                                // Parse vertex: v x y z
                                val parts = trimmedLine.split("\\s+".toRegex())
                                if (parts.size >= 4) {
                                    vertices.add(Vertex3D(
                                        parts[1].toFloat() * size,
                                        parts[2].toFloat() * size,
                                        parts[3].toFloat() * size
                                    ))
                                }
                            }
                            trimmedLine.startsWith("f ") -> {
                                // Parse face: f v1 v2 v3 or f v1/vt1/vn1 v2/vt2/vn2 v3/vt3/vn3
                                val parts = trimmedLine.split("\\s+".toRegex())
                                if (parts.size >= 4) {
                                    val faceVertices = parts.drop(1).map { part ->
                                        // Handle different face formats (v, v/vt, v/vt/vn)
                                        part.split("/")[0].toInt() - 1 // OBJ indices start at 1
                                    }
                                    faces.add(faceVertices)
                                }
                            }
                        }
                    }
                }
                reader.close()

                Log.d("OBJLoader", "Loaded ${vertices.size} vertices and ${faces.size} faces from $fileName")

            } catch (e: Exception) {
                Log.e("OBJLoader", "Error loading OBJ file $fileName: ${e.message}")
                // Return empty lists if loading fails
                return Pair(emptyList(), emptyList())
            }

            // Convert faces to edges for wireframe rendering
            val edges = mutableSetOf<Edge3D>()

            for (face in faces) {
                // Create edges for each face (connect consecutive vertices)
                for (i in face.indices) {
                    val currentVertex = face[i]
                    val nextVertex = face[(i + 1) % face.size]

                    // Add edge (avoid duplicates by using Set)
                    val edge1 = Edge3D(currentVertex, nextVertex)
                    val edge2 = Edge3D(nextVertex, currentVertex)

                    // Only add if neither direction exists
                    if (!edges.contains(edge2)) {
                        edges.add(edge1)
                    }
                }
            }

            return Pair(vertices, edges.toList())
        }
    }
}











