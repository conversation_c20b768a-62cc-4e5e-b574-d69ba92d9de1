package com.sr.ghostencryptedchat.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import java.util.Random
import kotlin.math.cos
import kotlin.math.sin

class ThreeDBackgroundView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val random = Random()
    private val paint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.FILL
    }
    
    private val models3D = mutableListOf<Model3D>()

    // Colors for the 3D effect - deeper greens based on the image
    private val darkColor = Color.parseColor("#121212")
    private val accentColor = Color.parseColor("#aeff00") // Bright green
    private val midColor = Color.parseColor("#0A3B15") // Deeper green
    private val shadowColor = Color.parseColor("#0A0A0A")
    
    // Pre-calculated trigonometric values
    private val cosValues = FloatArray(360)
    private val sinValues = FloatArray(360)

    // No animation variables needed for static cubes
    
    init {
        // Set background color
        setBackgroundColor(darkColor)
        
        // Enable hardware acceleration
        setLayerType(LAYER_TYPE_HARDWARE, null)
        
        // Pre-calculate sin and cos values
        for (i in 0 until 360) {
            val radians = Math.toRadians(i.toDouble())
            cosValues[i] = cos(radians).toFloat()
            sinValues[i] = sin(radians).toFloat()
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // Create static cubes
        createStaticCubes(w, h)

        // Force a redraw
        invalidate()
    }


    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // Draw static cubes (no animation)
        for (model in models3D) {
            drawStaticCube(canvas, model)
        }
    }


    


    private fun createStaticCubes(w: Int, h: Int) {
        models3D.clear()

        // Create 6 static wireframe cubes for performance testing
        val cubeCount = 6

        for (i in 0 until cubeCount) {
            // Distribute cubes evenly across screen
            val centerX = (w / 3f) * (i % 3) + (w / 6f)
            val centerY = (h / 2f) * (i / 3) + (h / 4f)
            val centerZ = 0f // No Z depth for maximum performance

            // Fixed size for consistency
            val size = 40f

            // Fixed rotation for static display
            val rotationX = 0f
            val rotationY = 0f
            val rotationZ = 0f

            // No animation speeds (static)
            val rotationSpeedX = 0f
            val rotationSpeedY = 0f
            val rotationSpeedZ = 0f

            // No movement (static)
            val driftSpeedX = 0f
            val driftSpeedY = 0f
            val driftSpeedZ = 0f

            // Only cubes
            val modelType = ModelType.CUBE

            // High alpha for visibility
            val alpha = 180

            models3D.add(Model3D(
                centerX = centerX,
                centerY = centerY,
                centerZ = centerZ,
                size = size,
                rotationX = rotationX,
                rotationY = rotationY,
                rotationZ = rotationZ,
                rotationSpeedX = rotationSpeedX,
                rotationSpeedY = rotationSpeedY,
                rotationSpeedZ = rotationSpeedZ,
                driftSpeedX = driftSpeedX,
                driftSpeedY = driftSpeedY,
                driftSpeedZ = driftSpeedZ,
                originalX = centerX,
                originalY = centerY,
                originalZ = centerZ,
                modelType = modelType,
                alpha = alpha
            ))
        }
    }



    private fun drawStaticCube(canvas: Canvas, model: Model3D) {
        // Simple static wireframe rendering (no rotation, no animation)
        val projectedVertices = mutableListOf<PointF>()

        for (vertex in model.vertices) {
            // No rotation - use vertices as-is
            val projected = PointF(
                vertex.x + model.centerX,
                vertex.y + model.centerY
            )
            projectedVertices.add(projected)
        }

        // Set up paint for wireframe
        paint.style = Paint.Style.STROKE
        paint.color = accentColor
        paint.shader = null
        paint.strokeWidth = 2f
        paint.alpha = model.alpha
        paint.setShadowLayer(4f, 0f, 0f, accentColor)

        // Draw edges
        for (edge in model.edges) {
            val start = projectedVertices[edge.start]
            val end = projectedVertices[edge.end]
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)
        }

        // Reset paint
        paint.clearShadowLayer()
        paint.style = Paint.Style.FILL
    }





    // 3D Model classes and methods
    private data class Vertex3D(var x: Float, var y: Float, var z: Float)
    private data class Edge3D(val start: Int, val end: Int)

    private inner class Model3D(
        var centerX: Float,
        var centerY: Float,
        var centerZ: Float,
        val size: Float,
        var rotationX: Float,
        var rotationY: Float,
        var rotationZ: Float,
        val rotationSpeedX: Float,
        val rotationSpeedY: Float,
        val rotationSpeedZ: Float,
        val driftSpeedX: Float,
        val driftSpeedY: Float,
        val driftSpeedZ: Float,
        val originalX: Float,
        val originalY: Float,
        val originalZ: Float,
        val modelType: ModelType,
        val alpha: Int
    ) {
        val vertices = mutableListOf<Vertex3D>()
        val edges = mutableListOf<Edge3D>()

        init {
            createModel()
        }

        private fun createModel() {
            when (modelType) {
                ModelType.CUBE -> createCube()
            }
        }

        private fun createCube() {
            // Define cube vertices
            vertices.addAll(listOf(
                Vertex3D(-size, -size, -size), // 0
                Vertex3D(size, -size, -size),  // 1
                Vertex3D(size, size, -size),   // 2
                Vertex3D(-size, size, -size),  // 3
                Vertex3D(-size, -size, size),  // 4
                Vertex3D(size, -size, size),   // 5
                Vertex3D(size, size, size),    // 6
                Vertex3D(-size, size, size)    // 7
            ))

            // Define cube edges
            edges.addAll(listOf(
                // Front face
                Edge3D(0, 1), Edge3D(1, 2), Edge3D(2, 3), Edge3D(3, 0),
                // Back face
                Edge3D(4, 5), Edge3D(5, 6), Edge3D(6, 7), Edge3D(7, 4),
                // Connecting edges
                Edge3D(0, 4), Edge3D(1, 5), Edge3D(2, 6), Edge3D(3, 7)
            ))
        }


    }

    private enum class ModelType {
        CUBE
    }

}











