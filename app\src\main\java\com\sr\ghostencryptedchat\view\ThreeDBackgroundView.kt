package com.sr.ghostencryptedchat.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import java.util.Random
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

class ThreeDBackgroundView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val random = Random()
    private val paint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.FILL
    }

    private val models3D = mutableListOf<Model3D>()
    private val staticCubes = mutableListOf<CubeData>()

    // Colors for the 3D effect - deeper greens based on the image
    private val darkColor = Color.parseColor("#121212")
    private val accentColor = Color.parseColor("#aeff00") // Bright green
    private val midColor = Color.parseColor("#0A3B15") // Deeper green
    private val shadowColor = Color.parseColor("#0A0A0A")

    // Pre-calculated trigonometric values
    private val cosValues = FloatArray(360)
    private val sinValues = FloatArray(360)

    // No animation variables needed for static cubes

    init {
        // Set background color
        setBackgroundColor(darkColor)

        // Enable hardware acceleration
        setLayerType(LAYER_TYPE_HARDWARE, null)

        // Pre-calculate sin and cos values
        for (i in 0 until 360) {
            val radians = Math.toRadians(i.toDouble())
            cosValues[i] = cos(radians).toFloat()
            sinValues[i] = sin(radians).toFloat()
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // Create static cubes
        createStaticCubes(w, h)

        // Force a redraw
        invalidate()
    }



    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // Draw proper 3D wireframe cubes
        for (cube in staticCubes) {
            drawProper3DCube(canvas, cube)
        }
    }





    private fun createStaticCubes(w: Int, h: Int) {
        models3D.clear()

        // Create 5-7 proper 3D wireframe cubes
        val cubeCount = 5 + random.nextInt(3) // 5-7 cubes

        for (i in 0 until cubeCount) {
            // Random positions avoiding center area
            val centerX = if (random.nextBoolean()) {
                // Left or right side
                if (random.nextBoolean()) random.nextInt(w / 4).toFloat()
                else (w * 3 / 4 + random.nextInt(w / 4)).toFloat()
            } else {
                // Top or bottom area
                random.nextInt(w).toFloat()
            }

            val centerY = random.nextInt(h).toFloat()
            val centerZ = random.nextFloat() * 100f - 50f // Moderate Z range

            // Cube size
            val size = 30f + random.nextFloat() * 20f // 30-50px

            // Create a proper 3D wireframe cube
            createProper3DCube(centerX, centerY, centerZ, size)
        }
    }

    private fun createProper3DCube(centerX: Float, centerY: Float, centerZ: Float, size: Float) {
        // Create 8 vertices of a cube in 3D space
        val vertices = mutableListOf<PointF>()
        val halfSize = size * 0.5f

        // Define the 8 corners of a cube
        val cubeVertices = listOf(
            Triple(-halfSize, -halfSize, -halfSize), // 0: front-bottom-left
            Triple(halfSize, -halfSize, -halfSize),  // 1: front-bottom-right
            Triple(halfSize, halfSize, -halfSize),   // 2: front-top-right
            Triple(-halfSize, halfSize, -halfSize),  // 3: front-top-left
            Triple(-halfSize, -halfSize, halfSize),  // 4: back-bottom-left
            Triple(halfSize, -halfSize, halfSize),   // 5: back-bottom-right
            Triple(halfSize, halfSize, halfSize),    // 6: back-top-right
            Triple(-halfSize, halfSize, halfSize)    // 7: back-top-left
        )

        // Apply random rotation for 3D effect
        val rotX = random.nextFloat() * 60f + 15f // 15-75 degrees
        val rotY = random.nextFloat() * 60f + 15f // 15-75 degrees
        val rotZ = random.nextFloat() * 30f - 15f // -15 to 15 degrees

        // Transform and project each vertex
        for ((x, y, z) in cubeVertices) {
            val rotated = rotate3DPoint(x, y, z, rotX, rotY, rotZ)
            val projected = project3DPoint(
                rotated.first + centerX,
                rotated.second + centerY,
                rotated.third + centerZ
            )
            vertices.add(projected)
        }

        // Define the 12 edges of a cube
        val edges = listOf(
            // Front face
            Pair(0, 1), Pair(1, 2), Pair(2, 3), Pair(3, 0),
            // Back face
            Pair(4, 5), Pair(5, 6), Pair(6, 7), Pair(7, 4),
            // Connecting edges
            Pair(0, 4), Pair(1, 5), Pair(2, 6), Pair(3, 7)
        )

        // Calculate alpha based on depth
        val alpha = (150 + (centerZ + 50f) * 2).toInt().coerceIn(100, 200)

        // Store the cube for rendering
        val cubeData = CubeData(vertices, edges, alpha, size)
        staticCubes.add(cubeData)
    }

    private fun rotate3DPoint(x: Float, y: Float, z: Float, rotX: Float, rotY: Float, rotZ: Float): Triple<Float, Float, Float> {
        var newX = x
        var newY = y
        var newZ = z

        // Rotate around X axis
        val cosX = cos(Math.toRadians(rotX.toDouble())).toFloat()
        val sinX = sin(Math.toRadians(rotX.toDouble())).toFloat()
        val tempY = newY * cosX - newZ * sinX
        val tempZ = newY * sinX + newZ * cosX
        newY = tempY
        newZ = tempZ

        // Rotate around Y axis
        val cosY = cos(Math.toRadians(rotY.toDouble())).toFloat()
        val sinY = sin(Math.toRadians(rotY.toDouble())).toFloat()
        val tempX = newX * cosY + newZ * sinY
        val tempZ2 = -newX * sinY + newZ * cosY
        newX = tempX
        newZ = tempZ2

        // Rotate around Z axis
        val cosZ = cos(Math.toRadians(rotZ.toDouble())).toFloat()
        val sinZ = sin(Math.toRadians(rotZ.toDouble())).toFloat()
        val tempX2 = newX * cosZ - newY * sinZ
        val tempY2 = newX * sinZ + newY * cosZ
        newX = tempX2
        newY = tempY2

        return Triple(newX, newY, newZ)
    }

    private fun project3DPoint(x: Float, y: Float, z: Float): PointF {
        val distance = 400f
        val scale = distance / (distance + z)
        return PointF(x * scale, y * scale)
    }

    private fun drawProper3DCube(canvas: Canvas, cube: CubeData) {
        // Set up paint for wireframe
        paint.style = Paint.Style.STROKE
        paint.color = accentColor
        paint.shader = null

        // Draw each edge with glow effect
        for ((startIdx, endIdx) in cube.edges) {
            val start = cube.vertices[startIdx]
            val end = cube.vertices[endIdx]

            // Check if line is reasonable length
            val dx = end.x - start.x
            val dy = end.y - start.y
            val lineLength = sqrt(dx * dx + dy * dy)

            if (lineLength > cube.size * 3 || lineLength < 2f) {
                continue
            }

            // Outer glow
            paint.strokeWidth = 5f
            paint.alpha = (cube.alpha * 0.4f).toInt()
            paint.setShadowLayer(10f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)

            // Middle glow
            paint.strokeWidth = 3f
            paint.alpha = (cube.alpha * 0.7f).toInt()
            paint.setShadowLayer(6f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)

            // Core line
            paint.strokeWidth = 1.5f
            paint.alpha = cube.alpha
            paint.setShadowLayer(3f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)
        }

        // Reset paint
        paint.clearShadowLayer()
        paint.style = Paint.Style.FILL
    }










    // 3D Cube data class
    private data class CubeData(
        val vertices: List<PointF>,
        val edges: List<Pair<Int, Int>>,
        val alpha: Int,
        val size: Float
    )

    // Legacy 3D Model classes (kept for compatibility)
    private data class Vertex3D(var x: Float, var y: Float, var z: Float)
    private data class Edge3D(val start: Int, val end: Int)

    private inner class Model3D(
        var centerX: Float,
        var centerY: Float,
        var centerZ: Float,
        val size: Float,
        var rotationX: Float,
        var rotationY: Float,
        var rotationZ: Float,
        val rotationSpeedX: Float,
        val rotationSpeedY: Float,
        val rotationSpeedZ: Float,
        val driftSpeedX: Float,
        val driftSpeedY: Float,
        val driftSpeedZ: Float,
        val originalX: Float,
        val originalY: Float,
        val originalZ: Float,
        val modelType: ModelType,
        val alpha: Int
    ) {
        val vertices = mutableListOf<Vertex3D>()
        val edges = mutableListOf<Edge3D>()

        init {
            createModel()
        }

        private fun createModel() {
            when (modelType) {
                ModelType.CUBE -> createCube()
            }
        }

        private fun createCube() {
            // Define cube vertices (ORIGINAL)
            vertices.addAll(listOf(
                Vertex3D(-size, -size, -size), // 0
                Vertex3D(size, -size, -size),  // 1
                Vertex3D(size, size, -size),   // 2
                Vertex3D(-size, size, -size),  // 3
                Vertex3D(-size, -size, size),  // 4
                Vertex3D(size, -size, size),   // 5
                Vertex3D(size, size, size),    // 6
                Vertex3D(-size, size, size)    // 7
            ))

            // Define cube edges (ORIGINAL)
            edges.addAll(listOf(
                // Front face
                Edge3D(0, 1), Edge3D(1, 2), Edge3D(2, 3), Edge3D(3, 0),
                // Back face
                Edge3D(4, 5), Edge3D(5, 6), Edge3D(6, 7), Edge3D(7, 4),
                // Connecting edges
                Edge3D(0, 4), Edge3D(1, 5), Edge3D(2, 6), Edge3D(3, 7)
            ))
        }


    }

    private enum class ModelType {
        CUBE
    }

}











