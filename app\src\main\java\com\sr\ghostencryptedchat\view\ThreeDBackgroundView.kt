package com.sr.ghostencryptedchat.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.Log
import android.view.View
import kotlinx.coroutines.*
import java.io.BufferedReader
import java.io.InputStreamReader
import java.util.Random
import kotlin.math.abs
import kotlin.math.cos
import kotlin.math.sin

class ThreeDBackgroundView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val random = Random()
    private val paint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.FILL
    }
    
    private val models3D = mutableListOf<Model3D>()

    // Coroutine scope for async operations
    private val viewScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    // PNG ghost bitmap
    private var ghostBitmap: Bitmap? = null

    // Colors for the 3D effect - deeper greens based on the image
    private val darkColor = Color.parseColor("#121212")
    private val accentColor = Color.parseColor("#aeff00") // Bright green
    private val midColor = Color.parseColor("#0A3B15") // Deeper green
    private val shadowColor = Color.parseColor("#0A0A0A")
    
    // Pre-calculated trigonometric values
    private val cosValues = FloatArray(360)
    private val sinValues = FloatArray(360)

    // Animation variables - smoother and faster
    private var animationTime = 0f
    private var lastFrameTime = System.currentTimeMillis()
    private val animationSpeed = 1.0f // Smooth, fluid animation
    private var isAnimating = false
    
    init {
        // Set background color
        setBackgroundColor(darkColor)
        
        // Enable hardware acceleration
        setLayerType(LAYER_TYPE_HARDWARE, null)
        
        // Pre-calculate sin and cos values
        for (i in 0 until 360) {
            val radians = Math.toRadians(i.toDouble())
            cosValues[i] = cos(radians).toFloat()
            sinValues[i] = sin(radians).toFloat()
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // Create 3D models asynchronously to avoid blocking UI
        viewScope.launch {
            create3DModels(w, h)
            // Start animation
            isAnimating = true
            // Force a redraw with the new pattern
            invalidate()
        }
    }


    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // Only animate if we have models and view is visible
        if (models3D.isEmpty() || !isShown) {
            return
        }

        // Update animation
        val currentTime = System.currentTimeMillis()
        val deltaTime = (currentTime - lastFrameTime) / 16.67f // Normalize to 60fps
        lastFrameTime = currentTime
        animationTime += deltaTime * animationSpeed

        // Update 3D models
        update3DModels(deltaTime)

        // Draw 3D models
        for (model in models3D) {
            draw3DModel(canvas, model)
        }

        // Request next frame for smooth animation only if visible
        if (isShown && isAnimating) {
            invalidate()
        }
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        isAnimating = visibility == View.VISIBLE
        if (isAnimating) {
            invalidate()
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        isAnimating = false
        viewScope.cancel()
    }


    


    private suspend fun create3DModels(w: Int, h: Int) {
        models3D.clear()

        // Create 1 centered ghost model + 4-6 floating cubes around it
        val cubeCount = 4 + random.nextInt(3) // 4-6 cubes

        // Create the centered ghost model first
        createCenteredGhostModel(w, h)

        // Create floating cubes around the ghost
        for (i in 0 until cubeCount) {
            // Position cubes around the screen edges, not in center
            val centerX = if (random.nextBoolean()) {
                // Left or right side
                if (random.nextBoolean()) random.nextInt(w / 4).toFloat()
                else (w * 3 / 4 + random.nextInt(w / 4)).toFloat()
            } else {
                // Anywhere horizontally but avoid center
                random.nextInt(w).toFloat()
            }

            val centerY = random.nextInt(h).toFloat()
            val centerZ = random.nextFloat() * 200f - 100f // -100 to 100

            // Smaller cubes so they don't compete with the ghost
            val size = 20f + random.nextFloat() * 30f // 20-50px

            // Random rotation
            val rotationX = random.nextFloat() * 360f
            val rotationY = random.nextFloat() * 360f
            val rotationZ = random.nextFloat() * 360f

            // Gentle rotation speeds
            val rotationSpeedX = (random.nextFloat() - 0.5f) * 0.8f
            val rotationSpeedY = (random.nextFloat() - 0.5f) * 0.8f
            val rotationSpeedZ = (random.nextFloat() - 0.5f) * 0.8f

            // Gentle floating movement
            val driftSpeedX = (random.nextFloat() - 0.5f) * 0.3f
            val driftSpeedY = (random.nextFloat() - 0.5f) * 0.3f
            val driftSpeedZ = (random.nextFloat() - 0.5f) * 0.15f

            // Only cubes for simplicity and focus
            val modelType = ModelType.CUBE

            // Lower alpha so they don't overpower the ghost
            val alpha = 80 + random.nextInt(60) // 80-140 (more subtle)

            models3D.add(Model3D(
                centerX = centerX,
                centerY = centerY,
                centerZ = centerZ,
                size = size,
                rotationX = rotationX,
                rotationY = rotationY,
                rotationZ = rotationZ,
                rotationSpeedX = rotationSpeedX,
                rotationSpeedY = rotationSpeedY,
                rotationSpeedZ = rotationSpeedZ,
                driftSpeedX = driftSpeedX,
                driftSpeedY = driftSpeedY,
                driftSpeedZ = driftSpeedZ,
                originalX = centerX,
                originalY = centerY,
                originalZ = centerZ,
                modelType = modelType,
                alpha = alpha
            ))
        }

    }

    private suspend fun createCenteredGhostModel(w: Int, h: Int) {
        Log.d("ThreeDBackground", "Creating centered ghost model...")

        // Try PNG ghost first (for testing)
        if (tryCreatePNGGhost(w, h)) {
            Log.d("ThreeDBackground", "Successfully created PNG ghost")
            return
        }

        // Fallback to OBJ models
        Log.d("ThreeDBackground", "PNG ghost failed, trying OBJ models...")

        // Try to load the ghost model (simplified version first, then original)
        val loadedModel = withContext(Dispatchers.IO) {
            val objLoader = OBJLoader()

            // Try simplified ghost first
            try {
                val modelData = objLoader.loadModel("ghost_simple.obj", 150f)
                if (modelData.first.isNotEmpty()) {
                    Log.d("ThreeDBackground", "Successfully loaded ghost_simple.obj with ${modelData.first.size} vertices")
                    return@withContext modelData
                }
            } catch (e: Exception) {
                Log.w("ThreeDBackground", "Failed to load ghost_simple.obj: ${e.message}")
            }

            // Fallback to original ghost with smaller scale
            try {
                val modelData = objLoader.loadModel("ghost.obj", 100f) // Smaller scale for complex model
                if (modelData.first.isNotEmpty()) {
                    Log.d("ThreeDBackground", "Successfully loaded ghost.obj with ${modelData.first.size} vertices")
                    return@withContext modelData
                }
            } catch (e: Exception) {
                Log.e("ThreeDBackground", "Failed to load ghost.obj: ${e.message}")
            }

            null
        }

        // If we successfully loaded the ghost model, create it in the center
        loadedModel?.let { (vertices, edges) ->
            Log.d("ThreeDBackground", "Creating centered ghost with ${vertices.size} vertices and ${edges.size} edges")

            // Center position
            val centerX = w / 2f
            val centerY = h / 2f
            val centerZ = 0f // Centered in depth too

            // Fixed size for the main ghost
            val size = 80f // Large, prominent size

            // Start with random rotation
            val rotationX = 0f // No X rotation for cleaner look
            val rotationY = random.nextFloat() * 360f // Random starting Y rotation
            val rotationZ = 0f // No Z rotation for cleaner look

            // Slow, steady horizontal rotation (only Y-axis)
            val rotationSpeedX = 0f // No X rotation
            val rotationSpeedY = 0.3f // Slow, steady horizontal rotation
            val rotationSpeedZ = 0f // No Z rotation

            // No drift - keep it centered
            val driftSpeedX = 0f
            val driftSpeedY = 0f
            val driftSpeedZ = 0f

            // High alpha for prominence
            val alpha = 200 // Very visible

            // Create the centered ghost model
            val ghostModel = Model3D(
                centerX = centerX,
                centerY = centerY,
                centerZ = centerZ,
                size = size,
                rotationX = rotationX,
                rotationY = rotationY,
                rotationZ = rotationZ,
                rotationSpeedX = rotationSpeedX,
                rotationSpeedY = rotationSpeedY,
                rotationSpeedZ = rotationSpeedZ,
                driftSpeedX = driftSpeedX,
                driftSpeedY = driftSpeedY,
                driftSpeedZ = driftSpeedZ,
                originalX = centerX,
                originalY = centerY,
                originalZ = centerZ,
                modelType = ModelType.CUSTOM_OBJ,
                alpha = alpha
            )

            // Manually set the vertices and edges from the OBJ file
            ghostModel.vertices.clear()
            ghostModel.edges.clear()
            ghostModel.vertices.addAll(vertices)
            ghostModel.edges.addAll(edges)

            models3D.add(ghostModel)
            Log.d("ThreeDBackground", "Centered ghost model added successfully!")
        } ?: run {
            Log.w("ThreeDBackground", "Could not load ghost OBJ, creating procedural ghost")
            createProceduralGhost(w, h)
        }
    }

    private fun createProceduralGhost(w: Int, h: Int) {
        Log.d("ThreeDBackground", "Creating procedural ghost wireframe")

        // Create a simple ghost shape procedurally
        val vertices = mutableListOf<Vertex3D>()
        val edges = mutableListOf<Edge3D>()

        // Ghost head (circle-like)
        val headRadius = 40f
        val headPoints = 12
        for (i in 0 until headPoints) {
            val angle = (i * 360f / headPoints) * Math.PI / 180f
            val x = cos(angle).toFloat() * headRadius
            val y = sin(angle).toFloat() * headRadius + 60f // Offset up
            vertices.add(Vertex3D(x, y, 0f))
        }

        // Connect head points
        for (i in 0 until headPoints) {
            edges.add(Edge3D(i, (i + 1) % headPoints))
        }

        // Ghost body (rectangular with wavy bottom)
        val bodyWidth = 50f
        val bodyHeight = 80f

        // Body corners
        vertices.add(Vertex3D(-bodyWidth, 20f, 0f))  // 12 - Left top
        vertices.add(Vertex3D(bodyWidth, 20f, 0f))   // 13 - Right top
        vertices.add(Vertex3D(-bodyWidth, -60f, 0f)) // 14 - Left bottom
        vertices.add(Vertex3D(bodyWidth, -60f, 0f))  // 15 - Right bottom

        // Body outline
        edges.add(Edge3D(12, 13)) // Top
        edges.add(Edge3D(12, 14)) // Left side
        edges.add(Edge3D(13, 15)) // Right side

        // Wavy bottom (classic ghost shape)
        val wavePoints = 6
        for (i in 0 until wavePoints) {
            val x = -bodyWidth + (i * (bodyWidth * 2) / (wavePoints - 1))
            val y = if (i % 2 == 0) -60f else -80f // Alternating wave
            vertices.add(Vertex3D(x, y, 0f))
        }

        // Connect wavy bottom
        val waveStart = vertices.size - wavePoints
        for (i in 0 until wavePoints - 1) {
            edges.add(Edge3D(waveStart + i, waveStart + i + 1))
        }

        // Connect body to wave
        edges.add(Edge3D(14, waveStart)) // Left body to wave start
        edges.add(Edge3D(15, waveStart + wavePoints - 1)) // Right body to wave end

        // Eyes (simple dots)
        vertices.add(Vertex3D(-15f, 40f, 5f)) // Left eye
        vertices.add(Vertex3D(15f, 40f, 5f))  // Right eye
        val leftEye = vertices.size - 2
        val rightEye = vertices.size - 1

        // Eye "pupils" (small lines)
        vertices.add(Vertex3D(-15f, 35f, 5f)) // Left eye bottom
        vertices.add(Vertex3D(15f, 35f, 5f))  // Right eye bottom
        edges.add(Edge3D(leftEye, vertices.size - 2))
        edges.add(Edge3D(rightEye, vertices.size - 1))

        // Mouth (simple curve)
        vertices.add(Vertex3D(-10f, 15f, 5f)) // Mouth left
        vertices.add(Vertex3D(0f, 10f, 5f))   // Mouth center
        vertices.add(Vertex3D(10f, 15f, 5f))  // Mouth right
        val mouthStart = vertices.size - 3
        edges.add(Edge3D(mouthStart, mouthStart + 1))
        edges.add(Edge3D(mouthStart + 1, mouthStart + 2))

        Log.d("ThreeDBackground", "Created procedural ghost with ${vertices.size} vertices and ${edges.size} edges")

        // Create the centered ghost model
        val centerX = w / 2f
        val centerY = h / 2f
        val centerZ = 0f

        val ghostModel = Model3D(
            centerX = centerX,
            centerY = centerY,
            centerZ = centerZ,
            size = 80f,
            rotationX = 0f,
            rotationY = random.nextFloat() * 360f,
            rotationZ = 0f,
            rotationSpeedX = 0f,
            rotationSpeedY = 0.3f, // Slow horizontal rotation
            rotationSpeedZ = 0f,
            driftSpeedX = 0f,
            driftSpeedY = 0f,
            driftSpeedZ = 0f,
            originalX = centerX,
            originalY = centerY,
            originalZ = centerZ,
            modelType = ModelType.CUSTOM_OBJ,
            alpha = 200
        )

        // Set the procedural vertices and edges
        ghostModel.vertices.clear()
        ghostModel.edges.clear()
        ghostModel.vertices.addAll(vertices)
        ghostModel.edges.addAll(edges)

        models3D.add(ghostModel)
        Log.d("ThreeDBackground", "Procedural ghost added successfully!")
    }

    private suspend fun tryCreatePNGGhost(w: Int, h: Int): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // Try to load ghost PNG from assets
                val inputStream = context.assets.open("images/ghost.png")
                val bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream.close()

                if (bitmap != null) {
                    ghostBitmap = bitmap
                    Log.d("ThreeDBackground", "Loaded ghost.png: ${bitmap.width}x${bitmap.height}")

                    // Create PNG ghost model on main thread
                    withContext(Dispatchers.Main) {
                        createPNGGhostModel(w, h, bitmap)
                    }
                    true
                } else {
                    Log.w("ThreeDBackground", "Failed to decode ghost.png")
                    false
                }
            } catch (e: Exception) {
                Log.w("ThreeDBackground", "Could not load ghost.png: ${e.message}")
                false
            }
        }
    }

    private fun createPNGGhostModel(w: Int, h: Int, bitmap: Bitmap) {
        Log.d("ThreeDBackground", "Creating PNG ghost model")

        // Center position
        val centerX = w / 2f
        val centerY = h / 2f
        val centerZ = 0f

        // Create PNG ghost model
        val ghostModel = Model3D(
            centerX = centerX,
            centerY = centerY,
            centerZ = centerZ,
            size = 100f, // Size for PNG scaling
            rotationX = 0f,
            rotationY = random.nextFloat() * 360f,
            rotationZ = 0f,
            rotationSpeedX = 0f,
            rotationSpeedY = 0.3f, // Slow horizontal rotation
            rotationSpeedZ = 0f,
            driftSpeedX = 0f,
            driftSpeedY = 0f,
            driftSpeedZ = 0f,
            originalX = centerX,
            originalY = centerY,
            originalZ = centerZ,
            modelType = ModelType.PNG_GHOST,
            alpha = 200
        )

        // No vertices/edges needed for PNG model
        models3D.add(ghostModel)
        Log.d("ThreeDBackground", "PNG ghost model added successfully!")
    }

    private fun update3DModels(deltaTime: Float) {
        for (model in models3D) {
            // Smooth rotation updates
            model.rotationX += model.rotationSpeedX * deltaTime
            model.rotationY += model.rotationSpeedY * deltaTime
            model.rotationZ += model.rotationSpeedZ * deltaTime

            // Keep rotations in bounds
            model.rotationX = model.rotationX % 360f
            model.rotationY = model.rotationY % 360f
            model.rotationZ = model.rotationZ % 360f

            // Different movement for ghost vs cubes
            if (model.modelType == ModelType.CUSTOM_OBJ || model.modelType == ModelType.PNG_GHOST) {
                // Ghost stays centered, only rotates
                model.centerX = model.originalX
                model.centerY = model.originalY
                model.centerZ = model.originalZ
            } else {
                // Cubes float around with organic movement
                val floatOffsetX = sin(animationTime * 0.008f + model.originalX * 0.001f) * 25f +
                                 cos(animationTime * 0.003f + model.originalX * 0.0005f) * 10f
                val floatOffsetY = cos(animationTime * 0.006f + model.originalY * 0.001f) * 20f +
                                 sin(animationTime * 0.004f + model.originalY * 0.0008f) * 8f
                val floatOffsetZ = sin(animationTime * 0.005f + model.originalZ * 0.001f) * 30f +
                                 cos(animationTime * 0.007f + model.originalZ * 0.0006f) * 15f

                model.centerX = model.originalX + model.driftSpeedX * animationTime + floatOffsetX
                model.centerY = model.originalY + model.driftSpeedY * animationTime + floatOffsetY
                model.centerZ = model.originalZ + model.driftSpeedZ * animationTime + floatOffsetZ

                // Smooth screen wrapping for cubes only
                if (model.centerX < -model.size * 3) {
                    model.centerX = width.toFloat() + model.size * 3
                } else if (model.centerX > width + model.size * 3) {
                    model.centerX = -model.size * 3
                }

                if (model.centerY < -model.size * 3) {
                    model.centerY = height.toFloat() + model.size * 3
                } else if (model.centerY > height + model.size * 3) {
                    model.centerY = -model.size * 3
                }
            }
        }
    }

    private fun draw3DModel(canvas: Canvas, model: Model3D) {
        if (model.modelType == ModelType.PNG_GHOST) {
            drawPNGGhost(canvas, model)
            return
        }

        // Transform and project vertices for wireframe models
        val projectedVertices = mutableListOf<PointF>()

        for (vertex in model.vertices) {
            // Apply rotations
            val rotatedVertex = rotateVertex(vertex, model.rotationX, model.rotationY, model.rotationZ)

            // Project to 2D
            val projected = project3DTo2D(
                rotatedVertex.x + model.centerX,
                rotatedVertex.y + model.centerY,
                rotatedVertex.z + model.centerZ
            )
            projectedVertices.add(projected)
        }

        // Calculate distance-based alpha for depth effect
        val avgZ = model.centerZ
        val depthAlpha = (model.alpha * (1.0f - (avgZ + 150f) / 300f)).toInt().coerceIn(50, 255)

        // Enhanced wireframe rendering with multiple glow layers
        paint.style = Paint.Style.STROKE
        paint.color = accentColor
        paint.shader = null

        // Draw edges with layered glow effect
        for (edge in model.edges) {
            val start = projectedVertices[edge.start]
            val end = projectedVertices[edge.end]

            // Outer glow (thicker, more transparent)
            paint.strokeWidth = 6f
            paint.alpha = (depthAlpha * 0.3f).toInt()
            paint.setShadowLayer(12f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)

            // Middle glow
            paint.strokeWidth = 3f
            paint.alpha = (depthAlpha * 0.6f).toInt()
            paint.setShadowLayer(6f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)

            // Core line (sharp and bright)
            paint.strokeWidth = 1.5f
            paint.alpha = depthAlpha
            paint.setShadowLayer(3f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)
        }

        // Reset paint
        paint.clearShadowLayer()
        paint.style = Paint.Style.FILL
    }

    private fun drawPNGGhost(canvas: Canvas, model: Model3D) {
        ghostBitmap?.let { bitmap ->
            // Calculate 3D transformation matrix
            val matrix = Matrix()

            // Scale based on model size
            val scale = model.size / 100f
            matrix.postScale(scale, scale)

            // Apply Y-axis rotation (3D effect)
            val rotationY = model.rotationY
            val perspective = cos(Math.toRadians(rotationY.toDouble())).toFloat()
            val skewX = sin(Math.toRadians(rotationY.toDouble())).toFloat() * 0.3f

            // Create 3D perspective effect
            matrix.postSkew(skewX, 0f)
            matrix.postScale(abs(perspective), 1f) // Flatten when viewed from side

            // Center the bitmap
            val bitmapCenterX = bitmap.width / 2f
            val bitmapCenterY = bitmap.height / 2f
            matrix.postTranslate(-bitmapCenterX, -bitmapCenterY)
            matrix.postTranslate(model.centerX, model.centerY)

            // Set up paint with green tint and glow
            paint.reset()
            paint.isAntiAlias = true
            paint.alpha = model.alpha

            // Apply green color filter for cyberpunk effect
            val colorFilter = PorterDuffColorFilter(accentColor, PorterDuff.Mode.MULTIPLY)
            paint.colorFilter = colorFilter

            // Add glow effect
            paint.setShadowLayer(15f, 0f, 0f, accentColor)

            // Draw the transformed bitmap
            canvas.drawBitmap(bitmap, matrix, paint)

            // Reset paint
            paint.clearShadowLayer()
            paint.colorFilter = null
        }
    }

    private fun rotateVertex(vertex: Vertex3D, rotX: Float, rotY: Float, rotZ: Float): Vertex3D {
        var x = vertex.x
        var y = vertex.y
        var z = vertex.z

        // Rotate around X axis
        val cosX = cos(Math.toRadians(rotX.toDouble())).toFloat()
        val sinX = sin(Math.toRadians(rotX.toDouble())).toFloat()
        val newY = y * cosX - z * sinX
        val newZ = y * sinX + z * cosX
        y = newY
        z = newZ

        // Rotate around Y axis
        val cosY = cos(Math.toRadians(rotY.toDouble())).toFloat()
        val sinY = sin(Math.toRadians(rotY.toDouble())).toFloat()
        val newX = x * cosY + z * sinY
        val newZ2 = -x * sinY + z * cosY
        x = newX
        z = newZ2

        // Rotate around Z axis
        val cosZ = cos(Math.toRadians(rotZ.toDouble())).toFloat()
        val sinZ = sin(Math.toRadians(rotZ.toDouble())).toFloat()
        val newX2 = x * cosZ - y * sinZ
        val newY2 = x * sinZ + y * cosZ
        x = newX2
        y = newY2

        return Vertex3D(x, y, z)
    }

    private fun project3DTo2D(x: Float, y: Float, z: Float): PointF {
        // Simple perspective projection
        val distance = 400f
        val scale = distance / (distance + z)
        return PointF(x * scale, y * scale)
    }



    // 3D Model classes and methods
    private data class Vertex3D(var x: Float, var y: Float, var z: Float)
    private data class Edge3D(val start: Int, val end: Int)

    private inner class Model3D(
        var centerX: Float,
        var centerY: Float,
        var centerZ: Float,
        val size: Float,
        var rotationX: Float,
        var rotationY: Float,
        var rotationZ: Float,
        val rotationSpeedX: Float,
        val rotationSpeedY: Float,
        val rotationSpeedZ: Float,
        val driftSpeedX: Float,
        val driftSpeedY: Float,
        val driftSpeedZ: Float,
        val originalX: Float,
        val originalY: Float,
        val originalZ: Float,
        val modelType: ModelType,
        val alpha: Int
    ) {
        val vertices = mutableListOf<Vertex3D>()
        val edges = mutableListOf<Edge3D>()

        init {
            createModel()
        }

        private fun createModel() {
            when (modelType) {
                ModelType.CUBE -> createCube()
                ModelType.CRYSTAL -> createCrystal()
                ModelType.PYRAMID -> createPyramid()
                ModelType.CUSTOM_OBJ -> {
                    // Custom OBJ models will be loaded separately
                    // This case is handled in the create3DModels function
                }
                ModelType.PNG_GHOST -> {
                    // PNG ghost models don't need vertices/edges
                    // Rendering is handled separately in drawPNGGhost
                }
            }
        }

        private fun createCube() {
            // Define cube vertices
            vertices.addAll(listOf(
                Vertex3D(-size, -size, -size), // 0
                Vertex3D(size, -size, -size),  // 1
                Vertex3D(size, size, -size),   // 2
                Vertex3D(-size, size, -size),  // 3
                Vertex3D(-size, -size, size),  // 4
                Vertex3D(size, -size, size),   // 5
                Vertex3D(size, size, size),    // 6
                Vertex3D(-size, size, size)    // 7
            ))

            // Define cube edges
            edges.addAll(listOf(
                // Front face
                Edge3D(0, 1), Edge3D(1, 2), Edge3D(2, 3), Edge3D(3, 0),
                // Back face
                Edge3D(4, 5), Edge3D(5, 6), Edge3D(6, 7), Edge3D(7, 4),
                // Connecting edges
                Edge3D(0, 4), Edge3D(1, 5), Edge3D(2, 6), Edge3D(3, 7)
            ))
        }

        private fun createCrystal() {
            // Define crystal vertices (octahedron-like)
            vertices.addAll(listOf(
                Vertex3D(0f, -size * 1.5f, 0f),    // Top point
                Vertex3D(-size, 0f, -size),        // Front left
                Vertex3D(size, 0f, -size),         // Front right
                Vertex3D(size, 0f, size),          // Back right
                Vertex3D(-size, 0f, size),         // Back left
                Vertex3D(0f, size * 1.5f, 0f)     // Bottom point
            ))

            // Define crystal edges
            edges.addAll(listOf(
                // Top pyramid
                Edge3D(0, 1), Edge3D(0, 2), Edge3D(0, 3), Edge3D(0, 4),
                // Middle square
                Edge3D(1, 2), Edge3D(2, 3), Edge3D(3, 4), Edge3D(4, 1),
                // Bottom pyramid
                Edge3D(5, 1), Edge3D(5, 2), Edge3D(5, 3), Edge3D(5, 4)
            ))
        }

        private fun createPyramid() {
            // Define pyramid vertices
            vertices.addAll(listOf(
                Vertex3D(0f, -size * 1.2f, 0f),   // Top point
                Vertex3D(-size, size, -size),      // Base front left
                Vertex3D(size, size, -size),       // Base front right
                Vertex3D(size, size, size),        // Base back right
                Vertex3D(-size, size, size)        // Base back left
            ))

            // Define pyramid edges
            edges.addAll(listOf(
                // Base
                Edge3D(1, 2), Edge3D(2, 3), Edge3D(3, 4), Edge3D(4, 1),
                // Sides to top
                Edge3D(0, 1), Edge3D(0, 2), Edge3D(0, 3), Edge3D(0, 4)
            ))
        }
    }

    private enum class ModelType {
        CUBE, CRYSTAL, PYRAMID, CUSTOM_OBJ, PNG_GHOST
    }

    // OBJ Model Loader
    private inner class OBJLoader {
        fun loadModel(fileName: String, size: Float): Pair<List<Vertex3D>, List<Edge3D>> {
            val vertices = mutableListOf<Vertex3D>()
            val faces = mutableListOf<List<Int>>()

            try {
                val inputStream = context.assets.open("models/$fileName")
                val reader = BufferedReader(InputStreamReader(inputStream))

                var lineCount = 0
                var vertexCount = 0
                var faceCount = 0
                reader.useLines { lines ->
                    lines.forEach { line ->
                        lineCount++
                        // Limit processing to prevent performance issues with huge models
                        if (lineCount > 5000) {
                            Log.w("OBJLoader", "File $fileName too large, stopping at 5000 lines")
                            return@forEach
                        }
                        // Limit vertices for performance
                        if (vertexCount > 500) {
                            Log.w("OBJLoader", "Too many vertices, stopping at 500")
                            return@forEach
                        }

                        val trimmedLine = line.trim()
                        when {
                            trimmedLine.startsWith("v ") && !trimmedLine.startsWith("vt ") && !trimmedLine.startsWith("vn ") -> {
                                // Parse vertex: v x y z
                                try {
                                    val parts = trimmedLine.split("\\s+".toRegex())
                                    if (parts.size >= 4) {
                                        val x = parts[1].toFloatOrNull() ?: 0f
                                        val y = parts[2].toFloatOrNull() ?: 0f
                                        val z = parts[3].toFloatOrNull() ?: 0f
                                        vertices.add(Vertex3D(x * size, y * size, z * size))
                                        vertexCount++
                                    }
                                } catch (e: Exception) {
                                    Log.w("OBJLoader", "Error parsing vertex line: $trimmedLine")
                                }
                            }
                            trimmedLine.startsWith("f ") -> {
                                // Parse face: f v1 v2 v3 or f v1/vt1/vn1 v2/vt2/vn2 v3/vt3/vn3
                                try {
                                    val parts = trimmedLine.split("\\s+".toRegex())
                                    if (parts.size >= 4 && faceCount < 1000) { // Limit faces too
                                        val faceVertices = parts.drop(1).mapNotNull { part ->
                                            try {
                                                val vertexIndex = part.split("/")[0].toInt() - 1 // OBJ indices start at 1
                                                if (vertexIndex >= 0 && vertexIndex < vertices.size) vertexIndex else null
                                            } catch (e: Exception) {
                                                null
                                            }
                                        }
                                        if (faceVertices.size >= 3) {
                                            faces.add(faceVertices)
                                            faceCount++
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.w("OBJLoader", "Error parsing face line: $trimmedLine")
                                }
                            }
                        }
                    }
                }
                reader.close()

                Log.d("OBJLoader", "Loaded ${vertices.size} vertices and ${faces.size} faces from $fileName")

            } catch (e: Exception) {
                Log.e("OBJLoader", "Error loading OBJ file $fileName: ${e.message}")
                // Return empty lists if loading fails
                return Pair(emptyList(), emptyList())
            }

            // Convert faces to edges for wireframe rendering
            val edges = mutableSetOf<Edge3D>()

            for (face in faces) {
                // Create edges for each face (connect consecutive vertices)
                for (i in face.indices) {
                    val currentVertex = face[i]
                    val nextVertex = face[(i + 1) % face.size]

                    // Add edge (avoid duplicates by using Set)
                    val edge1 = Edge3D(currentVertex, nextVertex)
                    val edge2 = Edge3D(nextVertex, currentVertex)

                    // Only add if neither direction exists
                    if (!edges.contains(edge2)) {
                        edges.add(edge1)
                    }
                }
            }

            return Pair(vertices, edges.toList())
        }
    }
}











