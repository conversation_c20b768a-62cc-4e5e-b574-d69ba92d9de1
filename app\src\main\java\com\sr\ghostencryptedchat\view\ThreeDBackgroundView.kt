package com.sr.ghostencryptedchat.view

import android.content.Context
import android.graphics.*
import android.media.MediaPlayer
import android.net.Uri
import android.util.AttributeSet
import android.util.Log
import android.view.SurfaceHolder
import android.view.SurfaceView

class ThreeDBackgroundView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : SurfaceView(context, attrs, defStyleAttr), SurfaceHolder.Callback {

    private var mediaPlayer: MediaPlayer? = null
    private var isVideoReady = false

    init {
        // Set up SurfaceView for video playback
        holder.addCallback(this)
        setZOrderOnTop(false) // Video behind other content
        holder.setFormat(PixelFormat.TRANSLUCENT)

        Log.d("VideoBackground", "ThreeDBackgroundView initialized")
    }

    override fun surfaceCreated(holder: SurfaceHolder) {
        Log.d("VideoBackground", "Surface created")
        setupVideoPlayer()
    }

    override fun surfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
        Log.d("VideoBackground", "Surface changed: ${width}x${height}")
        // Video will auto-scale to surface size
    }

    override fun surfaceDestroyed(holder: SurfaceHolder) {
        Log.d("VideoBackground", "Surface destroyed")
        releaseVideoPlayer()
    }





    private fun setupVideoPlayer() {
        try {
            // Release any existing player
            releaseVideoPlayer()

            // Create new MediaPlayer
            mediaPlayer = MediaPlayer().apply {
                // Try to load background.mp4 from assets
                val assetFileDescriptor = context.assets.openFd("background.mp4")
                setDataSource(assetFileDescriptor.fileDescriptor, assetFileDescriptor.startOffset, assetFileDescriptor.length)
                assetFileDescriptor.close()

                // Set display
                setDisplay(holder)

                // Configure playback
                isLooping = true
                setVideoScalingMode(MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING)

                // Set listeners
                setOnPreparedListener { player ->
                    Log.d("VideoBackground", "Video prepared successfully")
                    isVideoReady = true
                    player.start()
                }

                setOnErrorListener { _, what, extra ->
                    Log.e("VideoBackground", "MediaPlayer error: what=$what, extra=$extra")
                    isVideoReady = false
                    false
                }

                setOnVideoSizeChangedListener { _, width, height ->
                    Log.d("VideoBackground", "Video size: ${width}x${height}")
                }

                // Prepare asynchronously
                prepareAsync()
            }

            Log.d("VideoBackground", "MediaPlayer setup initiated")

        } catch (e: Exception) {
            Log.e("VideoBackground", "Failed to setup video player: ${e.message}")
            isVideoReady = false
        }
    }

    private fun releaseVideoPlayer() {
        try {
            mediaPlayer?.apply {
                if (isPlaying) {
                    stop()
                }
                reset()
                release()
            }
            mediaPlayer = null
            isVideoReady = false
            Log.d("VideoBackground", "Video player released")
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error releasing video player: ${e.message}")
        }
    }

    fun pauseVideo() {
        try {
            if (isVideoReady && mediaPlayer?.isPlaying == true) {
                mediaPlayer?.pause()
                Log.d("VideoBackground", "Video paused")
            }
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error pausing video: ${e.message}")
        }
    }

    fun resumeVideo() {
        try {
            if (isVideoReady && mediaPlayer?.isPlaying == false) {
                mediaPlayer?.start()
                Log.d("VideoBackground", "Video resumed")
            }
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error resuming video: ${e.message}")
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        releaseVideoPlayer()
    }










}











