package com.sr.ghostencryptedchat.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import java.util.Random
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

class ThreeDBackgroundView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val random = Random()
    private val paint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.FILL
    }

    private val models3D = mutableListOf<Model3D>()

    // Colors for the 3D effect - deeper greens based on the image
    private val darkColor = Color.parseColor("#121212")
    private val accentColor = Color.parseColor("#aeff00") // Bright green
    private val midColor = Color.parseColor("#0A3B15") // Deeper green
    private val shadowColor = Color.parseColor("#0A0A0A")

    // Pre-calculated trigonometric values
    private val cosValues = FloatArray(360)
    private val sinValues = FloatArray(360)

    // No animation variables needed for static cubes

    init {
        // Set background color
        setBackgroundColor(darkColor)

        // Enable hardware acceleration
        setLayerType(LAYER_TYPE_HARDWARE, null)

        // Pre-calculate sin and cos values
        for (i in 0 until 360) {
            val radians = Math.toRadians(i.toDouble())
            cosValues[i] = cos(radians).toFloat()
            sinValues[i] = sin(radians).toFloat()
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // Create static cubes
        createStaticCubes(w, h)

        // Force a redraw
        invalidate()
    }



    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // Draw static cubes (no animation)
        for (model in models3D) {
            drawStaticCube(canvas, model)
        }
    }





    private fun createStaticCubes(w: Int, h: Int) {
        models3D.clear()

        // Create 5-7 static cubes (same as before, just no animation)
        val cubeCount = 5 + random.nextInt(3) // 5-7 cubes

        for (i in 0 until cubeCount) {
            // Position cubes around the screen edges, not in center (same as before)
            val centerX = if (random.nextBoolean()) {
                // Left or right side
                if (random.nextBoolean()) random.nextInt(w / 4).toFloat()
                else (w * 3 / 4 + random.nextInt(w / 4)).toFloat()
            } else {
                // Anywhere horizontally but avoid center
                random.nextInt(w).toFloat()
            }

            val centerY = random.nextInt(h).toFloat()
            val centerZ = random.nextFloat() * 200f - 100f // Original Z range

            // Original cube sizes
            val size = 25f + random.nextFloat() * 15f // 25-40px (original range)

            // Random rotation (original)
            val rotationX = random.nextFloat() * 360f
            val rotationY = random.nextFloat() * 360f
            val rotationZ = random.nextFloat() * 360f

            // No animation speeds (static)
            val rotationSpeedX = 0f
            val rotationSpeedY = 0f
            val rotationSpeedZ = 0f

            // No movement (static)
            val driftSpeedX = 0f
            val driftSpeedY = 0f
            val driftSpeedZ = 0f

            // Only cubes
            val modelType = ModelType.CUBE

            // Original alpha range
            val alpha = 80 + random.nextInt(60) // 80-140 (original)

            models3D.add(Model3D(
                centerX = centerX,
                centerY = centerY,
                centerZ = centerZ,
                size = size,
                rotationX = rotationX,
                rotationY = rotationY,
                rotationZ = rotationZ,
                rotationSpeedX = rotationSpeedX,
                rotationSpeedY = rotationSpeedY,
                rotationSpeedZ = rotationSpeedZ,
                driftSpeedX = driftSpeedX,
                driftSpeedY = driftSpeedY,
                driftSpeedZ = driftSpeedZ,
                originalX = centerX,
                originalY = centerY,
                originalZ = centerZ,
                modelType = modelType,
                alpha = alpha
            ))
        }
    }



    private fun drawStaticCube(canvas: Canvas, model: Model3D) {
        // Transform and project vertices (same as original)
        val projectedVertices = mutableListOf<PointF>()

        for (vertex in model.vertices) {
            // Apply rotations (same as original)
            val rotatedVertex = rotateVertex(vertex, model.rotationX, model.rotationY, model.rotationZ)

            // Project to 2D (same as original)
            val projected = project3DTo2D(
                rotatedVertex.x + model.centerX,
                rotatedVertex.y + model.centerY,
                rotatedVertex.z + model.centerZ
            )
            projectedVertices.add(projected)
        }

        // Calculate distance-based alpha for depth effect (original)
        val avgZ = model.centerZ
        val depthAlpha = (model.alpha * (1.0f - (avgZ + 150f) / 300f)).toInt().coerceIn(50, 255)

        // Enhanced wireframe rendering with multiple glow layers (ORIGINAL)
        paint.style = Paint.Style.STROKE
        paint.color = accentColor
        paint.shader = null

        // Draw edges with layered glow effect (ORIGINAL 3-layer system)
        for (edge in model.edges) {
            val start = projectedVertices[edge.start]
            val end = projectedVertices[edge.end]

            // Strict bounds checking (original)
            if (start.x < -100 || start.x > width + 100 ||
                start.y < -100 || start.y > height + 100 ||
                end.x < -100 || end.x > width + 100 ||
                end.y < -100 || end.y > height + 100) {
                continue
            }

            // Line length check (original)
            val dx = end.x - start.x
            val dy = end.y - start.y
            val lineLength = sqrt(dx * dx + dy * dy)

            if (lineLength > model.size * 4 || lineLength < 1f) {
                continue
            }

            // Outer glow (thicker, more transparent) - ORIGINAL
            paint.strokeWidth = 6f
            paint.alpha = (depthAlpha * 0.3f).toInt()
            paint.setShadowLayer(12f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)

            // Middle glow - ORIGINAL
            paint.strokeWidth = 3f
            paint.alpha = (depthAlpha * 0.6f).toInt()
            paint.setShadowLayer(6f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)

            // Core line (sharp and bright) - ORIGINAL
            paint.strokeWidth = 1.5f
            paint.alpha = depthAlpha
            paint.setShadowLayer(3f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)
        }

        // Reset paint
        paint.clearShadowLayer()
        paint.style = Paint.Style.FILL
    }

    private fun rotateVertex(vertex: Vertex3D, rotX: Float, rotY: Float, rotZ: Float): Vertex3D {
        var x = vertex.x
        var y = vertex.y
        var z = vertex.z

        // Rotate around X axis
        val cosX = cos(Math.toRadians(rotX.toDouble())).toFloat()
        val sinX = sin(Math.toRadians(rotX.toDouble())).toFloat()
        val newY = y * cosX - z * sinX
        val newZ = y * sinX + z * cosX
        y = newY
        z = newZ

        // Rotate around Y axis
        val cosY = cos(Math.toRadians(rotY.toDouble())).toFloat()
        val sinY = sin(Math.toRadians(rotY.toDouble())).toFloat()
        val newX = x * cosY + z * sinY
        val newZ2 = -x * sinY + z * cosY
        x = newX
        z = newZ2

        // Rotate around Z axis
        val cosZ = cos(Math.toRadians(rotZ.toDouble())).toFloat()
        val sinZ = sin(Math.toRadians(rotZ.toDouble())).toFloat()
        val newX2 = x * cosZ - y * sinZ
        val newY2 = x * sinZ + y * cosZ
        x = newX2
        y = newY2

        return Vertex3D(x, y, z)
    }

    private fun project3DTo2D(x: Float, y: Float, z: Float): PointF {
        // Simple perspective projection (ORIGINAL)
        val distance = 400f
        val scale = distance / (distance + z)
        return PointF(x * scale, y * scale)
    }




    // 3D Model classes and methods
    private data class Vertex3D(var x: Float, var y: Float, var z: Float)
    private data class Edge3D(val start: Int, val end: Int)

    private inner class Model3D(
        var centerX: Float,
        var centerY: Float,
        var centerZ: Float,
        val size: Float,
        var rotationX: Float,
        var rotationY: Float,
        var rotationZ: Float,
        val rotationSpeedX: Float,
        val rotationSpeedY: Float,
        val rotationSpeedZ: Float,
        val driftSpeedX: Float,
        val driftSpeedY: Float,
        val driftSpeedZ: Float,
        val originalX: Float,
        val originalY: Float,
        val originalZ: Float,
        val modelType: ModelType,
        val alpha: Int
    ) {
        val vertices = mutableListOf<Vertex3D>()
        val edges = mutableListOf<Edge3D>()

        init {
            createModel()
        }

        private fun createModel() {
            when (modelType) {
                ModelType.CUBE -> createCube()
            }
        }

        private fun createCube() {
            // Define cube vertices (ORIGINAL)
            vertices.addAll(listOf(
                Vertex3D(-size, -size, -size), // 0
                Vertex3D(size, -size, -size),  // 1
                Vertex3D(size, size, -size),   // 2
                Vertex3D(-size, size, -size),  // 3
                Vertex3D(-size, -size, size),  // 4
                Vertex3D(size, -size, size),   // 5
                Vertex3D(size, size, size),    // 6
                Vertex3D(-size, size, size)    // 7
            ))

            // Define cube edges (ORIGINAL)
            edges.addAll(listOf(
                // Front face
                Edge3D(0, 1), Edge3D(1, 2), Edge3D(2, 3), Edge3D(3, 0),
                // Back face
                Edge3D(4, 5), Edge3D(5, 6), Edge3D(6, 7), Edge3D(7, 4),
                // Connecting edges
                Edge3D(0, 4), Edge3D(1, 5), Edge3D(2, 6), Edge3D(3, 7)
            ))
        }


    }

    private enum class ModelType {
        CUBE
    }

}











