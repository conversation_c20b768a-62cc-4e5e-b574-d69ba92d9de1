package com.sr.ghostencryptedchat.view

import android.content.Context
import android.media.MediaPlayer
import android.net.Uri
import android.util.AttributeSet
import android.util.Log
import android.widget.VideoView

class ThreeDBackgroundView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : VideoView(context, attrs, defStyleAttr) {

    private var isVideoReady = false
    private var videoDuration = 0
    private val loopHandler = android.os.Handler(android.os.Looper.getMainLooper())
    private var seamlessLoopRunnable: Runnable? = null

    init {
        setupFlawlessLooping()
        Log.d("VideoBackground", "Flawless looping VideoView initialized")
    }

    private fun setupFlawlessLooping() {
        try {
            // Create URI for video asset
            val uri = Uri.parse("android.resource://${context.packageName}/raw/background")

            // Configure VideoView for flawless looping
            setVideoURI(uri)

            // Set up listeners for seamless looping
            setOnPreparedListener { mediaPlayer ->
                Log.d("VideoBackground", "Video prepared for flawless looping")

                // Configure MediaPlayer for optimal looping
                mediaPlayer.apply {
                    isLooping = false // We'll handle this manually for perfect loops
                    setVideoScalingMode(MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING)
                    setVolume(0f, 0f) // Mute for background
                }

                videoDuration = mediaPlayer.duration
                isVideoReady = true

                // Start video
                start()

                // Start seamless loop monitoring
                startSeamlessLoopMonitoring()

                Log.d("VideoBackground", "Video started with duration: ${videoDuration}ms")
            }

            setOnCompletionListener {
                Log.d("VideoBackground", "Video completed - seamless restart")
                handleSeamlessRestart()
            }

            setOnErrorListener { _, what, extra ->
                Log.e("VideoBackground", "VideoView error: what=$what, extra=$extra")
                // Try to restart
                postDelayed({ setupFlawlessLooping() }, 1000)
                true
            }

            setOnInfoListener { _, what, extra ->
                when (what) {
                    MediaPlayer.MEDIA_INFO_VIDEO_RENDERING_START -> {
                        Log.d("VideoBackground", "Video rendering started")
                    }
                    MediaPlayer.MEDIA_INFO_BUFFERING_START -> {
                        Log.d("VideoBackground", "Buffering started")
                    }
                    MediaPlayer.MEDIA_INFO_BUFFERING_END -> {
                        Log.d("VideoBackground", "Buffering ended")
                    }
                }
                false
            }

        } catch (e: Exception) {
            Log.e("VideoBackground", "Error setting up flawless looping: ${e.message}")
            // Fallback to assets
            setupFromAssets()
        }
    }





    private fun setupFromAssets() {
        try {
            Log.d("VideoBackground", "Trying assets fallback")

            // Create URI for assets
            val uri = Uri.parse("android.resource://${context.packageName}/assets/background.mp4")
            setVideoURI(uri)

        } catch (e: Exception) {
            Log.e("VideoBackground", "Assets fallback failed: ${e.message}")
        }
    }

    private fun startSeamlessLoopMonitoring() {
        seamlessLoopRunnable = object : Runnable {
            override fun run() {
                try {
                    if (isVideoReady && isPlaying) {
                        val currentPosition = currentPosition
                        val timeUntilEnd = videoDuration - currentPosition

                        // Prepare for seamless restart 200ms before end
                        if (timeUntilEnd <= 200 && timeUntilEnd > 0) {
                            Log.d("VideoBackground", "Preparing seamless restart in ${timeUntilEnd}ms")
                            prepareSeamlessRestart()
                        }

                        // Check every 50ms for precision
                        loopHandler.postDelayed(this, 50)
                    }
                } catch (e: Exception) {
                    Log.e("VideoBackground", "Error in seamless monitoring: ${e.message}")
                }
            }
        }
        seamlessLoopRunnable?.let { loopHandler.post(it) }
    }

    private fun prepareSeamlessRestart() {
        try {
            // Pre-seek to beginning for instant restart
            seekTo(0)
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error preparing seamless restart: ${e.message}")
        }
    }

    private fun handleSeamlessRestart() {
        try {
            // Immediate restart without any delay
            seekTo(0)
            start()

            // Restart monitoring
            startSeamlessLoopMonitoring()

            Log.d("VideoBackground", "Seamless restart completed")
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error in seamless restart: ${e.message}")
            // Fallback restart
            postDelayed({
                try {
                    start()
                    startSeamlessLoopMonitoring()
                } catch (fallbackError: Exception) {
                    Log.e("VideoBackground", "Fallback restart failed: ${fallbackError.message}")
                }
            }, 50)
        }
    }

    private fun stopSeamlessLoopMonitoring() {
        seamlessLoopRunnable?.let { loopHandler.removeCallbacks(it) }
        seamlessLoopRunnable = null
    }

    fun pauseVideo() {
        try {
            if (isVideoReady && isPlaying) {
                pause()
                stopSeamlessLoopMonitoring()
                Log.d("VideoBackground", "Video paused")
            }
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error pausing video: ${e.message}")
        }
    }

    fun resumeVideo() {
        try {
            if (isVideoReady && !isPlaying) {
                start()
                startSeamlessLoopMonitoring()
                Log.d("VideoBackground", "Video resumed")
            }
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error resuming video: ${e.message}")
        }
    }

    private fun releaseVideo() {
        try {
            stopSeamlessLoopMonitoring()
            stopPlayback()
            isVideoReady = false
            Log.d("VideoBackground", "Video released")
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error releasing video: ${e.message}")
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        releaseVideo()
    }










}











