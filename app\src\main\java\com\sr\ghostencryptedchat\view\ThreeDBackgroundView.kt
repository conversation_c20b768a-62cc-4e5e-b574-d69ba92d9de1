package com.sr.ghostencryptedchat.view

import android.content.Context
import android.graphics.*
import android.media.MediaPlayer
import android.net.Uri
import android.util.AttributeSet
import android.util.Log
import android.view.SurfaceHolder
import android.view.SurfaceView

class ThreeDBackgroundView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : SurfaceView(context, attrs, defStyleAttr), SurfaceHolder.Callback {

    private var mediaPlayer: MediaPlayer? = null
    private var secondaryPlayer: MediaPlayer? = null
    private var isVideoReady = false
    private var isSeamlessLoopEnabled = true
    private var videoDuration = 0L
    private var isUsingPrimaryPlayer = true

    init {
        // Set up SurfaceView for video playback
        holder.addCallback(this)
        setZOrderOnTop(false) // Video behind other content
        holder.setFormat(PixelFormat.TRANSLUCENT)

        Log.d("VideoBackground", "ThreeDBackgroundView initialized with seamless looping")
    }

    override fun surfaceCreated(holder: SurfaceHolder) {
        Log.d("VideoBackground", "Surface created")
        setupVideoPlayer()
    }

    override fun surfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
        Log.d("VideoBackground", "Surface changed: ${width}x${height}")
        // Video will auto-scale to surface size
    }

    override fun surfaceDestroyed(holder: SurfaceHolder) {
        Log.d("VideoBackground", "Surface destroyed")
        releaseVideoPlayer()
    }





    private fun setupVideoPlayer() {
        try {
            // Release any existing players
            releaseVideoPlayer()

            // Setup primary player with seamless looping
            setupPrimaryPlayer()

            // Setup secondary player for seamless transitions
            if (isSeamlessLoopEnabled) {
                setupSecondaryPlayer()
            }

            Log.d("VideoBackground", "Seamless video players setup initiated")

        } catch (e: Exception) {
            Log.e("VideoBackground", "Failed to setup video players: ${e.message}")
            isVideoReady = false
        }
    }

    private fun setupPrimaryPlayer() {
        mediaPlayer = MediaPlayer().apply {
            try {
                // Load video from assets
                val assetFileDescriptor = context.assets.openFd("background.mp4")
                setDataSource(assetFileDescriptor.fileDescriptor, assetFileDescriptor.startOffset, assetFileDescriptor.length)
                assetFileDescriptor.close()

                // Set display
                setDisplay(holder)

                // Configure for seamless playback
                isLooping = false // We'll handle looping manually for seamless effect
                setVideoScalingMode(MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING)

                // Optimize for smooth playback
                setAudioStreamType(android.media.AudioManager.STREAM_MUSIC)

                // Set listeners
                setOnPreparedListener { player ->
                    Log.d("VideoBackground", "Primary player prepared")
                    videoDuration = player.duration.toLong()
                    isVideoReady = true
                    player.start()

                    // Start monitoring for seamless loop
                    startSeamlessLoopMonitoring()
                }

                setOnCompletionListener {
                    Log.d("VideoBackground", "Primary player completed - restarting seamlessly")
                    handleSeamlessLoop()
                }

                setOnErrorListener { _, what, extra ->
                    Log.e("VideoBackground", "Primary MediaPlayer error: what=$what, extra=$extra")
                    isVideoReady = false
                    false
                }

                setOnVideoSizeChangedListener { _, width, height ->
                    Log.d("VideoBackground", "Video size: ${width}x${height}")
                }

                // Prepare asynchronously
                prepareAsync()

            } catch (e: Exception) {
                Log.e("VideoBackground", "Error setting up primary player: ${e.message}")
                throw e
            }
        }
    }

    private fun setupSecondaryPlayer() {
        secondaryPlayer = MediaPlayer().apply {
            try {
                // Load same video for seamless transition
                val assetFileDescriptor = context.assets.openFd("background.mp4")
                setDataSource(assetFileDescriptor.fileDescriptor, assetFileDescriptor.startOffset, assetFileDescriptor.length)
                assetFileDescriptor.close()

                // Configure but don't set display yet
                isLooping = false
                setVideoScalingMode(MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING)
                setAudioStreamType(android.media.AudioManager.STREAM_MUSIC)

                setOnPreparedListener {
                    Log.d("VideoBackground", "Secondary player prepared and ready")
                }

                setOnErrorListener { _, what, extra ->
                    Log.e("VideoBackground", "Secondary MediaPlayer error: what=$what, extra=$extra")
                    false
                }

                // Prepare but don't start
                prepareAsync()

            } catch (e: Exception) {
                Log.e("VideoBackground", "Error setting up secondary player: ${e.message}")
                // Continue without secondary player
                secondaryPlayer = null
            }
        }
    }

    private fun startSeamlessLoopMonitoring() {
        // Use a handler to monitor playback position for seamless looping
        val handler = android.os.Handler(android.os.Looper.getMainLooper())
        val monitorRunnable = object : Runnable {
            override fun run() {
                try {
                    if (isVideoReady && mediaPlayer?.isPlaying == true) {
                        val currentPosition = mediaPlayer?.currentPosition?.toLong() ?: 0
                        val duration = videoDuration

                        // Start preparing for seamless loop when near end (500ms before)
                        if (duration > 0 && currentPosition > duration - 500) {
                            prepareSeamlessLoop()
                        }

                        // Continue monitoring
                        handler.postDelayed(this, 100) // Check every 100ms
                    }
                } catch (e: Exception) {
                    Log.e("VideoBackground", "Error in loop monitoring: ${e.message}")
                }
            }
        }
        handler.post(monitorRunnable)
    }

    private fun prepareSeamlessLoop() {
        try {
            // Ensure secondary player is ready
            secondaryPlayer?.let { secondary ->
                if (!secondary.isPlaying) {
                    secondary.setDisplay(null) // Prepare without display
                    secondary.seekTo(0)
                    // Don't start yet - will start in handleSeamlessLoop
                }
            }
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error preparing seamless loop: ${e.message}")
        }
    }

    private fun handleSeamlessLoop() {
        try {
            if (isSeamlessLoopEnabled && secondaryPlayer != null) {
                // Quick switch to secondary player
                secondaryPlayer?.setDisplay(holder)
                secondaryPlayer?.start()

                // Hide primary player and reset it
                mediaPlayer?.setDisplay(null)
                mediaPlayer?.seekTo(0)

                // Swap players
                val temp = mediaPlayer
                mediaPlayer = secondaryPlayer
                secondaryPlayer = temp

                isUsingPrimaryPlayer = !isUsingPrimaryPlayer

                Log.d("VideoBackground", "Seamless loop executed - switched to ${if (isUsingPrimaryPlayer) "primary" else "secondary"} player")

                // Restart monitoring for next loop
                startSeamlessLoopMonitoring()
            } else {
                // Fallback to simple restart
                mediaPlayer?.seekTo(0)
                mediaPlayer?.start()
                Log.d("VideoBackground", "Simple loop restart")
            }
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error in seamless loop: ${e.message}")
            // Fallback to simple restart
            try {
                mediaPlayer?.seekTo(0)
                mediaPlayer?.start()
            } catch (fallbackError: Exception) {
                Log.e("VideoBackground", "Fallback restart failed: ${fallbackError.message}")
            }
        }
    }

    private fun releaseVideoPlayer() {
        try {
            mediaPlayer?.apply {
                if (isPlaying) {
                    stop()
                }
                reset()
                release()
            }
            secondaryPlayer?.apply {
                if (isPlaying) {
                    stop()
                }
                reset()
                release()
            }
            mediaPlayer = null
            secondaryPlayer = null
            isVideoReady = false
            Log.d("VideoBackground", "Video players released")
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error releasing video players: ${e.message}")
        }
    }

    fun pauseVideo() {
        try {
            if (isVideoReady && mediaPlayer?.isPlaying == true) {
                mediaPlayer?.pause()
                Log.d("VideoBackground", "Video paused")
            }
            // Also pause secondary player if it's playing
            if (secondaryPlayer?.isPlaying == true) {
                secondaryPlayer?.pause()
            }
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error pausing video: ${e.message}")
        }
    }

    fun resumeVideo() {
        try {
            if (isVideoReady && mediaPlayer?.isPlaying == false) {
                mediaPlayer?.start()
                Log.d("VideoBackground", "Video resumed")
                // Restart monitoring if needed
                startSeamlessLoopMonitoring()
            }
        } catch (e: Exception) {
            Log.e("VideoBackground", "Error resuming video: ${e.message}")
        }
    }

    fun setSeamlessLooping(enabled: Boolean) {
        isSeamlessLoopEnabled = enabled
        Log.d("VideoBackground", "Seamless looping ${if (enabled) "enabled" else "disabled"}")
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        releaseVideoPlayer()
    }










}











