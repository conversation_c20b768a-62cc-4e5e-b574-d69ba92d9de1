package com.sr.ghostencryptedchat.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import java.util.Random
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

class ThreeDBackgroundView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val random = Random()
    private val paint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.FILL
    }

    private val models3D = mutableListOf<Model3D>()

    // Colors for the 3D effect - deeper greens based on the image
    private val darkColor = Color.parseColor("#121212")
    private val accentColor = Color.parseColor("#aeff00") // Bright green
    private val midColor = Color.parseColor("#0A3B15") // Deeper green
    private val shadowColor = Color.parseColor("#0A0A0A")

    // Pre-calculated trigonometric values
    private val cosValues = FloatArray(360)
    private val sinValues = FloatArray(360)

    // No animation variables needed for static cubes

    init {
        // Set background color
        setBackgroundColor(darkColor)

        // Enable hardware acceleration
        setLayerType(LAYER_TYPE_HARDWARE, null)

        // Pre-calculate sin and cos values
        for (i in 0 until 360) {
            val radians = Math.toRadians(i.toDouble())
            cosValues[i] = cos(radians).toFloat()
            sinValues[i] = sin(radians).toFloat()
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // Create static cubes
        createStaticCubes(w, h)

        // Force a redraw
        invalidate()
    }



    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // Draw static cubes (no animation)
        for (model in models3D) {
            drawStaticCube(canvas, model)
        }
    }





    private fun createStaticCubes(w: Int, h: Int) {
        models3D.clear()

        // Create 6 static 3D wireframe cubes with random positioning
        val cubeCount = 6

        for (i in 0 until cubeCount) {
            // Random positions across screen (avoid edges)
            val centerX = (w * 0.1f) + random.nextFloat() * (w * 0.8f)
            val centerY = (h * 0.1f) + random.nextFloat() * (h * 0.8f)
            val centerZ = random.nextFloat() * 120f - 60f // Larger Z range for better 3D effect

            // Varied sizes for visual interest
            val size = 30f + random.nextFloat() * 20f // 30-50px

            // Random static rotations for 3D appearance (favor angles that show cube structure)
            val rotationX = random.nextFloat() * 60f + 15f // 15-75 degrees
            val rotationY = random.nextFloat() * 60f + 15f // 15-75 degrees
            val rotationZ = random.nextFloat() * 30f - 15f // -15 to 15 degrees

            // No animation speeds (static)
            val rotationSpeedX = 0f
            val rotationSpeedY = 0f
            val rotationSpeedZ = 0f

            // No movement (static)
            val driftSpeedX = 0f
            val driftSpeedY = 0f
            val driftSpeedZ = 0f

            // Only cubes
            val modelType = ModelType.CUBE

            // Varied alpha for depth
            val alpha = 120 + random.nextInt(80) // 120-200

            models3D.add(Model3D(
                centerX = centerX,
                centerY = centerY,
                centerZ = centerZ,
                size = size,
                rotationX = rotationX,
                rotationY = rotationY,
                rotationZ = rotationZ,
                rotationSpeedX = rotationSpeedX,
                rotationSpeedY = rotationSpeedY,
                rotationSpeedZ = rotationSpeedZ,
                driftSpeedX = driftSpeedX,
                driftSpeedY = driftSpeedY,
                driftSpeedZ = driftSpeedZ,
                originalX = centerX,
                originalY = centerY,
                originalZ = centerZ,
                modelType = modelType,
                alpha = alpha
            ))
        }
    }



    private fun drawStaticCube(canvas: Canvas, model: Model3D) {
        // Static 3D wireframe rendering with rotation but no animation
        val projectedVertices = mutableListOf<PointF>()

        for (vertex in model.vertices) {
            // Apply static rotation for 3D appearance
            val rotatedVertex = rotateVertex(vertex, model.rotationX, model.rotationY, model.rotationZ)

            // Project to 2D with perspective
            val projected = project3DTo2D(
                rotatedVertex.x + model.centerX,
                rotatedVertex.y + model.centerY,
                rotatedVertex.z + model.centerZ
            )
            projectedVertices.add(projected)
        }

        // Calculate depth-based alpha for 3D effect
        val avgZ = model.centerZ
        val depthAlpha = (model.alpha * (1.0f - (avgZ + 80f) / 160f)).toInt().coerceIn(60, 255)

        // Set up paint for wireframe with glow
        paint.style = Paint.Style.STROKE
        paint.color = accentColor
        paint.shader = null

        // Draw edges with glow effect
        for (edge in model.edges) {
            val start = projectedVertices[edge.start]
            val end = projectedVertices[edge.end]

            // Skip extremely long lines to prevent artifacts
            val dx = end.x - start.x
            val dy = end.y - start.y
            val lineLength = kotlin.math.sqrt(dx * dx + dy * dy)

            if (lineLength > model.size * 3 || lineLength < 1f) {
                continue
            }

            // Outer glow
            paint.strokeWidth = 4f
            paint.alpha = (depthAlpha * 0.4f).toInt()
            paint.setShadowLayer(8f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)

            // Core line
            paint.strokeWidth = 2f
            paint.alpha = depthAlpha
            paint.setShadowLayer(4f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)
        }

        // Reset paint
        paint.clearShadowLayer()
        paint.style = Paint.Style.FILL
    }

    private fun rotateVertex(vertex: Vertex3D, rotX: Float, rotY: Float, rotZ: Float): Vertex3D {
        var x = vertex.x
        var y = vertex.y
        var z = vertex.z

        // Rotate around X axis
        val cosX = cos(Math.toRadians(rotX.toDouble())).toFloat()
        val sinX = sin(Math.toRadians(rotX.toDouble())).toFloat()
        val newY = y * cosX - z * sinX
        val newZ = y * sinX + z * cosX
        y = newY
        z = newZ

        // Rotate around Y axis
        val cosY = cos(Math.toRadians(rotY.toDouble())).toFloat()
        val sinY = sin(Math.toRadians(rotY.toDouble())).toFloat()
        val newX = x * cosY + z * sinY
        val newZ2 = -x * sinY + z * cosY
        x = newX
        z = newZ2

        // Rotate around Z axis
        val cosZ = cos(Math.toRadians(rotZ.toDouble())).toFloat()
        val sinZ = sin(Math.toRadians(rotZ.toDouble())).toFloat()
        val newX2 = x * cosZ - y * sinZ
        val newY2 = x * sinZ + y * cosZ
        x = newX2
        y = newY2

        return Vertex3D(x, y, z)
    }

    private fun project3DTo2D(x: Float, y: Float, z: Float): PointF {
        // Enhanced perspective projection for better 3D effect
        val distance = 300f
        val scale = distance / (distance + z)

        // Allow more perspective variation for better 3D appearance
        val clampedScale = scale.coerceIn(0.5f, 2.0f)

        return PointF(x * clampedScale, y * clampedScale)
    }




    // 3D Model classes and methods
    private data class Vertex3D(var x: Float, var y: Float, var z: Float)
    private data class Edge3D(val start: Int, val end: Int)

    private inner class Model3D(
        var centerX: Float,
        var centerY: Float,
        var centerZ: Float,
        val size: Float,
        var rotationX: Float,
        var rotationY: Float,
        var rotationZ: Float,
        val rotationSpeedX: Float,
        val rotationSpeedY: Float,
        val rotationSpeedZ: Float,
        val driftSpeedX: Float,
        val driftSpeedY: Float,
        val driftSpeedZ: Float,
        val originalX: Float,
        val originalY: Float,
        val originalZ: Float,
        val modelType: ModelType,
        val alpha: Int
    ) {
        val vertices = mutableListOf<Vertex3D>()
        val edges = mutableListOf<Edge3D>()

        init {
            createModel()
        }

        private fun createModel() {
            when (modelType) {
                ModelType.CUBE -> createCube()
            }
        }

        private fun createCube() {
            // Define cube vertices (proper 3D cube)
            val s = size * 0.5f // Half size for proper scaling
            vertices.addAll(listOf(
                Vertex3D(-s, -s, -s), // 0: front bottom left
                Vertex3D(s, -s, -s),  // 1: front bottom right
                Vertex3D(s, s, -s),   // 2: front top right
                Vertex3D(-s, s, -s),  // 3: front top left
                Vertex3D(-s, -s, s),  // 4: back bottom left
                Vertex3D(s, -s, s),   // 5: back bottom right
                Vertex3D(s, s, s),    // 6: back top right
                Vertex3D(-s, s, s)    // 7: back top left
            ))

            // Define cube edges (all 12 edges of a cube)
            edges.addAll(listOf(
                // Front face (4 edges)
                Edge3D(0, 1), Edge3D(1, 2), Edge3D(2, 3), Edge3D(3, 0),
                // Back face (4 edges)
                Edge3D(4, 5), Edge3D(5, 6), Edge3D(6, 7), Edge3D(7, 4),
                // Connecting edges front to back (4 edges)
                Edge3D(0, 4), Edge3D(1, 5), Edge3D(2, 6), Edge3D(3, 7)
            ))
        }


    }

    private enum class ModelType {
        CUBE
    }

}











