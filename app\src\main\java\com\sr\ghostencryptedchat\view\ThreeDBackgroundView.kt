package com.sr.ghostencryptedchat.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PointF
import android.graphics.RadialGradient
import android.graphics.Shader
import android.util.AttributeSet
import android.view.View
import java.util.Random
import kotlin.math.cos
import kotlin.math.sin

class ThreeDBackgroundView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val random = Random()
    private val paint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.FILL
    }
    
    private val polygons = mutableListOf<Polygon>()
    private val models3D = mutableListOf<Model3D>()

    // Colors for the 3D effect - deeper greens based on the image
    private val darkColor = Color.parseColor("#121212")
    private val accentColor = Color.parseColor("#aeff00") // Bright green
    private val midColor = Color.parseColor("#0A3B15") // Deeper green
    private val shadowColor = Color.parseColor("#0A0A0A")
    
    // Pre-calculated trigonometric values
    private val cosValues = FloatArray(360)
    private val sinValues = FloatArray(360)

    // Animation variables
    private var animationTime = 0f
    private var lastFrameTime = System.currentTimeMillis()
    private val animationSpeed = 0.5f // Slow, gentle animation
    
    init {
        // Set background color
        setBackgroundColor(darkColor)
        
        // Enable hardware acceleration
        setLayerType(LAYER_TYPE_HARDWARE, null)
        
        // Pre-calculate sin and cos values
        for (i in 0 until 360) {
            val radians = Math.toRadians(i.toDouble())
            cosValues[i] = cos(radians).toFloat()
            sinValues[i] = sin(radians).toFloat()
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        
        // Initialize polygons - create a new pattern each time
        polygons.clear()
        
        // Create fewer, larger polygons (8-12)
        val polygonCount = 8 + random.nextInt(4)
        
        // Create polygons with varied positions and animation properties
        for (i in 0 until polygonCount) {
            // Random position across the screen
            val centerX = random.nextInt(w).toFloat()
            val centerY = random.nextInt(h).toFloat()

            // Larger size (80-150px) to match the image
            val size = 80 + random.nextInt(70)

            // Random rotation
            val rotation = random.nextInt(360).toFloat()

            // Deeper depth (15-35px) for more pronounced 3D effect
            val depth = 15 + random.nextInt(20)

            // Higher alpha for more visibility
            val alpha = 180 + random.nextInt(75) // 180-255

            // Animation properties - very slow and gentle movement
            val driftSpeedX = (random.nextFloat() - 0.5f) * 0.3f // -0.15 to 0.15 pixels per frame
            val driftSpeedY = (random.nextFloat() - 0.5f) * 0.3f
            val rotationSpeed = (random.nextFloat() - 0.5f) * 0.2f // -0.1 to 0.1 degrees per frame

            // Fixed shading properties to prevent flickering
            val edgesToShade = 2 + random.nextInt(2) // 2-3 edges (fixed per polygon)
            val startEdge = random.nextInt(6) // Random starting edge (fixed per polygon)

            polygons.add(Polygon(
                centerX = centerX,
                centerY = centerY,
                size = size,
                rotation = rotation,
                depth = depth,
                alpha = alpha,
                driftSpeedX = driftSpeedX,
                driftSpeedY = driftSpeedY,
                rotationSpeed = rotationSpeed,
                originalX = centerX,
                originalY = centerY,
                edgesToShade = edgesToShade,
                startEdge = startEdge
            ))
        }
        
        // Check for overlapping polygons and adjust if needed
        preventExcessiveOverlap()

        // Create 3D models
        create3DModels(w, h)

        // Force a redraw with the new pattern
        invalidate()
    }

    // Prevent polygons from excessively overlapping
    private fun preventExcessiveOverlap() {
        val minDistance = 120 // Larger minimum distance for bigger polygons
        
        for (i in 0 until polygons.size) {
            for (j in i + 1 until polygons.size) {
                val p1 = polygons[i]
                val p2 = polygons[j]
                
                // Calculate distance between centers
                val dx = p1.centerX - p2.centerX
                val dy = p1.centerY - p2.centerY
                val distance = Math.sqrt((dx * dx + dy * dy).toDouble()).toInt()
                
                // If polygons are too close, move the second one
                if (distance < minDistance) {
                    // Move in random direction
                    val angle = random.nextInt(360)
                    val moveDistance = minDistance - distance

                    // Move p2 away from p1
                    val newX = p2.centerX + (moveDistance * cosValues[angle])
                    val newY = p2.centerY + (moveDistance * sinValues[angle])

                    polygons[j] = Polygon(
                        centerX = newX,
                        centerY = newY,
                        size = p2.size,
                        rotation = p2.rotation,
                        depth = p2.depth,
                        alpha = p2.alpha,
                        driftSpeedX = p2.driftSpeedX,
                        driftSpeedY = p2.driftSpeedY,
                        rotationSpeed = p2.rotationSpeed,
                        originalX = newX,
                        originalY = newY,
                        edgesToShade = p2.edgesToShade,
                        startEdge = p2.startEdge
                    )
                }
            }
        }
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // Update animation
        val currentTime = System.currentTimeMillis()
        val deltaTime = (currentTime - lastFrameTime) / 16.67f // Normalize to 60fps
        lastFrameTime = currentTime
        animationTime += deltaTime * animationSpeed

        // Update polygon positions and rotations
        updatePolygons(deltaTime)

        // Update 3D models
        update3DModels(deltaTime)

        // Draw each polygon
        for (polygon in polygons) {
            drawPolygon(canvas, polygon)
        }

        // Draw 3D models
        for (model in models3D) {
            draw3DModel(canvas, model)
        }

        // Request next frame for smooth animation
        invalidate()
    }

    private fun updatePolygons(deltaTime: Float) {
        for (polygon in polygons) {
            // Gentle floating movement with sine wave for organic feel
            val floatOffsetX = sin(animationTime * 0.01f + polygon.originalX * 0.001f) * 20f
            val floatOffsetY = cos(animationTime * 0.008f + polygon.originalY * 0.001f) * 15f

            // Update position with drift and floating
            polygon.centerX = polygon.originalX + polygon.driftSpeedX * animationTime + floatOffsetX
            polygon.centerY = polygon.originalY + polygon.driftSpeedY * animationTime + floatOffsetY

            // Update rotation
            polygon.rotation += polygon.rotationSpeed * deltaTime

            // Keep rotation in bounds
            if (polygon.rotation >= 360f) polygon.rotation -= 360f
            if (polygon.rotation < 0f) polygon.rotation += 360f

            // Wrap around screen edges for continuous movement
            if (polygon.centerX < -polygon.size) {
                polygon.centerX = width.toFloat() + polygon.size
            } else if (polygon.centerX > width + polygon.size) {
                polygon.centerX = -polygon.size.toFloat()
            }

            if (polygon.centerY < -polygon.size) {
                polygon.centerY = height.toFloat() + polygon.size
            } else if (polygon.centerY > height + polygon.size) {
                polygon.centerY = -polygon.size.toFloat()
            }
        }
    }
    
    private fun drawPolygon(canvas: Canvas, polygon: Polygon) {
        // Create path for the polygon with rounded corners
        val path = Path()

        // Top face
        val points = mutableListOf<Pair<Float, Float>>()
        for (i in 0 until 6) {
            val pointAngle = (polygon.rotation + i * 60f) % 360f
            val angleIndex = pointAngle.toInt() % 360
            val x = polygon.centerX + polygon.size * cosValues[angleIndex]
            val y = polygon.centerY + polygon.size * sinValues[angleIndex]
            points.add(Pair(x, y))
        }
        
        // Create rounded hexagon using quadratic curves
        val cornerRadius = polygon.size * 0.1f // 10% of size for subtle rounding

        // Start path with first point
        path.moveTo(points[0].first, points[0].second)

        // Add curved lines between points for rounded corners
        for (i in 0 until points.size) {
            val currentPoint = points[i]
            val nextPoint = points[(i + 1) % points.size]

            // Calculate control point for smooth curve
            val midX = (currentPoint.first + nextPoint.first) / 2f
            val midY = (currentPoint.second + nextPoint.second) / 2f

            // Create smooth curve to next point
            if (i == 0) {
                path.quadTo(
                    currentPoint.first + (midX - currentPoint.first) * 0.8f,
                    currentPoint.second + (midY - currentPoint.second) * 0.8f,
                    midX,
                    midY
                )
            } else {
                path.quadTo(
                    currentPoint.first,
                    currentPoint.second,
                    midX,
                    midY
                )
            }
        }

        // Close the path smoothly
        path.close()
        
        // Create lighting effect for top face - keep the original gradient
        val lightAngle = ((polygon.rotation + 30f) % 360f).toInt()
        val lightX = polygon.centerX + polygon.size * 2 * cosValues[lightAngle]
        val lightY = polygon.centerY + polygon.size * 2 * sinValues[lightAngle]
        
        // Create radial gradient for more realistic lighting
        paint.shader = RadialGradient(
            lightX, 
            lightY,
            polygon.size * 3f,
            intArrayOf(accentColor, midColor, darkColor),
            floatArrayOf(0.2f, 0.6f, 1.0f),
            Shader.TileMode.CLAMP
        )
        
        // Set alpha - high for better visibility
        paint.alpha = polygon.alpha
        
        // Draw top face
        canvas.drawPath(path, paint)
        
        // Use fixed shading properties to prevent flickering
        for (i in 0 until polygon.edgesToShade) {
            val edgeIndex = (polygon.startEdge + i) % 6
            val nextEdgeIndex = (edgeIndex + 1) % 6
            
            val sidePath = Path()
            
            // Current point on top face
            sidePath.moveTo(points[edgeIndex].first, points[edgeIndex].second)
            
            // Next point on top face
            sidePath.lineTo(points[nextEdgeIndex].first, points[nextEdgeIndex].second)
            
            // Next point projected down (with reduced depth)
            val depthAngle = ((polygon.rotation + 90f) % 360f).toInt()
            val projectedDepth = polygon.depth * 0.7f // Reduced depth for lighter effect
            
            sidePath.lineTo(
                points[nextEdgeIndex].first + projectedDepth * cosValues[depthAngle],
                points[nextEdgeIndex].second + projectedDepth * sinValues[depthAngle]
            )
            
            // Current point projected down
            sidePath.lineTo(
                points[edgeIndex].first + projectedDepth * cosValues[depthAngle],
                points[edgeIndex].second + projectedDepth * sinValues[depthAngle]
            )
            
            // Close the path
            sidePath.close()
            
            // Lighter gradient for side face
            paint.shader = LinearGradient(
                points[edgeIndex].first, 
                points[edgeIndex].second,
                points[edgeIndex].first + projectedDepth * cosValues[depthAngle], 
                points[edgeIndex].second + projectedDepth * sinValues[depthAngle],
                intArrayOf(midColor, shadowColor),
                floatArrayOf(0.2f, 1f),
                Shader.TileMode.CLAMP
            )
            
            // Lower alpha for sides - much lighter shading
            paint.alpha = (polygon.alpha * 0.5f).toInt()
            
            // Draw side face
            canvas.drawPath(sidePath, paint)
        }
        
        // Reset shader
        paint.shader = null
    }

    private fun create3DModels(w: Int, h: Int) {
        models3D.clear()

        // Create 3-5 3D models
        val modelCount = 3 + random.nextInt(3)

        for (i in 0 until modelCount) {
            // Random position
            val centerX = random.nextInt(w).toFloat()
            val centerY = random.nextInt(h).toFloat()
            val centerZ = random.nextFloat() * 200f - 100f // -100 to 100

            // Random size (smaller than hexagons)
            val size = 30f + random.nextFloat() * 40f // 30-70px

            // Random rotation
            val rotationX = random.nextFloat() * 360f
            val rotationY = random.nextFloat() * 360f
            val rotationZ = random.nextFloat() * 360f

            // Very slow rotation speeds
            val rotationSpeedX = (random.nextFloat() - 0.5f) * 0.5f
            val rotationSpeedY = (random.nextFloat() - 0.5f) * 0.5f
            val rotationSpeedZ = (random.nextFloat() - 0.5f) * 0.5f

            // Gentle drift
            val driftSpeedX = (random.nextFloat() - 0.5f) * 0.2f
            val driftSpeedY = (random.nextFloat() - 0.5f) * 0.2f
            val driftSpeedZ = (random.nextFloat() - 0.5f) * 0.1f

            // Random model type
            val modelType = ModelType.values()[random.nextInt(ModelType.values().size)]

            // Alpha for transparency
            val alpha = 120 + random.nextInt(60) // 120-180 for subtle appearance

            models3D.add(Model3D(
                centerX = centerX,
                centerY = centerY,
                centerZ = centerZ,
                size = size,
                rotationX = rotationX,
                rotationY = rotationY,
                rotationZ = rotationZ,
                rotationSpeedX = rotationSpeedX,
                rotationSpeedY = rotationSpeedY,
                rotationSpeedZ = rotationSpeedZ,
                driftSpeedX = driftSpeedX,
                driftSpeedY = driftSpeedY,
                driftSpeedZ = driftSpeedZ,
                originalX = centerX,
                originalY = centerY,
                originalZ = centerZ,
                modelType = modelType,
                alpha = alpha
            ))
        }
    }

    private fun update3DModels(deltaTime: Float) {
        for (model in models3D) {
            // Update rotations
            model.rotationX += model.rotationSpeedX * deltaTime
            model.rotationY += model.rotationSpeedY * deltaTime
            model.rotationZ += model.rotationSpeedZ * deltaTime

            // Keep rotations in bounds
            model.rotationX = model.rotationX % 360f
            model.rotationY = model.rotationY % 360f
            model.rotationZ = model.rotationZ % 360f

            // Update position with gentle floating
            val floatOffsetX = sin(animationTime * 0.005f + model.originalX * 0.001f) * 15f
            val floatOffsetY = cos(animationTime * 0.004f + model.originalY * 0.001f) * 10f
            val floatOffsetZ = sin(animationTime * 0.003f + model.originalZ * 0.001f) * 20f

            model.centerX = model.originalX + model.driftSpeedX * animationTime + floatOffsetX
            model.centerY = model.originalY + model.driftSpeedY * animationTime + floatOffsetY
            model.centerZ = model.originalZ + model.driftSpeedZ * animationTime + floatOffsetZ

            // Wrap around screen edges
            if (model.centerX < -model.size * 2) {
                model.centerX = width.toFloat() + model.size * 2
            } else if (model.centerX > width + model.size * 2) {
                model.centerX = -model.size * 2
            }

            if (model.centerY < -model.size * 2) {
                model.centerY = height.toFloat() + model.size * 2
            } else if (model.centerY > height + model.size * 2) {
                model.centerY = -model.size * 2
            }
        }
    }

    private fun draw3DModel(canvas: Canvas, model: Model3D) {
        // Transform and project vertices
        val projectedVertices = mutableListOf<PointF>()

        for (vertex in model.vertices) {
            // Apply rotations
            val rotatedVertex = rotateVertex(vertex, model.rotationX, model.rotationY, model.rotationZ)

            // Project to 2D
            val projected = project3DTo2D(
                rotatedVertex.x + model.centerX,
                rotatedVertex.y + model.centerY,
                rotatedVertex.z + model.centerZ
            )
            projectedVertices.add(projected)
        }

        // Set up paint for wireframe
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 2f
        paint.color = accentColor
        paint.alpha = model.alpha
        paint.shader = null

        // Draw edges
        for (edge in model.edges) {
            val start = projectedVertices[edge.start]
            val end = projectedVertices[edge.end]

            // Add glow effect
            paint.setShadowLayer(8f, 0f, 0f, accentColor)
            canvas.drawLine(start.x, start.y, end.x, end.y, paint)
        }

        // Reset paint
        paint.clearShadowLayer()
        paint.style = Paint.Style.FILL
    }

    private fun rotateVertex(vertex: Vertex3D, rotX: Float, rotY: Float, rotZ: Float): Vertex3D {
        var x = vertex.x
        var y = vertex.y
        var z = vertex.z

        // Rotate around X axis
        val cosX = cos(Math.toRadians(rotX.toDouble())).toFloat()
        val sinX = sin(Math.toRadians(rotX.toDouble())).toFloat()
        val newY = y * cosX - z * sinX
        val newZ = y * sinX + z * cosX
        y = newY
        z = newZ

        // Rotate around Y axis
        val cosY = cos(Math.toRadians(rotY.toDouble())).toFloat()
        val sinY = sin(Math.toRadians(rotY.toDouble())).toFloat()
        val newX = x * cosY + z * sinY
        val newZ2 = -x * sinY + z * cosY
        x = newX
        z = newZ2

        // Rotate around Z axis
        val cosZ = cos(Math.toRadians(rotZ.toDouble())).toFloat()
        val sinZ = sin(Math.toRadians(rotZ.toDouble())).toFloat()
        val newX2 = x * cosZ - y * sinZ
        val newY2 = x * sinZ + y * cosZ
        x = newX2
        y = newY2

        return Vertex3D(x, y, z)
    }

    private fun project3DTo2D(x: Float, y: Float, z: Float): PointF {
        // Simple perspective projection
        val distance = 400f
        val scale = distance / (distance + z)
        return PointF(x * scale, y * scale)
    }

    // Represents a 3D polygon with animation properties
    private inner class Polygon(
        var centerX: Float,
        var centerY: Float,
        val size: Int,
        var rotation: Float,
        val depth: Int,
        val alpha: Int,
        val driftSpeedX: Float,
        val driftSpeedY: Float,
        val rotationSpeed: Float,
        val originalX: Float,
        val originalY: Float,
        val edgesToShade: Int,
        val startEdge: Int
    )

    // 3D Model classes and methods
    private data class Vertex3D(var x: Float, var y: Float, var z: Float)
    private data class Edge3D(val start: Int, val end: Int)

    private inner class Model3D(
        var centerX: Float,
        var centerY: Float,
        var centerZ: Float,
        val size: Float,
        var rotationX: Float,
        var rotationY: Float,
        var rotationZ: Float,
        val rotationSpeedX: Float,
        val rotationSpeedY: Float,
        val rotationSpeedZ: Float,
        val driftSpeedX: Float,
        val driftSpeedY: Float,
        val driftSpeedZ: Float,
        val originalX: Float,
        val originalY: Float,
        val originalZ: Float,
        val modelType: ModelType,
        val alpha: Int
    ) {
        val vertices = mutableListOf<Vertex3D>()
        val edges = mutableListOf<Edge3D>()

        init {
            createModel()
        }

        private fun createModel() {
            when (modelType) {
                ModelType.CUBE -> createCube()
                ModelType.CRYSTAL -> createCrystal()
                ModelType.PYRAMID -> createPyramid()
            }
        }

        private fun createCube() {
            // Define cube vertices
            vertices.addAll(listOf(
                Vertex3D(-size, -size, -size), // 0
                Vertex3D(size, -size, -size),  // 1
                Vertex3D(size, size, -size),   // 2
                Vertex3D(-size, size, -size),  // 3
                Vertex3D(-size, -size, size),  // 4
                Vertex3D(size, -size, size),   // 5
                Vertex3D(size, size, size),    // 6
                Vertex3D(-size, size, size)    // 7
            ))

            // Define cube edges
            edges.addAll(listOf(
                // Front face
                Edge3D(0, 1), Edge3D(1, 2), Edge3D(2, 3), Edge3D(3, 0),
                // Back face
                Edge3D(4, 5), Edge3D(5, 6), Edge3D(6, 7), Edge3D(7, 4),
                // Connecting edges
                Edge3D(0, 4), Edge3D(1, 5), Edge3D(2, 6), Edge3D(3, 7)
            ))
        }

        private fun createCrystal() {
            // Define crystal vertices (octahedron-like)
            vertices.addAll(listOf(
                Vertex3D(0f, -size * 1.5f, 0f),    // Top point
                Vertex3D(-size, 0f, -size),        // Front left
                Vertex3D(size, 0f, -size),         // Front right
                Vertex3D(size, 0f, size),          // Back right
                Vertex3D(-size, 0f, size),         // Back left
                Vertex3D(0f, size * 1.5f, 0f)     // Bottom point
            ))

            // Define crystal edges
            edges.addAll(listOf(
                // Top pyramid
                Edge3D(0, 1), Edge3D(0, 2), Edge3D(0, 3), Edge3D(0, 4),
                // Middle square
                Edge3D(1, 2), Edge3D(2, 3), Edge3D(3, 4), Edge3D(4, 1),
                // Bottom pyramid
                Edge3D(5, 1), Edge3D(5, 2), Edge3D(5, 3), Edge3D(5, 4)
            ))
        }

        private fun createPyramid() {
            // Define pyramid vertices
            vertices.addAll(listOf(
                Vertex3D(0f, -size * 1.2f, 0f),   // Top point
                Vertex3D(-size, size, -size),      // Base front left
                Vertex3D(size, size, -size),       // Base front right
                Vertex3D(size, size, size),        // Base back right
                Vertex3D(-size, size, size)        // Base back left
            ))

            // Define pyramid edges
            edges.addAll(listOf(
                // Base
                Edge3D(1, 2), Edge3D(2, 3), Edge3D(3, 4), Edge3D(4, 1),
                // Sides to top
                Edge3D(0, 1), Edge3D(0, 2), Edge3D(0, 3), Edge3D(0, 4)
            ))
        }
    }

    private enum class ModelType {
        CUBE, CRYSTAL, PYRAMID
    }
}











