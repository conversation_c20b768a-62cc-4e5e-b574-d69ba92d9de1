package com.sr.ghostencryptedchat.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.sr.ghostencryptedchat.databinding.ItemContactBinding
import com.sr.ghostencryptedchat.R

class SimpleContactsAdapter(
    private val users: List<Pair<String, Boolean>>, // Pair(username, isPending)
    private val onClick: (String, Boolean) -> Unit,
    private val onLongClick: (String) -> Unit // Add long click handler
) : RecyclerView.Adapter<SimpleContactsAdapter.ContactViewHolder>() {

    inner class ContactViewHolder(val binding: ItemContactBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ContactViewHolder {
        val binding = ItemContactBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ContactViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ContactViewHolder, position: Int) {
        val (name, isPending) = users[position]
        holder.binding.contactName.text = name

        if (isPending) {
            holder.binding.statusText.text = "New message waiting"
            holder.binding.statusText.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.neon_green))
            holder.binding.statusText.setTypeface(null, android.graphics.Typeface.BOLD)
        } else {
            holder.binding.statusText.text = "Tap to DM"
            holder.binding.statusText.setTextColor(ContextCompat.getColor(holder.itemView.context, android.R.color.darker_gray))
            holder.binding.statusText.setTypeface(null, android.graphics.Typeface.NORMAL)
        }

        holder.binding.root.setOnClickListener {
            onClick(name, isPending)
        }
        
        // Add long click listener for deleting contacts
        holder.binding.root.setOnLongClickListener {
            onLongClick(name)
            true
        }
    }

    override fun getItemCount(): Int = users.size
}
