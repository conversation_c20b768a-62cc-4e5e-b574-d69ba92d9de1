<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_primary">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="24dp">

        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back"
            android:tint="@color/text_primary"
            android:contentDescription="Back" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text="Network Scanner"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary" />

    </LinearLayout>

    <!-- Network Info Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="@color/background_secondary"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Network Information"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="12dp" />

            <TextView
                android:id="@+id/networkInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Loading network information..."
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:fontFamily="monospace" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Scan Controls -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingHorizontal="24dp"
        android:paddingBottom="16dp">

        <Button
            android:id="@+id/scanButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:text="Scan Network"
            android:textColor="@color/background_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:backgroundTint="@color/accent_color"
            style="@style/Widget.MaterialComponents.Button" />

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="16dp"
            android:indeterminateTint="@color/accent_color"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Devices List -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginHorizontal="24dp"
        android:layout_marginBottom="24dp"
        app:cardBackgroundColor="@color/background_secondary"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Discovered Devices"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:padding="20dp"
                android:paddingBottom="12dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/devicesRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:paddingHorizontal="12dp"
                android:paddingBottom="12dp"
                android:clipToPadding="false"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>
