<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardBackgroundColor="@color/background_secondary"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="20dp"
        android:gravity="center_vertical">

        <!-- Tool Icon -->
        <TextView
            android:id="@+id/toolIcon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/circle_background"
            android:backgroundTint="@color/accent_color"
            android:gravity="center"
            android:text="🔧"
            android:textSize="24sp" />

        <!-- Tool Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/toolTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Tool Name"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:fontFamily="@font/inter_bold" />

                <TextView
                    android:id="@+id/comingSoonLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/rounded_background"
                    android:backgroundTint="@color/accent_color"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="4dp"
                    android:text="Coming Soon"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/background_primary"
                    android:fontFamily="@font/inter_bold"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/toolDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="Tool description"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:fontFamily="@font/inter_regular" />

        </LinearLayout>

        <!-- Arrow Icon -->
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="16dp"
            android:src="@drawable/ic_arrow_forward"
            android:tint="@color/text_secondary" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
