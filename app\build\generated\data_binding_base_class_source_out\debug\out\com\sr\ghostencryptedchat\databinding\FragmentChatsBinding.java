// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import com.sr.ghostencryptedchat.view.ThreeDBackgroundView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentChatsBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final ThreeDBackgroundView backgroundView;

  @NonNull
  public final LinearLayout chatLayout;

  @NonNull
  public final RecyclerView chatListRecycler;

  @NonNull
  public final TextView chatTitle;

  private FragmentChatsBinding(@NonNull FrameLayout rootView,
      @NonNull ThreeDBackgroundView backgroundView, @NonNull LinearLayout chatLayout,
      @NonNull RecyclerView chatListRecycler, @NonNull TextView chatTitle) {
    this.rootView = rootView;
    this.backgroundView = backgroundView;
    this.chatLayout = chatLayout;
    this.chatListRecycler = chatListRecycler;
    this.chatTitle = chatTitle;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentChatsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentChatsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_chats, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentChatsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backgroundView;
      ThreeDBackgroundView backgroundView = ViewBindings.findChildViewById(rootView, id);
      if (backgroundView == null) {
        break missingId;
      }

      id = R.id.chatLayout;
      LinearLayout chatLayout = ViewBindings.findChildViewById(rootView, id);
      if (chatLayout == null) {
        break missingId;
      }

      id = R.id.chatListRecycler;
      RecyclerView chatListRecycler = ViewBindings.findChildViewById(rootView, id);
      if (chatListRecycler == null) {
        break missingId;
      }

      id = R.id.chatTitle;
      TextView chatTitle = ViewBindings.findChildViewById(rootView, id);
      if (chatTitle == null) {
        break missingId;
      }

      return new FragmentChatsBinding((FrameLayout) rootView, backgroundView, chatLayout,
          chatListRecycler, chatTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
