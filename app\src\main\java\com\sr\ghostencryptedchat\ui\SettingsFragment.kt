package com.sr.ghostencryptedchat.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.sr.ghostencryptedchat.R
import com.sr.ghostencryptedchat.util.BackupUtil
import com.sr.ghostencryptedchat.util.KeyStoreUtil
import kotlinx.coroutines.launch

class SettingsFragment : Fragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Display username
        updateDisplayedUsername(view)

        // Set up backup button
        view.findViewById<View>(R.id.backupButton).setOnClickListener {
            lifecycleScope.launch {
                try {
                    val seedPhrase = BackupUtil.createBackup(requireContext())
                    showSeedPhraseDialog(seedPhrase)
                } catch (e: Exception) {
                    Toast.makeText(context, "Failed to create backup: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }

        // Set up restore button
        view.findViewById<View>(R.id.restoreButton).setOnClickListener {
            showRestoreDialog()
        }
    }

    override fun onResume() {
        super.onResume()
        updateDisplayedUsername(requireView())
    }

    private fun updateDisplayedUsername(view: View) {
        view.findViewById<android.widget.TextView>(R.id.usernameText).text = 
            "Username: ${KeyStoreUtil.getOrCreateUsername(requireContext())}"
    }

    private fun showSeedPhraseDialog(seedPhrase: String) {
        AlertDialog.Builder(requireContext())
            .setTitle("Backup Created")
            .setMessage("Your seed phrase is:\n\n$seedPhrase\n\nWrite this down and keep it safe! You'll need it to recover your account.")
            .setPositiveButton("OK", null)
            .show()
    }

    private fun showRestoreDialog() {
        val input = android.widget.EditText(requireContext())
        input.hint = "Enter your seed phrase"
        
        AlertDialog.Builder(requireContext())
            .setTitle("Restore Account")
            .setView(input)
            .setPositiveButton("Restore") { _, _ ->
                val seedPhrase = input.text.toString().trim()
                if (seedPhrase.isNotEmpty()) {
                    lifecycleScope.launch {
                        try {
                            if (BackupUtil.restoreFromSeedPhrase(requireContext(), seedPhrase)) {
                                Toast.makeText(context, "Account restored successfully!", Toast.LENGTH_SHORT).show()
                                updateDisplayedUsername(requireView())
                            } else {
                                Toast.makeText(context, "Invalid seed phrase", Toast.LENGTH_SHORT).show()
                            }
                        } catch (e: Exception) {
                            Toast.makeText(context, "Failed to restore account: ${e.message}", Toast.LENGTH_LONG).show()
                        }
                    }
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
}
