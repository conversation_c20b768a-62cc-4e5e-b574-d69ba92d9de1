// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class ItemDmMessageBinding implements ViewBinding {
  @NonNull
  private final TextView rootView;

  @NonNull
  public final TextView messageText;

  private ItemDmMessageBinding(@NonNull TextView rootView, @NonNull TextView messageText) {
    this.rootView = rootView;
    this.messageText = messageText;
  }

  @Override
  @NonNull
  public TextView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDmMessageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDmMessageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_dm_message, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDmMessageBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    TextView messageText = (TextView) rootView;

    return new ItemDmMessageBinding((TextView) rootView, messageText);
  }
}
