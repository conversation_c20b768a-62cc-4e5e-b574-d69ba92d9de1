package com.sr.ghostencryptedchat.ui

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.fragment.app.Fragment
import com.sr.ghostencryptedchat.databinding.FragmentGhostSecretBinding

class GhostSecretFragment : Fragment() {

    private var _binding: FragmentGhostSecretBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentGhostSecretBinding.inflate(inflater, container, false)
        return binding.root
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupWebView()
        setupBackButton()
    }

    private fun setupWebView() {
        binding.ghostSecretWebView.apply {
            webViewClient = WebViewClient()

            settings.apply {
                javaScriptEnabled = true
                domStorageEnabled = true
                allowFileAccess = false
                allowContentAccess = false
                setSupportZoom(true)
                builtInZoomControls = true
                displayZoomControls = false
                useWideViewPort = true
                loadWithOverviewMode = true
            }

            // Load Ghost Secret website
            loadUrl("https://secret.ghostbyjohnmcafee.com/")
        }
    }

    private fun setupBackButton() {
        binding.backButton.setOnClickListener {
            if (canGoBack()) {
                goBack()
            } else {
                parentFragmentManager.popBackStack()
            }
        }
    }

    fun canGoBack(): Boolean {
        return binding.ghostSecretWebView.canGoBack()
    }

    fun goBack() {
        binding.ghostSecretWebView.goBack()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
