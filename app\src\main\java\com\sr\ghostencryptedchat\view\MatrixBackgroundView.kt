package com.sr.ghostencryptedchat.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.Log
import android.view.View
import java.util.Random
import kotlin.math.min

class MatrixBackgroundView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val random = Random()
    private val paint = Paint().apply {
        color = Color.parseColor("#aeff00") // Matrix green
        alpha = 150 // Much more visible
        textSize = 36f // Larger text
        typeface = Typeface.MONOSPACE
        isAntiAlias = true
    }
    
    // Characters to use (mix of letters, numbers, and symbols)
    private val chars = "01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン"
    
    // Columns of falling characters
    private val columns = mutableListOf<Column>()
    
    // Number of columns to display
    private var columnCount = 20
    
    // Last update time for animation
    private var lastUpdateTime = 0L
    
    // Animation speed (ms)
    private val animationSpeed = 100L
    
    init {
        Log.d("MatrixView", "Initializing MatrixBackgroundView")
        // Set background color
        setBackgroundColor(Color.parseColor("#121212"))
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        
        Log.d("MatrixView", "onSizeChanged: w=$w, h=$h")
        
        // Calculate number of columns based on width
        columnCount = (w / 30).coerceAtLeast(10)
        
        // Initialize columns
        columns.clear()
        for (i in 0 until columnCount) {
            columns.add(Column(
                x = i * (w / columnCount.toFloat()) + (w / columnCount.toFloat() / 3),
                y = -random.nextInt(h),
                speed = random.nextInt(5) + 10,
                startY = -random.nextInt(h),
                length = random.nextInt(10) + 5
            ))
        }
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        // Fill the background with a dark color
        canvas.drawColor(Color.parseColor("#121212"))
        
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastUpdateTime > animationSpeed) {
            lastUpdateTime = currentTime
            updateColumns()
        }
        
        // Draw each column
        for (column in columns) {
            for (i in 0 until min(column.chars.size, column.length)) {
                val y = column.y - (i * 30)
                if (y > 0 && y < height) {
                    // Make characters more visible
                    paint.alpha = (150 * (1.0f - i.toFloat() / column.length)).toInt().coerceAtLeast(30)
                    canvas.drawText(column.chars[i].toString(), column.x, y.toFloat(), paint)
                }
            }
        }
        
        // Request next frame
        invalidate()
    }
    
    private fun updateColumns() {
        for (column in columns) {
            column.y += column.speed
            
            // If column is off screen, reset it
            if (column.y - (column.length * 30) > height) {
                column.y = -random.nextInt(30)
                column.speed = random.nextInt(5) + 1
                column.length = random.nextInt(10) + 5
            }
            
            // Update characters
            while (column.chars.size < column.length) {
                column.chars.add(chars[random.nextInt(chars.length)])
            }
            
            // Occasionally change the first character
            if (random.nextInt(5) == 0) {
                if (column.chars.isNotEmpty()) {
                    column.chars[0] = chars[random.nextInt(chars.length)]
                }
            }
        }
    }
    
    // Represents a column of falling characters
    private inner class Column(
        val x: Float,
        var y: Int,
        var speed: Int,
        val startY: Int,
        var length: Int,
        val chars: MutableList<Char> = mutableListOf()
    ) {
        init {
            for (i in 0 until length) {
                chars.add(<EMAIL>[random.nextInt(<EMAIL>)])
            }
        }
    }
}


