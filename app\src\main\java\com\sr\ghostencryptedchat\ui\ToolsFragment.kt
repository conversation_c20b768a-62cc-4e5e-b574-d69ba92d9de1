package com.sr.ghostencryptedchat.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.sr.ghostencryptedchat.databinding.FragmentToolsBinding

class ToolsFragment : Fragment() {

    private var _binding: FragmentToolsBinding? = null
    private val binding get() = _binding!!

    private lateinit var toolsAdapter: ToolsAdapter
    private val toolsList = mutableListOf<ToolItem>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentToolsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupToolsList()
        setupRecyclerView()
    }

    private fun setupToolsList() {
        toolsList.clear()
        
        // Add placeholder tools - we'll implement these later
        toolsList.add(
            ToolItem(
                title = "Password Generator",
                description = "Generate secure passwords",
                icon = "🔐",
                isAvailable = false // Will be implemented later
            )
        )
        
        toolsList.add(
            ToolItem(
                title = "QR Code Generator",
                description = "Create QR codes for text or URLs",
                icon = "📱",
                isAvailable = false // Will be implemented later
            )
        )
        
        toolsList.add(
            ToolItem(
                title = "Text Encryptor",
                description = "Encrypt and decrypt text messages",
                icon = "🔒",
                isAvailable = false // Will be implemented later
            )
        )
        
        toolsList.add(
            ToolItem(
                title = "Hash Generator",
                description = "Generate MD5, SHA256 hashes",
                icon = "🔗",
                isAvailable = false // Will be implemented later
            )
        )
        
        toolsList.add(
            ToolItem(
                title = "Base64 Encoder",
                description = "Encode and decode Base64 strings",
                icon = "📝",
                isAvailable = false // Will be implemented later
            )
        )
        
        toolsList.add(
            ToolItem(
                title = "Network Scanner",
                description = "Scan local network for devices",
                icon = "🌐",
                isAvailable = false // Will be implemented later
            )
        )
    }

    private fun setupRecyclerView() {
        toolsAdapter = ToolsAdapter(toolsList) { toolItem ->
            if (toolItem.isAvailable) {
                // Handle tool click - will implement specific tools later
                handleToolClick(toolItem)
            } else {
                // Show coming soon message
                // Could show a toast or dialog
            }
        }
        
        binding.toolsRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = toolsAdapter
        }
    }

    private fun handleToolClick(toolItem: ToolItem) {
        // This will be implemented when we add specific tools
        when (toolItem.title) {
            "Password Generator" -> {
                // Navigate to password generator
            }
            "QR Code Generator" -> {
                // Navigate to QR code generator
            }
            "Text Encryptor" -> {
                // Navigate to text encryptor
            }
            "Hash Generator" -> {
                // Navigate to hash generator
            }
            "Base64 Encoder" -> {
                // Navigate to Base64 encoder
            }
            "Network Scanner" -> {
                // Navigate to network scanner
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

data class ToolItem(
    val title: String,
    val description: String,
    val icon: String,
    val isAvailable: Boolean = false
)
