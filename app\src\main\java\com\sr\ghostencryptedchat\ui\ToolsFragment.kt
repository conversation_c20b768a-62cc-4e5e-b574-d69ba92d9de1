package com.sr.ghostencryptedchat.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.sr.ghostencryptedchat.R
import com.sr.ghostencryptedchat.databinding.FragmentToolsBinding

class ToolsFragment : Fragment() {

    private var _binding: FragmentToolsBinding? = null
    private val binding get() = _binding!!

    private lateinit var toolsAdapter: ToolsAdapter
    private val toolsList = mutableListOf<ToolItem>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentToolsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupToolsList()
        setupRecyclerView()
    }

    private fun setupToolsList() {
        toolsList.clear()

        // Implemented tools
        toolsList.add(
            ToolItem(
                title = "Password Generator",
                description = "Generate secure passwords with custom options",
                icon = "🔐",
                isAvailable = true
            )
        )

        toolsList.add(
            ToolItem(
                title = "Network Scanner",
                description = "Scan local network for connected devices",
                icon = "🌐",
                isAvailable = true
            )
        )

        toolsList.add(
            ToolItem(
                title = "Ghost Secret",
                description = "Create self-destructing encrypted messages",
                icon = "👻",
                isAvailable = true
            )
        )

        toolsList.add(
            ToolItem(
                title = "Port Scanner",
                description = "Check open ports on network hosts",
                icon = "🔌",
                isAvailable = true
            )
        )

        toolsList.add(
            ToolItem(
                title = "Device Info",
                description = "Comprehensive device and system information",
                icon = "📱",
                isAvailable = true
            )
        )

        toolsList.add(
            ToolItem(
                title = "WiFi Analyzer",
                description = "Analyze WiFi networks and signal strength",
                icon = "📡",
                isAvailable = true
            )
        )
    }

    private fun setupRecyclerView() {
        toolsAdapter = ToolsAdapter(toolsList) { toolItem ->
            if (toolItem.isAvailable) {
                // Handle tool click - will implement specific tools later
                handleToolClick(toolItem)
            } else {
                // Show coming soon message
                // Could show a toast or dialog
            }
        }
        
        binding.toolsRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = toolsAdapter
        }
    }

    private fun handleToolClick(toolItem: ToolItem) {
        when (toolItem.title) {
            "Password Generator" -> {
                parentFragmentManager.beginTransaction()
                    .hide(this@ToolsFragment)
                    .add(R.id.fragmentContainer, PasswordGeneratorFragment())
                    .addToBackStack("password_generator")
                    .commit()
            }
            "Network Scanner" -> {
                parentFragmentManager.beginTransaction()
                    .hide(this@ToolsFragment)
                    .add(R.id.fragmentContainer, NetworkScannerFragment())
                    .addToBackStack("network_scanner")
                    .commit()
            }
            "Ghost Secret" -> {
                parentFragmentManager.beginTransaction()
                    .hide(this@ToolsFragment)
                    .add(R.id.fragmentContainer, GhostSecretFragment())
                    .addToBackStack("ghost_secret")
                    .commit()
            }
            "Port Scanner" -> {
                parentFragmentManager.beginTransaction()
                    .hide(this@ToolsFragment)
                    .add(R.id.fragmentContainer, PortScannerFragment())
                    .addToBackStack("port_scanner")
                    .commit()
            }
            "Device Info" -> {
                parentFragmentManager.beginTransaction()
                    .hide(this@ToolsFragment)
                    .add(R.id.fragmentContainer, DeviceInfoFragment())
                    .addToBackStack("device_info")
                    .commit()
            }
            "WiFi Analyzer" -> {
                parentFragmentManager.beginTransaction()
                    .hide(this@ToolsFragment)
                    .add(R.id.fragmentContainer, WiFiAnalyzerFragment())
                    .addToBackStack("wifi_analyzer")
                    .commit()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

data class ToolItem(
    val title: String,
    val description: String,
    val icon: String,
    val isAvailable: Boolean = false
)
