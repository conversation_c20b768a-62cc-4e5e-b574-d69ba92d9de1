package com.sr.ghostencryptedchat.ui

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.sr.ghostencryptedchat.databinding.ItemPortScanResultBinding

class PortScanResultAdapter(
    private val results: List<PortScanResult>
) : RecyclerView.Adapter<PortScanResultAdapter.PortResultViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PortResultViewHolder {
        val binding = ItemPortScanResultBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PortResultViewHolder(binding)
    }

    override fun onBindViewHolder(holder: PortResultViewHolder, position: Int) {
        holder.bind(results[position])
    }

    override fun getItemCount(): Int = results.size

    inner class PortResultViewHolder(private val binding: ItemPortScanResultBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(result: PortScanResult) {
            binding.apply {
                portNumber.text = result.port.toString()
                serviceName.text = result.service
                
                if (result.isOpen) {
                    portStatus.text = "OPEN"
                    portStatus.setTextColor(
                        itemView.context.getColor(android.R.color.holo_green_light)
                    )
                    responseTime.text = "${result.responseTime}ms"
                    responseTime.visibility = android.view.View.VISIBLE
                    
                    // Set icon based on service
                    serviceIcon.text = when (result.service.lowercase()) {
                        "http", "https" -> "🌐"
                        "ssh" -> "🔐"
                        "ftp" -> "📁"
                        "smtp", "pop3", "imap", "pop3s", "imaps" -> "📧"
                        "dns" -> "🌍"
                        "mysql", "postgresql", "sql server" -> "🗄️"
                        "rdp" -> "🖥️"
                        "vnc" -> "👁️"
                        "telnet" -> "📟"
                        else -> "🔌"
                    }
                } else {
                    portStatus.text = "CLOSED"
                    portStatus.setTextColor(
                        itemView.context.getColor(android.R.color.holo_red_light)
                    )
                    responseTime.visibility = android.view.View.GONE
                    serviceIcon.text = "❌"
                }
            }
        }
    }
}
