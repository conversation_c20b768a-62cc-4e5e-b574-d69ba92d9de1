package com.sr.ghostencryptedchat

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.firebase.firestore.FirebaseFirestore
import com.sr.ghostencryptedchat.util.BackupUtil
import com.sr.ghostencryptedchat.util.KeyStoreUtil
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

class UsernameSetupActivity : AppCompatActivity() {
    private val db = FirebaseFirestore.getInstance()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_username_setup)

        // Set up new account button
        findViewById<android.widget.Button>(R.id.newAccountButton).setOnClickListener {
            findViewById<android.widget.LinearLayout>(R.id.newAccountLayout).visibility = android.view.View.VISIBLE
            findViewById<android.widget.LinearLayout>(R.id.recoverAccountLayout).visibility = android.view.View.GONE
        }

        // Set up recover account button
        findViewById<android.widget.Button>(R.id.recoverAccountButton).setOnClickListener {
            findViewById<android.widget.LinearLayout>(R.id.newAccountLayout).visibility = android.view.View.GONE
            findViewById<android.widget.LinearLayout>(R.id.recoverAccountLayout).visibility = android.view.View.VISIBLE
        }

        // Set up register button
        findViewById<android.widget.Button>(R.id.register_button).setOnClickListener {
            val username = findViewById<android.widget.EditText>(R.id.username_input).text.toString().trim()
            if (username.isNotEmpty()) {
                registerUsername(username)
            } else {
                Toast.makeText(this, "Please enter a username", Toast.LENGTH_SHORT).show()
            }
        }

        // Set up recover button
        findViewById<android.widget.Button>(R.id.recover_button).setOnClickListener {
            val seedPhrase = findViewById<android.widget.EditText>(R.id.seed_phrase_input).text.toString().trim()
            if (seedPhrase.isNotEmpty()) {
                lifecycleScope.launch {
                    try {
                        if (BackupUtil.restoreFromSeedPhrase(this@UsernameSetupActivity, seedPhrase)) {
                            Toast.makeText(this@UsernameSetupActivity, "Account restored successfully!", Toast.LENGTH_SHORT).show()
                            startMainActivity()
                        } else {
                            Toast.makeText(this@UsernameSetupActivity, "Invalid seed phrase", Toast.LENGTH_SHORT).show()
                        }
                    } catch (e: Exception) {
                        Toast.makeText(this@UsernameSetupActivity, "Failed to restore account: ${e.message}", Toast.LENGTH_LONG).show()
                    }
                }
            } else {
                Toast.makeText(this, "Please enter your seed phrase", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun registerUsername(username: String) {
        lifecycleScope.launch {
            try {
                // Check if username is available
                val querySnapshot = db.collection("users")
                    .whereEqualTo("username", username)
                    .get()
                    .await()

                if (querySnapshot.isEmpty) {
                    // Username is available, register it
                    KeyStoreUtil.setUsername(this@UsernameSetupActivity, username)
                    
                    // Create user document in Firestore
                    val userData = hashMapOf(
                        "username" to username,
                        "timestamp" to System.currentTimeMillis()
                    )
                    
                    db.collection("users")
                        .document(username)
                        .set(userData)
                        .await()

                    Toast.makeText(this@UsernameSetupActivity, "Username registered successfully!", Toast.LENGTH_SHORT).show()
                    startMainActivity()
                } else {
                    Toast.makeText(this@UsernameSetupActivity, "Username already taken", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                Toast.makeText(this@UsernameSetupActivity, "Failed to register username: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun startMainActivity() {
        val intent = Intent(this, EncryptedChatActivity::class.java)
        startActivity(intent)
        finish()
    }
}
