package com.sr.ghostencryptedchat.util

import android.app.Activity
import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.util.Log
import android.view.Gravity
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.FileProvider
import com.google.gson.Gson
import com.sr.ghostencryptedchat.model.UpdateInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.net.HttpURLConnection
import java.net.URL

class UpdateManager(private val context: Context) {
    private val TAG = "UpdateManager"
    private val UPDATE_URL = "https://chat.myghost.org/version.json"
    private var downloadId: Long = -1
    private var updateOverlay: TextView? = null
    
    // Get current version code from PackageInfo instead of BuildConfig
    private val currentVersionCode: Int by lazy {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                context.packageManager.getPackageInfo(context.packageName, PackageManager.PackageInfoFlags.of(0)).longVersionCode.toInt()
            } else {
                @Suppress("DEPRECATION")
                context.packageManager.getPackageInfo(context.packageName, 0).versionCode
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting version code", e)
            1 // Default to 1 if we can't get the version
        }
    }

    suspend fun checkForUpdate() {
        try {
            Log.d(TAG, "Checking for updates...")
            val updateInfo = fetchUpdateInfo()
            Log.d(TAG, "Current version: $currentVersionCode")
            Log.d(TAG, "Server version: ${updateInfo?.versionCode}")
            
            if (updateInfo != null && updateInfo.versionCode > currentVersionCode) {
                Log.d(TAG, "New version available: ${updateInfo.versionName}")
                showUpdateOverlay("Update found (${updateInfo.versionName})... downloading")
                downloadAndInstallUpdate(updateInfo)
            } else {
                Log.d(TAG, "App is up to date")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking for updates", e)
            // Continue with current version if update check fails
        }
    }

    private fun showUpdateOverlay(message: String) {
        if (context is Activity) {
            context.runOnUiThread {
                try {
                    // Create a TextView for the overlay
                    val overlay = TextView(context).apply {
                        text = message
                        setTextColor(Color.WHITE)
                        setBackgroundColor(Color.parseColor("#88000000")) // Semi-transparent black
                        gravity = Gravity.CENTER
                        textSize = 16f
                        setPadding(20, 20, 20, 20)
                    }

                    // Create layout parameters
                    val params = FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    ).apply {
                        gravity = Gravity.TOP
                    }

                    // Add to the root view
                    val rootView = (context as Activity).window.decorView.findViewById<ViewGroup>(android.R.id.content)
                    rootView.addView(overlay, params)
                    
                    // Save reference to remove later
                    updateOverlay = overlay
                } catch (e: Exception) {
                    Log.e(TAG, "Error showing update overlay", e)
                }
            }
        } else {
            // Fallback to toast if not an activity
            Toast.makeText(context, message, Toast.LENGTH_LONG).show()
        }
    }

    private fun updateOverlayMessage(message: String) {
        if (context is Activity && updateOverlay != null) {
            context.runOnUiThread {
                updateOverlay?.text = message
            }
        }
    }

    private fun removeUpdateOverlay() {
        if (context is Activity && updateOverlay != null) {
            context.runOnUiThread {
                try {
                    val rootView = (context as Activity).window.decorView.findViewById<ViewGroup>(android.R.id.content)
                    rootView.removeView(updateOverlay)
                    updateOverlay = null
                } catch (e: Exception) {
                    Log.e(TAG, "Error removing update overlay", e)
                }
            }
        }
    }

    private suspend fun fetchUpdateInfo(): UpdateInfo? = withContext(Dispatchers.IO) {
        try {
            val url = URL(UPDATE_URL)
            val connection = url.openConnection() as HttpURLConnection
            connection.connectTimeout = 5000
            connection.readTimeout = 5000
            
            val inputStream = connection.inputStream
            val jsonString = inputStream.bufferedReader().use { it.readText() }
            inputStream.close()
            
            return@withContext Gson().fromJson(jsonString, UpdateInfo::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching update info", e)
            return@withContext null
        }
    }

    private fun downloadAndInstallUpdate(updateInfo: UpdateInfo) {
        try {
            val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            
            val apkFile = File(
                context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS),
                "GhostChat-update.apk"
            )
            
            // Delete existing file if it exists
            if (apkFile.exists()) {
                apkFile.delete()
            }
            
            // Create download request
            val request = DownloadManager.Request(Uri.parse(updateInfo.apkUrl))
                .setTitle("Ghost Chat Update")
                .setDescription("Downloading version ${updateInfo.versionName}")
                .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE)
                .setDestinationUri(Uri.fromFile(apkFile))
                .setAllowedOverMetered(true)
                .setAllowedOverRoaming(true)
            
            // Start download
            downloadId = downloadManager.enqueue(request)
            
            // Register receiver to install APK when download completes
            val onComplete = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
                    if (id == downloadId) {
                        context.unregisterReceiver(this)
                        
                        // Check if download was successful
                        val query = DownloadManager.Query().setFilterById(downloadId)
                        val cursor = downloadManager.query(query)
                        
                        if (cursor.moveToFirst()) {
                            val statusIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS)
                            if (statusIndex != -1 && cursor.getInt(statusIndex) == DownloadManager.STATUS_SUCCESSFUL) {
                                updateOverlayMessage("Download complete. Installing...")
                                installApk(apkFile)
                            } else {
                                updateOverlayMessage("Download failed. Continuing with current version.")
                                removeUpdateOverlay()
                            }
                        }
                        cursor.close()
                    }
                }
            }
            
            context.registerReceiver(
                onComplete,
                IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE)
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading update", e)
            updateOverlayMessage("Update failed. Continuing with current version.")
            removeUpdateOverlay()
            Toast.makeText(context, "Update failed, continuing with current version", Toast.LENGTH_SHORT).show()
        }
    }

    private fun installApk(apkFile: File) {
        try {
            val intent = Intent(Intent.ACTION_VIEW)
            val uri: Uri
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                uri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.provider",
                    apkFile
                )
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            } else {
                uri = Uri.fromFile(apkFile)
            }
            
            intent.setDataAndType(uri, "application/vnd.android.package-archive")
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
            
            // Remove overlay after starting installation
            removeUpdateOverlay()
        } catch (e: Exception) {
            Log.e(TAG, "Error installing APK", e)
            updateOverlayMessage("Installation failed. Continuing with current version.")
            removeUpdateOverlay()
            Toast.makeText(context, "Installation failed, continuing with current version", Toast.LENGTH_SHORT).show()
        }
    }
}




