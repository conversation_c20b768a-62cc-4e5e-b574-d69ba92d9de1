<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="ic_launcher_background">#000000</color>
    <color name="neon_green">#aeff00</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">Ghost Chat</string>
    <style name="Base.Theme.GhostEncryptedChat" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="BottomNavigationView.ActiveIndicator" parent="Widget.Material3.BottomNavigationView.ActiveIndicator">
        <item name="android:color">@color/nav_item_background_selector</item>
    </style>
    <style name="BottomNavigationViewStyle" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="android:minHeight">56dp</item>
        <item name="itemIconSize">24dp</item>
        <item name="itemTextAppearanceActive">@style/TextAppearance.BottomNavigationView.Active</item>
        <item name="itemTextAppearanceInactive">@style/TextAppearance.BottomNavigationView.Inactive</item>
        <item name="itemActiveIndicatorStyle">@style/BottomNavigationView.ActiveIndicator</item>
    </style>
    <style name="TextAppearance.BottomNavigationView.Active" parent="TextAppearance.AppCompat.Small">
        <item name="android:textSize">12sp</item>
    </style>
    <style name="TextAppearance.BottomNavigationView.Inactive" parent="TextAppearance.AppCompat.Small">
        <item name="android:textSize">12sp</item>
    </style>
    <style name="Theme.GhostEncryptedChat" parent="Base.Theme.GhostEncryptedChat"/>
</resources>