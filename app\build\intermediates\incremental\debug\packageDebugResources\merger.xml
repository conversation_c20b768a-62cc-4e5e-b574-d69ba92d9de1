<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res"><file name="nav_icon_selector" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\color\nav_icon_selector.xml" qualifiers="" type="color"/><file name="nav_item_background_selector" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\color\nav_item_background_selector.xml" qualifiers="" type="color"/><file name="nav_text_selector" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\color\nav_text_selector.xml" qualifiers="" type="color"/><file name="avatar_circle_bg" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\avatar_circle_bg.xml" qualifiers="" type="drawable"/><file name="bg_badge" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\bg_badge.xml" qualifiers="" type="drawable"/><file name="bg_bubble_me" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\bg_bubble_me.xml" qualifiers="" type="drawable"/><file name="bg_bubble_other" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\bg_bubble_other.xml" qualifiers="" type="drawable"/><file name="bg_chat_item" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\bg_chat_item.xml" qualifiers="" type="drawable"/><file name="bg_dm_message" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\bg_dm_message.xml" qualifiers="" type="drawable"/><file name="bg_input_border" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\bg_input_border.xml" qualifiers="" type="drawable"/><file name="bg_message_me" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\bg_message_me.xml" qualifiers="" type="drawable"/><file name="bg_message_other" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\bg_message_other.xml" qualifiers="" type="drawable"/><file name="circle_bg" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\circle_bg.xml" qualifiers="" type="drawable"/><file name="circle_green" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\circle_green.xml" qualifiers="" type="drawable"/><file name="dm_input_box" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\dm_input_box.xml" qualifiers="" type="drawable"/><file name="ic_add_chat" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_add_chat.xml" qualifiers="" type="drawable"/><file name="ic_add_contact" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_add_contact.xml" qualifiers="" type="drawable"/><file name="ic_add_image" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_add_image.xml" qualifiers="" type="drawable"/><file name="ic_avatar_placeholder" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_avatar_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_chat" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_chat.xml" qualifiers="" type="drawable"/><file name="ic_contacts" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_contacts.xml" qualifiers="" type="drawable"/><file name="ic_globe" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_globe.png" qualifiers="" type="drawable"/><file name="ic_globe_white" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_globe_white.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_person" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_person.png" qualifiers="" type="drawable"/><file name="ic_person_add" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_person_add.png" qualifiers="" type="drawable"/><file name="ic_send" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_send.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_wallet" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\ic_wallet.xml" qualifiers="" type="drawable"/><file name="message_bubble_me" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\message_bubble_me.xml" qualifiers="" type="drawable"/><file name="message_bubble_other" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\message_bubble_other.xml" qualifiers="" type="drawable"/><file name="neon_border" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\neon_border.xml" qualifiers="" type="drawable"/><file name="neon_input_border" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\neon_input_border.xml" qualifiers="" type="drawable"/><file name="rounded_bg" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\rounded_bg.xml" qualifiers="" type="drawable"/><file name="search_box_background" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\search_box_background.xml" qualifiers="" type="drawable"/><file name="splash_logo" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\drawable\splash_logo.png" qualifiers="" type="drawable"/><file name="activity_contacts" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\activity_contacts.xml" qualifiers="" type="layout"/><file name="activity_direct_message" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\activity_direct_message.xml" qualifiers="" type="layout"/><file name="activity_dm_chat" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\activity_dm_chat.xml" qualifiers="" type="layout"/><file name="activity_dm_request" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\activity_dm_request.xml" qualifiers="" type="layout"/><file name="activity_dm_requests" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\activity_dm_requests.xml" qualifiers="" type="layout"/><file name="activity_encrypted_chat" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\activity_encrypted_chat.xml" qualifiers="" type="layout"/><file name="activity_group_chat" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\activity_group_chat.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_settings" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\activity_settings.xml" qualifiers="" type="layout"/><file name="activity_splash" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="activity_username_setup" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\activity_username_setup.xml" qualifiers="" type="layout"/><file name="fragment_chats" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\fragment_chats.xml" qualifiers="" type="layout"/><file name="fragment_contacts" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\fragment_contacts.xml" qualifiers="" type="layout"/><file name="fragment_global_chat" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\fragment_global_chat.xml" qualifiers="" type="layout"/><file name="fragment_settings" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\fragment_settings.xml" qualifiers="" type="layout"/><file name="fragment_wallet" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\fragment_wallet.xml" qualifiers="" type="layout"/><file name="item_chat" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\item_chat.xml" qualifiers="" type="layout"/><file name="item_chat_list" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\item_chat_list.xml" qualifiers="" type="layout"/><file name="item_contact" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\item_contact.xml" qualifiers="" type="layout"/><file name="item_direct_message" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\item_direct_message.xml" qualifiers="" type="layout"/><file name="item_dm_message" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\item_dm_message.xml" qualifiers="" type="layout"/><file name="item_dm_request" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\item_dm_request.xml" qualifiers="" type="layout"/><file name="item_message" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\item_message.xml" qualifiers="" type="layout"/><file name="item_message_me" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\item_message_me.xml" qualifiers="" type="layout"/><file name="item_message_other" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\layout\item_message_other.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="chat_menu" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\menu\chat_menu.xml" qualifiers="" type="menu"/><file name="dm_menu" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\menu\dm_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="background" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\raw\background.mp4" qualifiers="" type="raw"/><file path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="neon_green">#aeff00</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#000000</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Ghost Chat</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\values\styles.xml" qualifiers=""><style name="BottomNavigationViewStyle" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="android:minHeight">56dp</item>
        <item name="itemIconSize">24dp</item>
        <item name="itemTextAppearanceActive">@style/TextAppearance.BottomNavigationView.Active</item>
        <item name="itemTextAppearanceInactive">@style/TextAppearance.BottomNavigationView.Inactive</item>
        <item name="itemActiveIndicatorStyle">@style/BottomNavigationView.ActiveIndicator</item>
    </style><style name="TextAppearance.BottomNavigationView.Active" parent="TextAppearance.AppCompat.Small">
        <item name="android:textSize">12sp</item>
    </style><style name="TextAppearance.BottomNavigationView.Inactive" parent="TextAppearance.AppCompat.Small">
        <item name="android:textSize">12sp</item>
    </style><style name="BottomNavigationView.ActiveIndicator" parent="Widget.Material3.BottomNavigationView.ActiveIndicator">
        <item name="android:color">@color/nav_item_background_selector</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.GhostEncryptedChat" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.GhostEncryptedChat" parent="Base.Theme.GhostEncryptedChat"/></file><file path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.GhostEncryptedChat" parent="Theme.Material3.DayNight.NoActionBar">
        
        
        <item name="android:statusBarColor">#000000</item>
        <item name="android:navigationBarColor">#000000</item>
        <item name="android:windowLightStatusBar">false</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\GhostEncryptedChat\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>