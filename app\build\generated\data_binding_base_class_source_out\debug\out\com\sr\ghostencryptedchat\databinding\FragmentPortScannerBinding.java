// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentPortScannerBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final EditText customPortsInput;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView resultsRecyclerView;

  @NonNull
  public final Button scanCommonButton;

  @NonNull
  public final Button scanCustomButton;

  @NonNull
  public final TextView scanStatus;

  @NonNull
  public final EditText targetInput;

  private FragmentPortScannerBinding(@NonNull ScrollView rootView, @NonNull ImageButton backButton,
      @NonNull EditText customPortsInput, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView resultsRecyclerView, @NonNull Button scanCommonButton,
      @NonNull Button scanCustomButton, @NonNull TextView scanStatus,
      @NonNull EditText targetInput) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.customPortsInput = customPortsInput;
    this.progressBar = progressBar;
    this.resultsRecyclerView = resultsRecyclerView;
    this.scanCommonButton = scanCommonButton;
    this.scanCustomButton = scanCustomButton;
    this.scanStatus = scanStatus;
    this.targetInput = targetInput;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentPortScannerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentPortScannerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_port_scanner, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentPortScannerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backButton;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.customPortsInput;
      EditText customPortsInput = ViewBindings.findChildViewById(rootView, id);
      if (customPortsInput == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.resultsRecyclerView;
      RecyclerView resultsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (resultsRecyclerView == null) {
        break missingId;
      }

      id = R.id.scanCommonButton;
      Button scanCommonButton = ViewBindings.findChildViewById(rootView, id);
      if (scanCommonButton == null) {
        break missingId;
      }

      id = R.id.scanCustomButton;
      Button scanCustomButton = ViewBindings.findChildViewById(rootView, id);
      if (scanCustomButton == null) {
        break missingId;
      }

      id = R.id.scanStatus;
      TextView scanStatus = ViewBindings.findChildViewById(rootView, id);
      if (scanStatus == null) {
        break missingId;
      }

      id = R.id.targetInput;
      EditText targetInput = ViewBindings.findChildViewById(rootView, id);
      if (targetInput == null) {
        break missingId;
      }

      return new FragmentPortScannerBinding((ScrollView) rootView, backButton, customPortsInput,
          progressBar, resultsRecyclerView, scanCommonButton, scanCustomButton, scanStatus,
          targetInput);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
