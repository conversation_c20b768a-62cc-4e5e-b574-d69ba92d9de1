// Generated by view binder compiler. Do not edit!
package com.sr.ghostencryptedchat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.sr.ghostencryptedchat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentGlobalChatBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView chatTitle;

  @NonNull
  public final LinearLayout globalChatLayout;

  @NonNull
  public final EditText messageInput;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final ImageButton sendButton;

  private FragmentGlobalChatBinding(@NonNull LinearLayout rootView, @NonNull TextView chatTitle,
      @NonNull LinearLayout globalChatLayout, @NonNull EditText messageInput,
      @NonNull RecyclerView recyclerView, @NonNull ImageButton sendButton) {
    this.rootView = rootView;
    this.chatTitle = chatTitle;
    this.globalChatLayout = globalChatLayout;
    this.messageInput = messageInput;
    this.recyclerView = recyclerView;
    this.sendButton = sendButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentGlobalChatBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentGlobalChatBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_global_chat, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentGlobalChatBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.chatTitle;
      TextView chatTitle = ViewBindings.findChildViewById(rootView, id);
      if (chatTitle == null) {
        break missingId;
      }

      LinearLayout globalChatLayout = (LinearLayout) rootView;

      id = R.id.messageInput;
      EditText messageInput = ViewBindings.findChildViewById(rootView, id);
      if (messageInput == null) {
        break missingId;
      }

      id = R.id.recyclerView;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.sendButton;
      ImageButton sendButton = ViewBindings.findChildViewById(rootView, id);
      if (sendButton == null) {
        break missingId;
      }

      return new FragmentGlobalChatBinding((LinearLayout) rootView, chatTitle, globalChatLayout,
          messageInput, recyclerView, sendButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
