package com.sr.ghostencryptedchat.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.sr.ghostencryptedchat.databinding.ItemMessageMeBinding
import com.sr.ghostencryptedchat.databinding.ItemMessageOtherBinding
import com.sr.ghostencryptedchat.model.ChatMessage
import com.sr.ghostencryptedchat.util.EncryptionUtil

class DMAdapter(
    private val messages: List<ChatMessage>,
    private val currentUser: String
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val VIEW_TYPE_ME = 1
    private val VIEW_TYPE_OTHER = 2

    inner class MeViewHolder(val binding: ItemMessageMeBinding) : RecyclerView.ViewHolder(binding.root)
    inner class OtherViewHolder(val binding: ItemMessageOtherBinding) : RecyclerView.ViewHolder(binding.root)

    override fun getItemViewType(position: Int): Int {
        return if (messages[position].sender == currentUser) VIEW_TYPE_ME else VIEW_TYPE_OTHER
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        Log.d("DMAdapter", "Creating ViewHolder for viewType: $viewType")
        return if (viewType == VIEW_TYPE_ME) {
            val binding = ItemMessageMeBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            MeViewHolder(binding)
        } else {
            val binding = ItemMessageOtherBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            OtherViewHolder(binding)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val message = messages[position]
        Log.d("DMAdapter", "Binding message at position $position: sender=${message.sender}, isCurrentUser=${message.sender == currentUser}")
        
        try {
            val decrypted = EncryptionUtil.decrypt(message.message)
            Log.d("DMAdapter", "Decrypted message: ${decrypted.take(20)}...")

            val formattedTime = android.text.format.DateFormat.format("hh:mm a", message.timestamp)

            if (holder is MeViewHolder) {
                holder.binding.textMessage.text = decrypted
                holder.binding.timestamp.text = formattedTime
                Log.d("DMAdapter", "Set text for MeViewHolder")
            } else if (holder is OtherViewHolder) {
                holder.binding.textMessage.text = decrypted
                holder.binding.timestamp.text = formattedTime
                Log.d("DMAdapter", "Set text for OtherViewHolder")
            }
        } catch (e: Exception) {
            Log.e("DMAdapter", "Error binding message", e)
        }
    }

    override fun getItemCount(): Int {
        Log.d("DMAdapter", "getItemCount: ${messages.size}")
        return messages.size
    }
}
